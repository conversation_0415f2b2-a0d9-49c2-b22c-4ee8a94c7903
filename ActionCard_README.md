# ActionCard 卡片消息组件

ActionCard 是一个双端支持的卡片式消息组件，用于在聊天界面中展示重要信息并引导用户进行相关操作。

## 功能特性

- ✅ 双端支持（React Native + React Web）
- ✅ 支持URL跳转和提问操作
- ✅ 自定义按钮样式和颜色
- ✅ 自定义卡片背景色
- ✅ 文本自动省略
- ✅ 响应式设计

## 数据格式

```typescript
interface ActionCard {
    type: 'actionCard';
    insert: {
        actionCard: {
            button: Button;
            title: string;
            subTitle: string;
            backgroundColor?: string; // HexColor
        }
    }
}

interface Button {
    text: string; // 必填，展示文本
    url?: string; // 跳链
    question?: string; // 提问内容
    action?: 'submitQuestion'; // action > url, 可以用来定义操作，每次新增操作需要开发
    color: string; // color > type，css支持的color，hexColor等
    type: string; // 'primary' | 'normal', primary: 美团黄，normal：白色
}
```

## 使用示例

### 1. URL跳转

```json
{
    "type": "actionCard",
    "insert": {
        "actionCard": {
            "title": "查新店加权",
            "subTitle": "提升新商家曝光量和下单转化",
            "button": {
                "text": "去查询",
                "url": "https://example.com/new-store-weight",
                "color": "#ffdd10",
                "type": "primary"
            }
        }
    }
}
```

### 2. 提问操作

```json
{
    "type": "actionCard",
    "insert": {
        "actionCard": {
            "title": "门店营业状态异常",
            "subTitle": "检测到您的门店营业状态可能存在问题，建议及时处理",
            "button": {
                "text": "如何处理？",
                "action": "submitQuestion",
                "question": "门店营业状态异常应该如何处理？",
                "color": "#ff6a00",
                "type": "primary"
            }
        }
    }
}
```

### 3. 自定义背景色

```json
{
    "type": "actionCard",
    "insert": {
        "actionCard": {
            "title": "优惠活动推荐",
            "subTitle": "根据您的门店情况，为您推荐合适的优惠活动",
            "backgroundColor": "#f6ffed",
            "button": {
                "text": "查看活动",
                "url": "https://example.com/promotions",
                "color": "#52c41a",
                "type": "normal"
            }
        }
    }
}
```

## 实现文件

### React Native (waimai_cd_fe_bee_assistant)

- **组件文件**: `src/components/ActionCard.tsx`
- **类型定义**: `src/types/message.ts` (已添加 ActionCardMessage)
- **渲染逻辑**: `src/components/MessageBox/Answer/AnswerContent/textMessageUtils.tsx`
- **示例文件**: `src/components/ActionCard/ActionCardExample.tsx`

### React Web (waimai_cd_fe_crm_bdservice)

- **组件文件**: `src/pages/knowledge/components/ActionCard/ActionCard.tsx`
- **样式文件**: `src/pages/knowledge/components/ActionCard/ActionCard.scss`
- **类型定义**: `src/pages/knowledge/chat/common/type/message.ts` (已添加 ActionCardMessage)
- **渲染逻辑**: `src/pages/knowledge/chat/common/ui/message/messageItemRender.tsx`
- **测试文件**: `src/pages/knowledge/components/ActionCard/ActionCard.test.tsx`
- **示例文件**: `src/pages/knowledge/components/ActionCard/ActionCardExample.tsx`

## 样式设计

### 卡片样式
- 圆角: 12px
- 内边距: 16px
- 阴影: 轻微阴影效果
- 背景: 默认白色，支持自定义

### 文本样式
- **标题**: 16px, 粗体, 最多2行省略
- **副标题**: 14px, 常规, 最多3行省略

### 按钮样式
- **Primary**: 美团黄 (#ffdd10)
- **Normal**: 白色背景 + 边框
- 圆角: 25px
- 高度: 44px (RN) / 40px (Web移动端)

## 交互逻辑

1. **优先级**: action > url
2. **submitQuestion**: 发送预设问题到聊天
3. **URL跳转**: 打开指定链接
4. **错误处理**: 无操作时显示提示

## 响应式支持

- Web端支持移动端适配
- 文字大小和按钮高度在小屏幕上自动调整

## 测试

Web端提供了完整的单元测试，覆盖：
- 基础渲染
- URL跳转
- 提问操作
- 样式自定义
- 错误处理

运行测试：
```bash
cd waimai_cd_fe_crm_bdservice
npm run test ActionCard.test.tsx
```

## 注意事项

1. `button.text` 为必填字段
2. `action` 优先级高于 `url`
3. `color` 优先级高于 `type`
4. 长文本会自动省略，建议控制文本长度
5. 确保URL的有效性和安全性
