package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 目录数据（挂载节点信息）获取请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/30 10:46
 */
@ThriftStruct
@Data
public class FetchCategoryDataRequestDto extends BeeRequestDto {

    /**
     * 父目录名称
     */
    private String name;

    /**
     * 分页号
     */
    private Integer pageNum;

    /**
     * 入口点
     */
    private String entryPoint;
}
