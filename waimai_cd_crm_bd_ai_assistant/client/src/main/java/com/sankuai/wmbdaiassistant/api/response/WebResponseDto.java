package com.sankuai.wmbdaiassistant.api.response;

import lombok.Data;

/**
 * 响应体
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-21 15:46
 */
@Data
public class WebResponseDto<T> {

    private int code = 0;
    private String msg = null;
    private T data;

    public static <T> WebResponseDto<T> of(T data) {
        WebResponseDto<T> responseDto = new WebResponseDto<>();
        responseDto.setData(data);
        return responseDto;
    }

    public static WebResponseDto<Void> success() {
        return new WebResponseDto<>();
    }

}
