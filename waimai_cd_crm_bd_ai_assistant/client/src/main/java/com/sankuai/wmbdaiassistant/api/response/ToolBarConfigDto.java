package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * 蜜蜂端 工具栏配置信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/14 16:36
 */

@ThriftStruct
@Data
public class ToolBarConfigDto {

    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 暂时弃用
     */
    private Integer subAbilityType;

    /**
     * 枚举值，必填，1：跳转链接，2：发送消息
     */
    private Integer operationType;

    /**
     * 展示内容，必填
     */
    private String content;

    /**
     * 可选，operationType为1时的跳链
     */
    private String url;

    /**
     * 有权限查看的角色
     */
    private String role;

    /**
     * 是否置顶
     */
    private Boolean top;

    /**
     * 图标链接
     */
    private String link;

    /**
     * 支持的平台
     * @see com.sankuai.wmbdaiassistant.domain.enums.PlatformEnum
     */
    private List<String> platform;

    /**
     * 链接打开方式
     * @see com.sankuai.wmbdaiassistant.domain.enums.OpenWayEnum
     */
    private String openWay;


}