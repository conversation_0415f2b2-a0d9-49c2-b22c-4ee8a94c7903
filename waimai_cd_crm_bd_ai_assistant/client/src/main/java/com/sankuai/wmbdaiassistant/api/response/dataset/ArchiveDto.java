package com.sankuai.wmbdaiassistant.api.response.dataset;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 归档目录
 * <AUTHOR>
 * @Date 2025/2/16
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class ArchiveDto {

    /**
     * 归档ID
     */
    private Long id;

    /**
     * 归档名称
     */
    private String content;

    /**
     * 子节点
     */
    private List<ArchiveDto> children;

    /**
     * 是否包含子节点
     */
    private Boolean hasChild;
}
