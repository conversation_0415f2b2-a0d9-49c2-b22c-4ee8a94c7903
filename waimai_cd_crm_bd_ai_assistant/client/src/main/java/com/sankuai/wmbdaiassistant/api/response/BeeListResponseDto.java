package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024/4/25 15:08
 */

@ThriftStruct
public class BeeListResponseDto {
    private Integer code;

    private String msg;

    private List<BeeResponseData> data;


    @ThriftField(1)
    public Integer getCode() {
        return code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(2)
    public String getMsg() {
        return msg;
    }

    @ThriftField
    public void setMsg(String msg) {
        this.msg = msg;
    }

    @ThriftField(3)
    public List<BeeResponseData> getData() {
        return data;
    }

    public static BeeListResponseDto getInstance(){
        return  new BeeListResponseDto();
    }

    public BeeListResponseDto(){

    }

    public BeeListResponseDto(Integer code, String msg, List<BeeResponseData> data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public BeeListResponseDto code(Integer code){
        this.code = code;
        return this;
    }

    public BeeListResponseDto message(String message){
        this.msg = message;
        return this;
    }

    public BeeListResponseDto data(List<BeeResponseData> beeListResponseData){
        this.data = beeListResponseData;
        return this;
    }

    public static BeeListResponseDto build(Integer code, String msg, List<BeeResponseData> req) {
        return new BeeListResponseDto(code, msg, req);
    }

    public static BeeListResponseDto buildSuccessResponse() {
        return build(0, "成功", null);
    }

    public static BeeListResponseDto buildSuccessResponse(List<BeeResponseData> req) {
        return build(0, "成功", req);
    }
}
