package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公告查询响应结果
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnouncementQueryResponseDto extends BeeResponseData {

    /**
     * 公告ID
     */
    private Long announcementId;

    /**
     * 公告内容
     */
    private String content;
}