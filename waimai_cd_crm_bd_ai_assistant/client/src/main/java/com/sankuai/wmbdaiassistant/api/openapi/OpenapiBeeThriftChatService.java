package com.sankuai.wmbdaiassistant.api.openapi;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.OpenapiBeeThriftChatRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * 【开放平台】问答
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-13 13:34
 */
@ThriftService
public interface OpenapiBeeThriftChatService {

    /**
     * 问答
     *
     * @param request 请求
     * @return
     */
    @ThriftMethod
    BeeResponseDto chat(OpenapiBeeThriftChatRequestDto request);

}
