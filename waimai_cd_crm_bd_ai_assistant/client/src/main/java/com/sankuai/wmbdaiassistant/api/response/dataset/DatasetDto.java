package com.sankuai.wmbdaiassistant.api.response.dataset;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2025/2/15
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DatasetDto {

    /**
     * 说明: 主键
     */
    private Long id;

    /**
     * 说明: 知识库
     */
    private String name;

    /**
     * 说明: 描述
     */
    private String desc;

    /**
     * 说明: 更新时间
     */
    private Date updateTime;

    /**
     * 说明: 分片个数
     */
    private Long fragmentSize;
}