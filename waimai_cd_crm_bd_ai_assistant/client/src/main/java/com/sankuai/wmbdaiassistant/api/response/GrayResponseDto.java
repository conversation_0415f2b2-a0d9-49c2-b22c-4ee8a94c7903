package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一灰度逻辑响应
 *
 * <AUTHOR> <yuh<PERSON><PERSON>@meituan.com>
 * @date 2025/4/18 14:51
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GrayResponseDto extends BeeResponseData {

    /**
     * 是否走新版本的交互。true: 新版本交互，false：旧版本交互
     */
    private Boolean interactionGray;

    /**
     * 是否支持上传图片。true：支持，false：不支持
     */
    private Boolean pictureQuestionGray;

}
