package com.sankuai.wmbdaiassistant.api.response;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-19
 */
@ThriftStruct
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationQuestionResponse extends BeeResponseData {

    private List<String> questions;

    @ThriftField(1)
    public List<String> getQuestions() {
        return questions;
    }

    @ThriftField
    public void setQuestions(List<String> questions) {
        this.questions = questions;
    }
}
