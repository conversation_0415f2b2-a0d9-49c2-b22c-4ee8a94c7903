package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.BeeRequestDto;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseDto;

/**
 * <AUTHOR>
 * @date 2023-12-05
 */
@ThriftService
public interface SignRecordThriftService {

    @ThriftMethod
    BeeResponseDto getInstructions(BeeRequestDto beeRequestDto);

    @ThriftMethod
    BeeResponseDto existSignRecord(BeeRequestDto beeRequestDto);

    @ThriftMethod
    BeeResponseDto signInstructions(BeeRequestDto beeRequestDto);
}
