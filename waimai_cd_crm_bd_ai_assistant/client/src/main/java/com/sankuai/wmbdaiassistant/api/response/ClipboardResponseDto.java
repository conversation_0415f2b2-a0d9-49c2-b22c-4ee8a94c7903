package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 剪贴板响应
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-12-13 17:49
 */
@Data
@ThriftStruct
public class ClipboardResponseDto extends BeeResponseData {

    /**
     * 剪贴板内容
     */
    @ThriftField(1)
    private String content;

    /**
     * 提示语
     */
    @ThriftField(2)
    private String toast;

    /**
     * 触发时机
     */
    @ThriftField(3)
    private String when;
}
