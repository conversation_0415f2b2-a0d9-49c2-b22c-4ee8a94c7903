package com.sankuai.wmbdaiassistant.api.response.dataset;

import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Wiki格式评估返回
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/26 上午10:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WikiFormatEvaluationResponse {

    /**
     * 学城HTML内容
     */
    private String wiki;

    /**
     * 建议信息
     */
    private List<WikiFormatIssueDto> infos;

    /**
     * 评测时间
     */
    private Date evaluationTime;
    /**
     * 是否结束
     */
    private boolean finish;
}
