package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.Builder;
import lombok.Data;

/**
 * 用户信息查询结果
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/15 16:22
 */
@ThriftStruct
@Data
@Builder
public class UserInfoResponseDto extends BeeResponseData {

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 业务ID
     */
    private Integer bizId;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 用户mis号
     */
    private String misId;

    /**
     * 用户姓名
     */
    private String name;
}
