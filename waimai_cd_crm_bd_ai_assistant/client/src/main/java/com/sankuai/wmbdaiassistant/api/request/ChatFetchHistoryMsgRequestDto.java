package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/15
 **/
@ThriftStruct
public class ChatFetchHistoryMsgRequestDto extends BeeRequestDto {

    private Long minMsgId;

    /**
     * 兼容字段，目前V3全量
     */
    private String version;

    @ThriftField(2)
    public Long getMinMsgId() {
        return minMsgId;
    }

    public void setMinMsgId(Long minMsgId) {
        this.minMsgId = minMsgId;
    }

    @ThriftField(3)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
