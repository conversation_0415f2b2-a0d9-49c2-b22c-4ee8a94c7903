package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 蜜蜂端 工具栏配置查询响应结果
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/10/14 14:26
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolBarConfigQueryResponseDto extends BeeResponseData {

    /**
     * 工具栏配置列表
     */
    List<ToolBarConfigDto> options;
}
