package com.sankuai.wmbdaiassistant.api.response.dataset;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseData;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分片召回数据
 *
 * <AUTHOR> <<EMAIL>>
 * @date 4/14/25 2:40 PM
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentRecallRespData extends BeeResponseData {

    /**
     * 查询到的ES MAP结果
     */
    private Map<String,Boolean> permissionMap;
}
