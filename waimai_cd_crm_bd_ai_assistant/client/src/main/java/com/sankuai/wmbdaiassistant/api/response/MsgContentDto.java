package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/14
 **/
@ThriftStruct
public class MsgContentDto extends BeeResponseData {

    /**
     * 问题消息ID
     */
    @ThriftField(1)
    private String questionMsgId;
    /**
     * 回答消息ID
     */
    private String msgId;
    /**
     * 类型  1 QUESTION 用户问题  2 ANSWER 回答
     */
    private Integer type;
    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 回答状态
     */
    private Integer status;

    /**
     * 是否涉敏 true涉敏，false不涉敏
     */
    private Boolean sensitive;

    /**
     * 消息类型(1.普通纯文本，2.选项型)
     */
    private Integer msgType;
    /**
     * 当前纯文本消息(消息类型=普通纯文本才有)
     */
    private String currentContent;

    /**
     * 前序所有消息内容(消息类型=普通纯文本才有)
     */
    private String previousContent;

    /**
     * 前置内容(消息类型=选项型才有)
     */
    private String prefixTextContent;

    /**
     * 后置内容(消息类型=选项型才有)
     */
    private String postTextContent;

    /**
     * 选项(消息类型=选项型才有)
     */
    private List<SelectionItemDto> selectionItems;

    /**
     * 图片链接
     */
    private List<String> imageList;
    /**
     * 反馈类型，只返回点赞和点踩，没有则不返回
     */
    private Integer feedbackType;

    /**
     * 子能力类型
     */
    private Integer subAbilityType;
    /**
     * 是否有其他回答
     */
    private Boolean hasNext;
    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 服务期时间
     */
    private Long respTime;

    /**
     * 标签
     */
    private List<String> tags;

    @ThriftField(1)
    public String getQuestionMsgId() {
        return questionMsgId;
    }

    public void setQuestionMsgId(String questionMsgId) {
        this.questionMsgId = questionMsgId;
    }

    @ThriftField(2)
    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @ThriftField(3)
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @ThriftField(4)
    public Integer getAbilityType() {
        return abilityType;
    }

    public void setAbilityType(Integer abilityType) {
        this.abilityType = abilityType;
    }

    @ThriftField(5)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @ThriftField(6)
    public Boolean getSensitive() {
        return sensitive;
    }

    public void setSensitive(Boolean sensitive) {
        this.sensitive = sensitive;
    }

    @ThriftField(7)
    public Integer getMsgType() {
        return msgType;
    }

    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
    }

    @ThriftField(8)
    public String getCurrentContent() {
        return currentContent;
    }

    public void setCurrentContent(String currentContent) {
        this.currentContent = currentContent;
    }

    @ThriftField(9)
    public String getPreviousContent() {
        return previousContent;
    }

    public void setPreviousContent(String previousContent) {
        this.previousContent = previousContent;
    }

    @ThriftField(10)
    public String getPrefixTextContent() {
        return prefixTextContent;
    }

    public void setPrefixTextContent(String prefixTextContent) {
        this.prefixTextContent = prefixTextContent;
    }

    @ThriftField(11)
    public String getPostTextContent() {
        return postTextContent;
    }

    public void setPostTextContent(String postTextContent) {
        this.postTextContent = postTextContent;
    }

    @ThriftField(12)
    public List<SelectionItemDto> getSelectionItems() {
        return selectionItems;
    }

    public void setSelectionItems(List<SelectionItemDto> selectionItems) {
        this.selectionItems = selectionItems;
    }

    @ThriftField(13)
    public List<String> getImageList() {
        return imageList;
    }

    public void setImageList(List<String> imageList) {
        this.imageList = imageList;
    }

    @ThriftField(14)
    public Integer getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(Integer feedbackType) {
        this.feedbackType = feedbackType;
    }

    @ThriftField(15)
    public Integer getSubAbilityType() {
        return subAbilityType;
    }

    public void setSubAbilityType(Integer subAbilityType) {
        this.subAbilityType = subAbilityType;
    }

    @ThriftField(16)
    public Boolean getHasNext() {
        return hasNext;
    }

    public void setHasNext(Boolean hasNext) {
        this.hasNext = hasNext;
    }

    @ThriftField(17)
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    @ThriftField(18)
    public Long getRespTime() {
        return respTime;
    }

    public void setRespTime(Long respTime) {
        this.respTime = respTime;
    }

    @ThriftField(19)
    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }
}
