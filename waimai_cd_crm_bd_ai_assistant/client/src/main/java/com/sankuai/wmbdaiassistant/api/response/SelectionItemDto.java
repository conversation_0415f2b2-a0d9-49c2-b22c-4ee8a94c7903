package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/14
 **/
@ThriftStruct
public class SelectionItemDto {
    /**
     * 能力类型
     */
    private Integer abilityType;
    /**
     * 子能力类型(缺省)
     */
    private Integer subAbilityType;
    /**
     * 选项名称
     */
    private String content;
    /**
     * 跳转链接
     */
    private String url;
    /**
     * 用户点击选项之后的操作类型：1.url跳转，2.继续提问
     */
    private Integer operationType;

    @ThriftField(1)
    public Integer getAbilityType() {
        return abilityType;
    }

    public void setAbilityType(Integer abilityType) {
        this.abilityType = abilityType;
    }

    @ThriftField(2)
    public Integer getSubAbilityType() {
        return subAbilityType;
    }

    public void setSubAbilityType(Integer subAbilityType) {
        this.subAbilityType = subAbilityType;
    }

    @ThriftField(3)
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(4)
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @ThriftField(5)
    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }
}
