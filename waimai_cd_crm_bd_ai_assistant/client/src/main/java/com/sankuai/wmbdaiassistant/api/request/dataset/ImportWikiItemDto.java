package com.sankuai.wmbdaiassistant.api.request.dataset;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * Wiki 导入Item
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/2/17 下午2:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportWikiItemDto {

    /**
     * wiki ID
     */
    private Long wikiId;

    /**
     * wiki标题
     */
    private String title;

    /**
     * 是否自动更新
     */
    private boolean autoUpdate;

    /**
     * 是否需要子文档
     */
    private boolean needSubWiki;

    /**
     * 是否需要引用文档
     */
    private boolean needReferWiki;

    /**
     * 类型，wiki:父文档，sub_wiki:子文档，refer_wiki:引用文档
     */
    private String type;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 是否同步wiki权限
     */
    private boolean syncWikiAuth;

    /**
     * 参数是否有效
     * @return
     */
    public boolean isValid() {
        return wikiId != null
            && StringUtils.isNotBlank(title)
            && datasetId != null
            && StringUtils.isNotBlank(type);
    }
}
