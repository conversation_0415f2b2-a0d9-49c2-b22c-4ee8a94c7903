package com.sankuai.wmbdaiassistant.api.response.dataset;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 知识库wiki信息
 *
 * <AUTHOR>
 * @date 2025-02-17 15:55
 */
@Data
public class DatasetWikiInfoDto {

    /**
     * ES 主键,创建规则为 {wikiId}-{batchId}
     */
    private String id;

    /**
     * Wiki ID
     */
    private Long wikiId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 文档链接
     */
    private String url;

    /**
     * 修改人mis
     */
    private String modifyMis;

    /**
     * 是否是引用文档
     */
    private Boolean isReferWiki;

    /**
     * 是否有格式问题
     */
    private Boolean hasFormatProblem;

    /**
     * 标签列表
     *
     */
    private List<String> tags;

    /**
     * 说明: 更新时间
     */
    private Date updateTime;

    /**
     * 授权方式，知识库权限：dataset，学城权限：wiki
     */
    private String authType;

}
