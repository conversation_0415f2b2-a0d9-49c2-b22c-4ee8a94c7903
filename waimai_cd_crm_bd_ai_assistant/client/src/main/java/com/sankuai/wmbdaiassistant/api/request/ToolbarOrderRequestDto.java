package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * 工具栏顺序调整请求
 *
 * <AUTHOR> <yuh<PERSON><PERSON>@meituan.com>
 * @date 2025-04-17 15:52
 */
@Data
@ThriftStruct
public class ToolbarOrderRequestDto extends BeeRequestDto {

    /**
     * 工具栏顺序配置（除金刚位top置顶）
     */
    @ThriftField(2)
    private List<String> toolNameList;

}
