package com.sankuai.wmbdaiassistant.api.openapi;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.RecallRequestDto;
import com.sankuai.wmbdaiassistant.api.response.RecallResponseDto;

/**
 * 【开放平台】分片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-11 17:24
 */
@ThriftService
public interface OpenapiFragmentThriftService {

    /**
     * 召回
     *
     * @param request 请求
     * @return
     */
    @ThriftMethod
    RecallResponseDto recall(RecallRequestDto request);
}
