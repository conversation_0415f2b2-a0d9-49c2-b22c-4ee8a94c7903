package com.sankuai.wmbdaiassistant.api.response;

import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * 【开放平台】会话详情结果
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-13 14:47
 */
@Data
@Builder
public class TTSessionDetailResponseDto {

    /**
     * 翻页参数，下一次查询从哪条聊天消息 Id 开始查询，如果为空则表示没有数据了
     */
    private Long nextId;

    /**
     * 聊天记录列表
     */
    private List<TtMsgDto> messageList;
}
