package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 会话查询请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-05-24 15:12
 */
@Data
public class SessionQueryRequestDto {

    /**
     * mis
     */
    private String mis;

    /**
     * 日期
     */
    private String date;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 标准问ID或者标准问名称
     */
    private String phraseData;

    /**
     * 是否已转TT
     */
    private Boolean submittedTt;

    /**
     * 是否备注过
     */
    private Boolean marked;

    /**
     * 域ID
     */
    private Long domainId;

    /**
     * 分页参数：而号，从1开始
     */
    private Integer pageNum;

    /**
     * 分页参数：每页的条数，默认20
     */
    private Integer pageSize;

}
