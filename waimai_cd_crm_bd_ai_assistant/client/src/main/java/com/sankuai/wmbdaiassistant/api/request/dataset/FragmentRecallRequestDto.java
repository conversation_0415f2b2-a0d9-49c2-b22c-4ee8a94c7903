package com.sankuai.wmbdaiassistant.api.request.dataset;

import com.facebook.swift.codec.ThriftStruct;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述？
 *
 * <AUTHOR> <<EMAIL>>
 * @date 4/14/25 2:44 PM
 */
@ThriftStruct
@Data
@NotNull(message = "请求参数不能为空")
public class FragmentRecallRequestDto {

    /**
     * 用户mis
     */
    @NotBlank(message = "用户mis不能为空")
    private String mis;
    /**
     * 用户问题
     */
    @NotEmpty(message = "分片信息不能为空")
    private List<String> fragmentIdList;
}
