package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 【开放平台】会话请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-18 17:02
 */
@Data
@ThriftStruct
public class OpenapiBeeThriftChatRequestData {

    /**
     * 会话来源
     */
    @ThriftField(1)
    private String app;

    /**
     * 请求ID，响应体中会透传【可选项】
     */
    @ThriftField(2)
    private String requestId;

    /**
     * 会话ID
     */
    @ThriftField(3)
    private Long sessionId;

    /**
     * 提问方式。normal：常规提问；template : 模板提问；
     */
    @ThriftField(4)
    private String type;

    /**
     * 问题，当 type = normal 时，问题必填。
     */
    @ThriftField(5)
    private String question;

    /**
     * 模板ID，当 type = template 时，模板ID必填
     */
    @ThriftField(6)
    private Long templateId;

    /**
     * 模板参数，格式为 json。当 type = template 时，如果有模板参数，则需要填，如果没有则无须填
     */
    @ThriftField(7)
    private String templateParams;

    /**
     * 有两个取值：true 和 false。当开启 debug 模式后，会返回额外信息。
     */
    @ThriftField(8)
    private Boolean debug;

}
