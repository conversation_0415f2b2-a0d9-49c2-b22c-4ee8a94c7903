package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/15
 **/
@ThriftStruct
public class ChatFetchHistoryMsgResponseDto extends BeeResponseData {

    private Long minMsgId;

    private List<MsgContentDto> msgItems;


    @ThriftField(1)
    public Long getMinMsgId() {
        return minMsgId;
    }

    public void setMinMsgId(Long minMsgId) {
        this.minMsgId = minMsgId;
    }

    @ThriftField(2)
    public List<MsgContentDto> getMsgItems() {
        return msgItems;
    }

    public void setMsgItems(List<MsgContentDto> msgItems) {
        this.msgItems = msgItems;
    }
}
