package com.sankuai.wmbdaiassistant.api.enums;

import java.util.List;
import java.util.Objects;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2023-12-04
 */
@ThriftEnum
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum WmBdAiAssistantAbilityTypeEnum {
    GENERAL(1, "文本输入", false, "通用类型"),
    JUMP(2, "快捷跳转", true, "快捷跳转类型"),
    SERVICE_SCORE(3, "服务分查询", true, "服务分类型");

    private Integer id;
    private String text;
    private Boolean show;
    private String description;

    WmBdAiAssistantAbilityTypeEnum(Integer id, String text, Boolean show, String description) {
        this.id = id;
        this.text = text;
        this.show = show;
        this.description = description;
    }

    @ThriftEnumValue
    public Integer getId() {
        return id;
    }

    public String getText() {
        return text;
    }

    public Boolean getShow() {
        return show;
    }

    public String getDescription() {
        return description;
    }

    public static List<WmBdAiAssistantAbilityTypeEnum> getAllAbilityType() {
        return Lists.newArrayList(WmBdAiAssistantAbilityTypeEnum.values());
    }

    public static WmBdAiAssistantAbilityTypeEnum getAbilityType(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        for (WmBdAiAssistantAbilityTypeEnum type : WmBdAiAssistantAbilityTypeEnum.values()) {
            if (type.getId().equals(id)) {
                return type;
            }
        }
        return null;
    }

}
