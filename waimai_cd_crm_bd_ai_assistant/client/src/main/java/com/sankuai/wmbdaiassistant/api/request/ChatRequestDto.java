package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 交互请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-06 10:46
 */
@Data
public class ChatRequestDto {

    /**
     * 会话来源
     */
    private String app;

    /**
     * 请求ID，响应体中会透传【可选项】
     */
    private String requestId;

    /**
     * mis
     */
    private String mis;

    /**
     * 会话ID
     */
    private Long sessionId;

    /**
     * 提问方式。normal：常规提问；template : 模板提问；
     */
    private String type;

    /**
     * 问题，当 type = normal 时，问题必填。
     */
    private String question;

    /**
     * 模板ID，当 type = template 时，模板ID必填
     */
    private Long templateId;

    /**
     * 模板参数，格式为 json。当 type = template 时，如果有模板参数，则需要填，如果没有则无须填
     */
    private String templateParams;

    /**
     * 有两个取值：true 和 false。当开启 debug 模式后，会返回额外信息。
     */
    private Boolean debug;
}
