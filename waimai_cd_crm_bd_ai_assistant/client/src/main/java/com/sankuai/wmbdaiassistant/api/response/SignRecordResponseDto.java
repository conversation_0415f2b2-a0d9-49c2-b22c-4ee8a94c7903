package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-05
 */
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SignRecordResponseDto extends BeeResponseData {
    private String instructions;
    private Boolean sign;

    @ThriftField(1)
    public String getInstructions() {
        return instructions;
    }

    @ThriftField
    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    @ThriftField(2)
    public Boolean getSign() {
        return sign;
    }

    @ThriftField
    public void setSign(Boolean sign) {
        this.sign = sign;
    }
}
