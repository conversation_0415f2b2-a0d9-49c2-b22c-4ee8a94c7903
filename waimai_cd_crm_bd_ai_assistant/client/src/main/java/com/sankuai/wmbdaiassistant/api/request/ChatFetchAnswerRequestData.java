package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/21
 **/
@ThriftStruct
public class ChatFetchAnswerRequestData {
    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 子能力类型
     */
    private Integer subAbilityType;

    /**
     * 回答消息
     */
    private String msgId;
    /**
     * 问题消息id
     */
    private String questionMsgId;

    /**
     * 页码(请求下一组选项时必填)
     */
    private Integer pageNum;

    /**
     * 兼容字段，目前V3全量
     */
    private String version;

    @ThriftField(1)
    public Integer getAbilityType() {
        return abilityType;
    }

    public void setAbilityType(Integer abilityType) {
        this.abilityType = abilityType;
    }

    @ThriftField(2)
    public Integer getSubAbilityType() {
        return subAbilityType;
    }

    public void setSubAbilityType(Integer subAbilityType) {
        this.subAbilityType = subAbilityType;
    }

    @ThriftField(3)
    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @ThriftField(4)
    public String getQuestionMsgId() {
        return questionMsgId;
    }

    public void setQuestionMsgId(String questionMsgId) {
        this.questionMsgId = questionMsgId;
    }

    @ThriftField(5)
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    @ThriftField(6)
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
