package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2024/4/17 20:02
 */

@ThriftStruct
public class TriggerSceneResponseDto extends BeeResponseData {
    /**
     * 消息ID
     */
    private String id;

    /**
     * 前置内容
     */
    private String prefixTextContent;

    /**
     * 当前内容
     */
    private String currentContent;

    /**
     * 后置内容
     */
    private String postTextContent;

    /**
     * 类型  1 QUESTION 用户问题  2 ANSWER 回答
     */
    private Integer type;

    /**
     * 消息类型(1.普通纯文本，2.选项型)
     */
    private Integer msgType;

    /**
     * 选项(消息类型=选项型才有)
     */
    private List<SelectionItemDto> selectionItems;

    @ThriftField(1)
    public String getId() {
        return id;
    }

    @ThriftField
    public void setId(String id) {
        this.id = id;
    }

    @ThriftField(2)
    public String getPrefixTextContent() {
        return prefixTextContent;
    }

    @ThriftField
    public void setPrefixTextContent(String prefixTextContent) {
        this.prefixTextContent = prefixTextContent;
    }

    @ThriftField(3)
    public String getCurrentContent() {
        return currentContent;
    }

    @ThriftField
    public void setCurrentContent(String currentContent) {
        this.currentContent = currentContent;
    }

    @ThriftField(4)
    public String getPostTextContent() {
        return postTextContent;
    }

    @ThriftField
    public void setPostTextContent(String postTextContent) {
        this.postTextContent = postTextContent;
    }

    @ThriftField(5)
    public Integer getType() {
        return type;
    }

    @ThriftField
    public void setType(Integer type) {
        this.type = type;
    }

    @ThriftField(6)
    public Integer getMsgType() {
        return msgType;
    }

    @ThriftField
    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
    }

    @ThriftField(7)
    public List<SelectionItemDto> getSelectionItems() {
        return selectionItems;
    }

    @ThriftField
    public void setSelectionItems(List<SelectionItemDto> selectionItems) {
        this.selectionItems = selectionItems;
    }
}
