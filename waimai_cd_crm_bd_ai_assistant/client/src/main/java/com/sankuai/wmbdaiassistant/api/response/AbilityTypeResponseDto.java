package com.sankuai.wmbdaiassistant.api.response;

import java.util.List;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wmbdaiassistant.api.enums.WmBdAiAssistantAbilityTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-04
 */

@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AbilityTypeResponseDto extends BeeResponseData {
    private List<WmBdAiAssistantAbilityTypeEnum> abilityTypes;


    @ThriftField(1)
    public List<WmBdAiAssistantAbilityTypeEnum> getAbilityTypes() {
        return abilityTypes;
    }


    @ThriftField
    public void setAbilityTypes(List<WmBdAiAssistantAbilityTypeEnum> abilityTypes) {
        this.abilityTypes = abilityTypes;
    }
}
