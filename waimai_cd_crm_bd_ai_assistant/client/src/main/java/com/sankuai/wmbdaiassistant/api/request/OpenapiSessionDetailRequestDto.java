package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 开发平台获取会话详情
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/20 13:50
 */
@Data
public class OpenapiSessionDetailRequestDto {

    /**
     * 前端拼接的关联字段，参考格式：{msgId}_{uid}_{date:YYYY-MM-DD HH:mm:ss}_{sessionId1,sessionId2,sessionIdxx...}
     */
    private String associatedField;
    /**
     * 翻页参数，从哪条聊天消息 Id 开始查询，如果为空则从第一条开始
     */
    private Long sinceId;
    /**
     * 翻页参数，查询多少条数据，默认 10 条
     */
    private Integer size;
}
