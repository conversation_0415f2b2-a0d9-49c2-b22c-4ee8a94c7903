package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * input description here
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PoiQueryResponseDto extends BeeResponseData{
	private List<PoiQueryDto> poiList;
	private Integer total;
}
