package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 热门问题信息
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/4/18 11:15
 */

@ThriftStruct
@Data
public class HotQuestionDto {

    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 子能力类型
     */
    private Integer subAbilityType;

    /**
     * 名称
     */
    private String content;

    /**
     * 用户点击选项之后的操作类型：1.url跳转，2.继续提问
     */
    private Integer operationType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 图标链接
     */
    private String link;

    /**
     * 是否高亮
     */
    private Boolean isNew;
}
