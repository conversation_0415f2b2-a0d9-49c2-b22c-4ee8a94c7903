package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 标准问增加的请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 11:18
 */
@Data
public class StandardPhraseAddRequestDto {

    /**
     * 域Id
     */
    private Long domainId;

    /**
     * 问题
     */
    private String question;

    /**
     * 回复
     */
    private String answer;

    /**
     * TT 链接
     */
    private String ttUrl;

    /**
     * 类型
     */
    private String type;

    public FaqAddRequestDto toFaqRequestDto() {
        FaqAddRequestDto faqAddRequestDto = new FaqAddRequestDto();
        faqAddRequestDto.setDomainId(domainId);
        faqAddRequestDto.setQuestion(question);
        faqAddRequestDto.setAnswer(answer);
        faqAddRequestDto.setTtUrl(ttUrl);

        return faqAddRequestDto;
    }
}
