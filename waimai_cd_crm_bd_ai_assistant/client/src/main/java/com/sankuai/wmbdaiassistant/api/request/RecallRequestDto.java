package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * 召回请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-11 17:39
 */
@Data
@ThriftStruct
public class RecallRequestDto {

    /**
     * 请求标识
     */
    @ThriftField(1)
    private String app;

    /**
     * 用户ID
     */
    @ThriftField(2)
    private Long uid;

    /**
     * 用户mis
     */
    @ThriftField(3)
    private String mis;

    /**
     * 查询
     */
    @ThriftField(4)
    private String query;

    /**
     * 知识库ID列表
     */
    @ThriftField(5)
    private List<Long> datasetIds;

    /**
     * 知识库code列表（与 datasetIds 字段互斥）
     */
    @ThriftField(6)
    private List<String> datasetCodes;

    /**
     * 召回的分片个数（默认返回5条）
     */
    @ThriftField(7)
    private Integer size;
}
