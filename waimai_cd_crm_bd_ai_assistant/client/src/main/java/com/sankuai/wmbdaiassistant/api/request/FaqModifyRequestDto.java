package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * FAQ 修改
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 13:26
 */
@Data
public class FaqModifyRequestDto {

    private Long id;
    private Long domainId;
    private String question;
    private String answer;
    private String ttUrl;

    public FaqModifyRequestDto build(Long id, Long DomainId, String question, String answer, String ttUrl) {
        FaqModifyRequestDto faqModifyRequestDto = new FaqModifyRequestDto();
        faqModifyRequestDto.setDomainId(DomainId);
        faqModifyRequestDto.setId(id);
        faqModifyRequestDto.setQuestion(question);
        faqModifyRequestDto.setAnswer(answer);
        faqModifyRequestDto.setTtUrl(ttUrl);
        return faqModifyRequestDto;
    }
}
