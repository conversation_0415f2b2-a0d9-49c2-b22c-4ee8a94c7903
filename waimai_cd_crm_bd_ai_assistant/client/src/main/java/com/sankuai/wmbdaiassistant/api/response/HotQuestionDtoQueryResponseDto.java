package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 热门问题查询响应结果
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/4/18 11:15
 */
@ThriftStruct
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotQuestionDtoQueryResponseDto extends BeeResponseData {

    /**
     * 热门问题列表
     */
    List<HotQuestionDto> hotQuestions;

}
