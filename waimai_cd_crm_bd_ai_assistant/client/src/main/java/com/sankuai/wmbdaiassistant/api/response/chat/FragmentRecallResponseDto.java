package com.sankuai.wmbdaiassistant.api.response.chat;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分片召回响应
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/4/10 下午8:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FragmentRecallResponseDto {

    /**
     * 与用户问题关联的分片ID列表，默认200条
     */
    List<String> fragmentIdList;
}
