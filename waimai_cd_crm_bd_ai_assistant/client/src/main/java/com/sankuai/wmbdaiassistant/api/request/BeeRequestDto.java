package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wmbdaiassistant.api.dto.BeeContext;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/6 14:44
 */
@ThriftStruct
public class BeeRequestDto {

    private BeeContext beeContext;


    @ThriftField(1)
    public BeeContext getBeeContext() {
        return beeContext;
    }

    public void setBeeContext(BeeContext beeContext) {
        this.beeContext = beeContext;
    }


}
