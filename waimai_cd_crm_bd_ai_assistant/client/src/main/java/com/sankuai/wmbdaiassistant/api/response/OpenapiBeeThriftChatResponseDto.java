package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.Map;

/**
 * 【开放平台】会话响应体
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-13 14:47
 */
@Data
@ThriftStruct
public class OpenapiBeeThriftChatResponseDto extends BeeResponseData {

    /**
     * 透传的业务ID
     */
    @ThriftField(1)
    private String requestId;

    /**
     * 内容
     */
    @ThriftField(2)
    private String content;

    /**
     * 额外信息
     */
    @ThriftField(3)
    private Map<String, Object> extra;

}
