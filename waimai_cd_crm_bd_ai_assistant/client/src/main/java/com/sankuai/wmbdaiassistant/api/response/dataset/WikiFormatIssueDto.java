package com.sankuai.wmbdaiassistant.api.response.dataset;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Wiki格式建议
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/26 上午10:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WikiFormatIssueDto {
    /**
     * 问题标题,例如 检测到合并单元格的表格
     */
    private String title;
    /**
     * 建议信息,例如 有合并单元格的表格不利于解析，建议您不使用合并单元格
     */
    private String message;
    /**
     * WIKI组件路径,例如 1-2 代表第一个组件内第二个组件
     */
    private String path;
    /**
     * 组件ID,例如 7811e37dbe6641dd9a610779f70658fd
     */
    private String nodeId;
    /**
     * 问题等级 0-最高 9-最低
     */
    private int level;
}
