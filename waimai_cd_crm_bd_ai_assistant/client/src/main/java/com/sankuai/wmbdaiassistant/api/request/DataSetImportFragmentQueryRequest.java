package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 导入过程中的分片查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 14:07
 */
@Data
public class DataSetImportFragmentQueryRequest {

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 分页参数，页号，默认1
     */
    private Integer pageNum;

    /**
     * 分页参数，每页条数，默认20
     */
    private Integer pageSize;

    /**
     * wiki ID
     */
    private Long wikiId;

}
