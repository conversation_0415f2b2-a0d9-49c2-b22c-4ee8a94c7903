package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

import java.util.List;

/**
 * 召回响应
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-11 17:43
 */
@Data
@ThriftStruct
public class RecallResponseDto {

    /**
     * 响应码，0成功，其他失败
     */
    @ThriftField(1)
    private Integer code = 0;

    /**
     * 响应消息
     */
    @ThriftField(2)
    private String msg = "success";

    /**
     * 片段列表
     */
    @ThriftField(3)
    private List<RecallFragmentDto> fragmentList;

}
