package com.sankuai.wmbdaiassistant.api.openapi;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.HasPermissionRequestDto;
import com.sankuai.wmbdaiassistant.api.response.ResponseDto;

import java.util.Map;

/**
 * 【开放平台】权限校验
 */
@ThriftService
public interface OpenapiAuthThriftService {

    /**
     * 权限校验
     *
     * @param request 请求
     * @return 权限校验结果
     */
    @ThriftMethod
    ResponseDto<Map<String, Boolean>> hasPermission(HasPermissionRequestDto request);
}