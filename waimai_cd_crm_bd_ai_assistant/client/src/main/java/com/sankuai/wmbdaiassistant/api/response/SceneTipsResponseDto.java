package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @description: 场景提示语
 * @author: maningning03
 * @create: 2025/4/22
 **/
@Data
@Builder
@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class SceneTipsResponseDto extends BeeResponseData {

    /**
     * 场景提示语 key为场景,value为提示语
     * @see com.sankuai.wmbdaiassistant.domain.enums.SceneTipTypeEnum
     */

    private Map<String, SceneTipsDto> sceneTipsMap;

}
