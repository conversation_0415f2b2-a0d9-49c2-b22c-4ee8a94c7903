package com.sankuai.wmbdaiassistant.api.response.dataset;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库卡片信息
 *
 * <AUTHOR> <yuh<PERSON><PERSON>@meituan.com>
 * @date 2025/3/27 下午17:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DatasetCardStatisticsResponse {

    /**
     * wiki总数
     */
    private Long wikiCount;

    /**
     * 上周wiki被调用总数
     */
    private Long wikiRecallCount;

    /**
     * 上周wiki更新数量
     */
    private Long wikiUpdateCount;

}
