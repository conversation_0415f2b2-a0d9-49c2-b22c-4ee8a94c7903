package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-04
 */
@ThriftStruct
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GraySwitchResponseDto extends BeeResponseData {

    private Boolean gray;

    @ThriftField(1)
    public Boolean getGray() {
        return gray;
    }

    @ThriftField
    public void setGray(Boolean gray) {
        this.gray = gray;
    }
}
