package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 响应体基类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-14 16:50
 */
@Data
@ThriftStruct
public class ResponseDto<T> {

    /**
     * 响应码，0成功，其他失败
     */
    @ThriftField(1)
    private Integer code = 0;

    /**
     * 响应消息
     */
    @ThriftField(2)
    private String msg = "success";

    /**
     * 数据
     */
    @ThriftField(3)
    private T data;
}
