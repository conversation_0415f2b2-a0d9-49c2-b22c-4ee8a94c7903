package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * <AUTHOR>
 * @description 其他场景触发参数
 * @create 2024/4/17 19:54
 */

@ThriftStruct
public class TriggerSceneRequestDto extends BeeRequestDto {
    /**
     * 场景id
     */
    private Long id;

    /**
     * 不同场景下传入的参数
     */
    private String params;

    /**
     * 业务id
     */
    private Integer bizId;

    @ThriftField(2)
    public Long getId() {
        return id;
    }

    @ThriftField
    public void setId(Long id) {
        this.id = id;
    }

    @ThriftField(3)
    public String getParams() {
        return params;
    }

    @ThriftField
    public void setParams(String params) {
        this.params = params;
    }

    @ThriftField(4)
    public Integer getBizId() {
        return bizId;
    }

    @ThriftField
    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }
}
