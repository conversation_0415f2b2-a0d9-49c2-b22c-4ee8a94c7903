package com.sankuai.wmbdaiassistant.api.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分片查询返回
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 14:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSetImportFragmentQueryResponse {

    /**
     * 分页参数，页号，默认1
     */
    private Integer pageNum;

    /**
     * 分页参数，每页条数，默认20
     */
    private Integer pageSize;

    /**
     * 总数
     */
    private Long total;

    /**
     * 分片列表
     */
    private List<String> fragmentList;

}
