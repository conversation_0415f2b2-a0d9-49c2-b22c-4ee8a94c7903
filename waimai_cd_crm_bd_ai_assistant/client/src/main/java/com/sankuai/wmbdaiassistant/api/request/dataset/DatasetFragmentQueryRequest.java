package com.sankuai.wmbdaiassistant.api.request.dataset;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识库wiki分片查询请求
 *
 * <AUTHOR>
 * @date 2025-02-18 14:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatasetFragmentQueryRequest {

    /**
     * 分页参数，页号，默认为1
     */
    private Integer pageNum;

    /**
     * 分页参数，每页的个数，默认为20
     */
    private Integer pageSize;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * wiki ID
     */
    private Long wikiId;

    /**
     * 所选的目录名称树列表
     *
     */
    private List<String> categories;

}
