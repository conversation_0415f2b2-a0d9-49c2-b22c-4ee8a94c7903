package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 打点请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-28 16:38
 */
@Data
@ThriftStruct
public class TraceLogRequestDto extends BeeRequestDto {

    /**
     * 入口点类型
     */
    @ThriftField(2)
    private String entryPoint;

    /**
     * 事件类型
     */
    @ThriftField(3)
    private String eventType;

    /**
     * 内容
     */
    @ThriftField(4)
    private String content;

}
