package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * input description here
 *
 * <AUTHOR>
 * @date 2024/8/23
 */
@ThriftStruct
@Data
public class PoiQueryDto{
    /**
     * 商家ID
     */
    @ThriftField(1)
    private Long id;

    /**
     * 商家名称
     */
    @ThriftField(2)
    private String name;

    /**
     * 商家图片链接
     */
    @ThriftField(3)
    private String url;

    /**
     * 类型，默认 poi
     */
    @ThriftField(4)
    private String type;
}
