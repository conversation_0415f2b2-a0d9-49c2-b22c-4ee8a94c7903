package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/5
 **/
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatSessionResponseDto extends MsgContentDto {

    /**
     * 会话id
     */
    private Long sessionId;

    /**
     * 是否有聊天记录标识
     */
    private boolean existChatRecord;

    /**
     * 是否展示刷新提示语
     */
    private boolean showRefreshMsg;

    @ThriftField(20)
    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(21)
    public boolean isExistChatRecord() {
        return existChatRecord;
    }

    public void setExistChatRecord(boolean existChatRecord) {
        this.existChatRecord = existChatRecord;
    }

    @ThriftField(22)
    public boolean isShowRefreshMsg() {
        return showRefreshMsg;
    }

    public void setShowRefreshMsg(boolean showRefreshMsg) {
        this.showRefreshMsg = showRefreshMsg;
    }


}
