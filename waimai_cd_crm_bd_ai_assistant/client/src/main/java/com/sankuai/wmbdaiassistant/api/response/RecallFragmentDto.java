package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 召回的分片（markdown格式）
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-04-14 10:58
 */
@Data
@ThriftStruct
public class RecallFragmentDto {

    /**
     * 分片内容
     */
    @ThriftField(1)
    private String content;

    /**
     * 分片得分
     */
    @ThriftField(2)
    private Double score;

}
