package com.sankuai.wmbdaiassistant.api.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 会话时问题类型
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-06-06 13:38
 */
@Getter
public enum ChatQuestionTypeEnum {

    NORMAL("normal", "正常提问"),
    TEMPLATE("template", "模板提问"),
    ;

    private String code;
    private String desc;

    ChatQuestionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChatQuestionTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ChatQuestionTypeEnum chatQuestionTypeEnum : values()) {
            if (StringUtils.equals(chatQuestionTypeEnum.getCode(), code)) {
                return chatQuestionTypeEnum;
            }
        }
        return null;
    }
}
