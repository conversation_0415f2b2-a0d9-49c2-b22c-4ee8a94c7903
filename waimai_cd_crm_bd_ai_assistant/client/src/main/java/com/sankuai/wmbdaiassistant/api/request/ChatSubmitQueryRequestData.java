package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/21
 **/
@ThriftStruct
public class ChatSubmitQueryRequestData {
    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 子能力类型(文本输入可以不填)
     */
    private Integer subAbilityType;

    /**
     * 问题文本内容
     */
    private String content;

    private Integer bizId;

    /** 请求的入口点类型 （旧版本）*/
    private Integer entryPointType;

    /**
     * 请求的入口点类型（新版本）
     */
    private String entryPoint;

    @ThriftField(1)
    public Integer getAbilityType() {
        return abilityType;
    }

    public void setAbilityType(Integer abilityType) {
        this.abilityType = abilityType;
    }

    @ThriftField(2)
    public Integer getSubAbilityType() {
        return subAbilityType;
    }

    public void setSubAbilityType(Integer subAbilityType) {
        this.subAbilityType = subAbilityType;
    }

    @ThriftField(3)
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(4)
    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    @ThriftField(5)
    public Integer getEntryPointType() {
        return entryPointType;
    }

    public void setEntryPointType(Integer entryPointType) {
        this.entryPointType = entryPointType;
    }

    @ThriftField(6)
    public String getEntryPoint() {
        return entryPoint;
    }

    public void setEntryPoint(String entryPoint) {
        this.entryPoint = entryPoint;
    }
}
