package com.sankuai.wmbdaiassistant.api.request.chat;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分片召回请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/4/10 下午8:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FragmentRecallRequestDto {

    /**
     * 用户mis
     */
    @NotBlank(message = "用户mis不能为空")
    private String mis;
    /**
     * 用户问题
     */
    @NotBlank(message = "用户问题不能为空")
    private String query;
    /**
     * 分片大小
     */
    @NotBlank(message = "需要召回的分片条数不能为空")
    private Integer size;
}
