package com.sankuai.wmbdaiassistant.api.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据导入过程中的wiki查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025-02-17 15:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSetWikiQueryResponse {

    /**
     * 分页参数，默认为1
     */
    private Integer pageNum;

    /**
     * 分页参数，默认为20
     */
    private Integer pageSize;

    /**
     * 总数
     */
    private Long total;

    /**
     * 学城文档列表
     */
    private List<WikiDto> wikiList;
}
