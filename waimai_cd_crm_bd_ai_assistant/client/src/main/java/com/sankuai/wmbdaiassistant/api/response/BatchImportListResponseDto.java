package com.sankuai.wmbdaiassistant.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标准问批量导入的数据分页查询
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-07-27 10:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchImportListResponseDto {

    /**
     * 分页参数，页号
     */
    private Integer pageNum;

    /**
     * 分页参数，每页的个数
     */
    private Integer pageSize;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 进程ID
     */
    private Long processId;

    /**
     * 导入流程的状态
     */
    private String status;

    /**
     * 导入项
     */
    private List<BatchImportListItemDto> list;
}
