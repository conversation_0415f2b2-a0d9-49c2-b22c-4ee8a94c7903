package com.sankuai.wmbdaiassistant.api.request.dataset;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 知识库修改请求
 * <AUTHOR>
 * @Date 2025/2/16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DatasetModifyRequestDto {
    /**
     *   说明: 知识库ID
     */
    private Long datasetId;

    /**
     *   说明: 描述
     */
    private String desc;

    /**
     *   说明: 业务线Code
     */
    private String bizLine;

    /**
     *   说明: 授权的所有组织
     */
    private List<Long> authOrgIdList;

    /**
     *   说明: 授权的所有用户
     */
    private List<Long> authUserList;
}
