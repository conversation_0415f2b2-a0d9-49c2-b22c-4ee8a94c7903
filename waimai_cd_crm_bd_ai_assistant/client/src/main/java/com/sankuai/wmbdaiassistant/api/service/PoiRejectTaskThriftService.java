package com.sankuai.wmbdaiassistant.api.service;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.sankuai.wmbdaiassistant.api.request.phrase.PoiRejectTaskSuggestionQueryDto;
import com.sankuai.wmbdaiassistant.api.response.phrase.PoiRejectTaskSuggestionResponseDto;

/**
 * 商家驳回相关thrift接口
 *
 * <AUTHOR>
 * @date 2025-04-30 10:38
 */
@ThriftService
public interface PoiRejectTaskThriftService {

    /**
     * 查询标准问匹配结果
     *
     * @param request
     * @return
     */
    @ThriftMethod
    PoiRejectTaskSuggestionResponseDto searchPhraseAnswer(PoiRejectTaskSuggestionQueryDto request);

}
