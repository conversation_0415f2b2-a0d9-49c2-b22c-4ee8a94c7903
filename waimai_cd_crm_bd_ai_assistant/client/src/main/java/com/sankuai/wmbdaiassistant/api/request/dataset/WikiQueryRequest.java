package com.sankuai.wmbdaiassistant.api.request.dataset;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识库数据列表查询请求
 *
 * <AUTHOR>
 * @date 2025-02-17 15:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WikiQueryRequest {

    /**
     * 分页参数，页号，默认为1
     */
    private Integer pageNum;

    /**
     * 分页参数，每页的个数，默认为20
     */
    private Integer pageSize;

    /**
     * 文档名称/标题/url
     */
    private String query;

    /**
     * 修改人mis
     */
    private String modifyMis;

    /**
     * 知识库ID
     */
    private Long datasetId;

    /**
     * 所选的目录名称树列表
     *
     */
    private List<String> categories;

    /**
     * 是否有格式问题
     */
    private Boolean hasFormatProblem;

    /**
     * 授权方式，知识库权限：dataset，学城权限：wiki
     */
    private String authType;
}
