package com.sankuai.wmbdaiassistant.api.response;

import lombok.Data;

/**
 * 开放平台-消息体
 * wiki:https://km.sankuai.com/collabpage/2576831257
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024/11/20 13:57
 */
@Data
public class TtMsgDto {

    /**
     * // 消息 Id
     */
    private Long id;

    /**
     * 发送人，传用户的 misId，或者 '智能客服'
     */
    private String sender;
    /**
     * 消息原始内容
     */
    private String sourceContent;
    /**
     * 转换后的格式
     * 
     * 举例：
     * [
     * {
     * "type": "text",
     * "content": "请点击下方【TT工单】联系人工帮你解决问题。\n【"
     * },
     * {
     * "type": "link",
     * "url": "xxxxxxx",
     * "name": "TT工单"
     * },
     * {
     * "type": "text",
     * "content": "】"
     * }
     * ]
     */
    private Object transferContent;

    /**
     * 消息序号：用于标识消息的顺序，不必严格从1、2、3开始，只需能够清晰地体现消息的顺序即可。
     */
    private Long sequence;

    /**
     * 64 位 Long 类型时间戳
     */
    private Long sendTime;
}
