package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/12/22 14:45
 */
@ThriftStruct
public class BeeResponseDto {


    private Integer code;

    private String msg;

    private BeeResponseData data;


    @ThriftField(1)
    public Integer getCode() {
        return code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(2)
    public String getMsg() {
        return msg;
    }

    @ThriftField
    public void setMsg(String msg) {
        this.msg = msg;
    }

    @ThriftField(3)
    public BeeResponseData getData() {
        return data;
    }

    @ThriftField
    public void setData(BeeResponseData data) {
        this.data = data;
    }

    public static BeeResponseDto getInstance(){
        return  new BeeResponseDto();
    }

    public BeeResponseDto(){

    }

    public BeeResponseDto(Integer code, String msg, BeeResponseData data){
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public BeeResponseDto code(Integer code){
        this.code = code;
        return this;
    }

    public BeeResponseDto message(String message){
        this.msg = message;
        return this;
    }

    public BeeResponseDto data(BeeResponseData beeResponseData){
        this.data = beeResponseData;
        return this;
    }

    public static BeeResponseDto build(Integer code, String msg, BeeResponseData req) {
        return new BeeResponseDto(code, msg, req);
    }

    public static BeeResponseDto buildSuccessResponse() {
        return build(0, "成功", null);
    }

    public static BeeResponseDto buildSuccessResponse(BeeResponseData req) {
        return build(0, "成功", req);
    }

}
