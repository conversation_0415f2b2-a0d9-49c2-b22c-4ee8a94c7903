package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * 获取机器人配置
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-11-22 10:24
 */
@Data
@ThriftStruct
public class RobotConfigRequestDto extends BeeRequestDto {

    /**
     * 租户ID
     */
    @ThriftField(2)
    private Long tenantId;

    /**
     * 租户名字
     */
    @ThriftField(3)
    private String tenantName;

    /**
     * 业务ID
     */
    @ThriftField(4)
    private Long bizId;

    /**
     * 业务名称
     */
    @ThriftField(5)
    private String bizName;

    /**
     * 系统名称，枚举值：android，ios
     */
    @ThriftField(6)
    private String sysName;

    /**
     * 系统版本号
     */
    @ThriftField(7)
    private String sysVer;

    /**
     * 请求来源的app名称
     */
    @ThriftField(8)
    private String appName;

    /**
     * 请求来源app版本号
     */
    @ThriftField(9)
    private String appVer;

    /**
     * 请求来源入口
     */
    @ThriftField(10)
    private String source;

}
