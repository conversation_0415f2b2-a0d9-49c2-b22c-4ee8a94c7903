package com.sankuai.wmbdaiassistant.api.response.dataset;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Wiki 内容评价响应结果
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2025/3/19 下午7:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WikiContentEvaluationResponse {

    /**
     * 评测建议内容
     */
    private String content;

    /**
     * 评测是否完成
     */
    private boolean finish;

    /**
     * 评测时间
     */
    private Date evaluationTime;
}
