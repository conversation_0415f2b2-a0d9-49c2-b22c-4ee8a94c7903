package com.sankuai.wmbdaiassistant.api.request;

import lombok.Data;

/**
 * 标准问增加的请求
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 15:32
 */
@Data
public class FaqAddRequestDto {

    /**
     * 域Id
     */
    private Long domainId;

    /**
     * 问题
     */
    private String question;

    /**
     * 回复
     */
    private String answer;

    /**
     * TT 链接
     */
    private String ttUrl;

    public static FaqAddRequestDto build(Long domainId, String question, String answer, String ttUrl) {
        FaqAddRequestDto faqAddRequestDto = new FaqAddRequestDto();
        faqAddRequestDto.setDomainId(domainId);
        faqAddRequestDto.setQuestion(question);
        faqAddRequestDto.setAnswer(answer);
        faqAddRequestDto.setTtUrl(ttUrl);
        return faqAddRequestDto;
    }

}
