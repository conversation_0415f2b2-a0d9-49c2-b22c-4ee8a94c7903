package com.sankuai.wmbdaiassistant.api.response;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * @description:
 * @author: hexiang03
 * @create: 2023/12/8
 **/
@ThriftStruct
public class ChatSubmitQueryResponseDto extends BeeResponseData {

    private String questionMsgId;

    private Long msgId;

    /**
     * 能力类型
     */
    private Integer abilityType;

    /**
     * 子能力类型
     */
    private Integer subAbilityType;

    @ThriftField(1)
    public String getQuestionMsgId() {
        return questionMsgId;
    }

    @ThriftField
    public void setQuestionMsgId(String questionMsgId) {
        this.questionMsgId = questionMsgId;
    }

    @ThriftField(2)
    public Long getMsgId() {
        return msgId;
    }

    @ThriftField
    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }

    @ThriftField(3)
    public Integer getAbilityType() {
        return abilityType;
    }

    @ThriftField
    public void setAbilityType(Integer abilityType) {
        this.abilityType = abilityType;
    }

    @ThriftField(4)
    public Integer getSubAbilityType() {
        return subAbilityType;
    }

    @ThriftField
    public void setSubAbilityType(Integer subAbilityType) {
        this.subAbilityType = subAbilityType;
    }

    public static ChatSubmitQueryResponseDto of(Long questionMsgId, Integer abilityType, Integer subAbilityType) {
        ChatSubmitQueryResponseDto responseDto = new ChatSubmitQueryResponseDto();
        responseDto.setQuestionMsgId(String.valueOf(questionMsgId));
        responseDto.setAbilityType(abilityType);
        responseDto.setSubAbilityType(subAbilityType);
        return responseDto;
    }
}
