package com.sankuai.wmbdaiassistant.api.response.common;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.wmbdaiassistant.api.response.BeeResponseData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 天气问候响应结果
 *
 * <AUTHOR>
 * @date 2024/09/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class WeatherGreetingResponseDto extends BeeResponseData {

    /**
     * 问候语
     */
    private String greeting;

    /**
     * 天气提示
     */
    private String weatherTips;

    /**
     * 天气类型
     */
    private String weatherType;
}