package com.sankuai.wmbdaiassistant.api.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

/**
 * <AUTHOR>
 * @date 2023-12-05
 */
@ThriftStruct
public class FeedBackRequestDto extends BeeRequestDto {
    private Long chatRecordId;
    private Integer type;
    private String feedBackContent;


    @ThriftField(2)
    public Long getChatRecordId() {
        return chatRecordId;
    }

    @ThriftField
    public void setChatRecordId(Long chatRecordId) {
        this.chatRecordId = chatRecordId;
    }

    @ThriftField(3)
    public Integer getType() {
        return type;
    }

    @ThriftField
    public void setType(Integer type) {
        this.type = type;
    }

    @ThriftField(4)
    public String getFeedBackContent() {
        return feedBackContent;
    }

    @ThriftField
    public void setFeedBackContent(String feedBackContent) {
        this.feedBackContent = feedBackContent;
    }
}
