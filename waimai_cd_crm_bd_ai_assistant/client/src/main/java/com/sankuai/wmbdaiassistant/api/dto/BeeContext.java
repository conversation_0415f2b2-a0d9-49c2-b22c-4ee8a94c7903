package com.sankuai.wmbdaiassistant.api.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2021/9/6 10:55
 */
@Data
@ThriftStruct
public class BeeContext {

    @ThriftField(1)
    private Integer uid;

    @ThriftField(2)
    private String name;

    @ThriftField(3)
    private String misId;

    @ThriftField(4)
    private String email;

    @ThriftField(5)
    private String code;

    @ThriftField(6)
    private String tenantId;

    @ThriftField(7)
    private Integer opUid;

    @ThriftField(8)
    private String opMisId;

    @ThriftField(9)
    private String opUname;

    @ThriftField(10)
    private Long sessionId;

    @ThriftField(11)
    private String version;
}
