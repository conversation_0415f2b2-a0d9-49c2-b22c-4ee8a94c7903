package com.sankuai.wmbdaiassistant.api.response;

import lombok.Data;

/**
 * 标准问
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-25 20:07
 */
@Data
public class StandardPhraseDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 域
     */
    private Long domainId;

    /**
     * 问题
     */
    private String question;

    /**
     * 类型
     */
    private String type;

    /**
     * 更新时间
     */
    private String utime;

    /**
     * 修改人名称
     */
    private String modifierName;

    /**
     * 修改人mis
     */
    private String modifierMis;

    /**
     * 答案内容
     */
    private String answer;

    /**
     * 触发器ID
     */
    private Long triggerId;

    /**
     * 扩展问个数
     */
    private Long phraseSize;
}
