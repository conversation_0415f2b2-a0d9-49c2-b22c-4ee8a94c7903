package com.sankuai.wmbdaiassistant.api.response.dataset;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Desc 知识库详情
 * <AUTHOR>
 * @Date 2025/2/16
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DatasetDetailResponseDto {

    /**
     * 说明: 知识库
     */
    private String name;

    /**
     * 说明: 描述
     */
    private String desc;

    /**
     * 说明: 业务线
     */
    private String bizLine;

    /**
     * 说明: 授权的所有组织
     */
    private List<DatasetDetailOrgResponseDto> authOrgList;

    /**
     * 说明: 授权的所有用户
     */
    private List<DatasetDetailUserResponseDto> authUserList;

}
