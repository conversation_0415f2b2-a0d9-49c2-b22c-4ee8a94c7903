<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;1ad22bca-1407-4e04-9d59-ef4ab794502b&quot;,&quot;conversations&quot;:{&quot;3417676a-a61a-45eb-8bc0-e0a3f690f033&quot;:{&quot;id&quot;:&quot;3417676a-a61a-45eb-8bc0-e0a3f690f033&quot;,&quot;createdAtIso&quot;:&quot;2025-06-18T08:27:58.808Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-18T08:27:58.808Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;1ad22bca-1407-4e04-9d59-ef4ab794502b&quot;:{&quot;id&quot;:&quot;1ad22bca-1407-4e04-9d59-ef4ab794502b&quot;,&quot;createdAtIso&quot;:&quot;2025-06-18T08:27:58.814Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-18T08:27:58.814Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;cb940dbc-ffbb-486c-8154-61b34244232a&quot;,&quot;uuid&quot;:&quot;d832f29a-07ea-4427-add8-571b2df49e03&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1750235278814,&quot;seen_state&quot;:&quot;seen&quot;}],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;b5d46b05-c97f-4371-b0bb-ece64e1251d5&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>