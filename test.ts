// import MarkdownIt from 'npm:markdown-it@14'

// const str = `您将预计提升**_15.50%_**绩效分（数据仅供参考，实际绩效以最终结果为准），其中上月追溯未攻克暂时没有待攻克商家，本月未攻克可以帮您提高**_15.50%_**分`;

// console.log(JSON.stringify(MarkdownIt().parse(str)))

// import {fromMarkdown} from 'npm:mdast-util-from-markdown';
// console.log(JSON.stringify(fromMarkdown(str)))

// import dayjs from 'npm:dayjs@1.11.13'

// console.log(dayjs('Wed Jul 16 19:15:19 CST 2025').format('YYYY-MM-DD HH:mm:ss'));

const window = globalThis;
import { html, LitElement } from 'https://cdn.jsdelivr.net/npm/lit@2/+esm';
    
    class MyButton extends LitElement {
      handleClick() {
        alert('<PERSON><PERSON> clicked!');
      }
      
      render() {
        return html`<button @click=${this.handleClick}>Click Me</button>`;
      }
    }
    customElements.define('my-button', MyButton);
