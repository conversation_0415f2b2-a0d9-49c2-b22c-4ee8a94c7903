import { View, StyleSheet, Text, TouchableOpacity } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';
import Svg, { Path, LinearGradient, Defs, Stop, Rect } from 'react-native-svg';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 12,
        paddingVertical: 10,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 5,
        borderRadius: 10,
        marginRight: 10,
    },
    gradientContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 10,
        overflow: 'hidden',
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        zIndex: 1,
    },
    icon: {
        width: 18,
        height: 18,
        marginRight: 4,
    },
    iconContainer: {
        width: 14,
        height: 14,
        marginRight: 4,
        borderRadius: 7,
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: '#6047FA',
        borderWidth: 1,
    },
    text: {
        fontSize: 14,
        color: '#999',
    },
    arrowContainer: {
        position: 'absolute',
        right: -8,
        top: '50%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 12,
    },
});

export interface TaskReminderBubbleProps {
    onPress?: () => void;
}

const TaskReminderBubble = (props: TaskReminderBubbleProps) => {
    const handlePress = () => {
        props.onPress?.();
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            activeOpacity={0.8}
            style={[
                styles.container,
                {
                    width: 220,
                    height: 40,
                    position: 'relative',
                },
            ]}
        >
            {/* 渐变背景 */}
            <View style={styles.gradientContainer}>
                <Svg
                    width="100%"
                    height="100%"
                    style={{ position: 'absolute' }}
                >
                    <Defs>
                        <LinearGradient
                            id="bubbleGradient"
                            x1="0%"
                            y1="0%"
                            x2="100%"
                            y2="0%"
                        >
                            <Stop offset="0%" stopColor="#E8F0FE" />
                            <Stop offset="100%" stopColor="#F1FDFF" />
                        </LinearGradient>
                    </Defs>
                    <Rect
                        width="100%"
                        height="100%"
                        fill="url(#bubbleGradient)"
                    />
                </Svg>
            </View>

            {/* 内容 */}
            <View style={styles.contentContainer}>
                <View style={styles.iconContainer}>
                    <Icon type="check" size={8} tintColor="#6047FA" />
                </View>

                <Text
                    style={[
                        styles.text,
                        { color: '#222', marginRight: 4, fontWeight: '500' },
                    ]}
                >
                    商家诊断
                </Text>
                <Text style={styles.text}>任务已全部完成</Text>
                <Icon type="right" size={12} tintColor="#444" />
            </View>
            {/* SVG 气泡箭头 - 指向右侧 */}
            <View style={styles.arrowContainer}>
                <Svg width={8} height={16} viewBox="0 0 8 16">
                    {/* 箭头主体 */}
                    <Path d="M0 0 Q6 4 8 8 Q6 12 0 16 Z" fill="#F1FDFF" />
                </Svg>
            </View>
        </TouchableOpacity>
    );
};

export default TaskReminderBubble;
