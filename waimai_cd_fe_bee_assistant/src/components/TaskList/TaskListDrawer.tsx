import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    SectionList,
    Dimensions,
    Modal,
    Animated,
    Platform,
} from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import dayjs from 'dayjs';
import React, { useState, useEffect, useMemo, useRef } from 'react';

import LeftArrowIcon from './LeftArrowIcon'; // 导入图标
import TaskListItem from './TaskListItem';
import { TaskItem, TaskListResponse } from '../../api/taskApi';

import useCallerRequest from '@/hooks/useCallerRequest';

// 扩展TaskItem类型，包含createTime
interface TaskItemWithTime extends TaskItem {
    createTime: string;
}

interface TaskListDrawerProps {
    /** 是否显示抽屉 */
    visible: boolean;
    /** 关闭抽屉回调 */
    onClose: () => void;
    /** 发送消息回调 */
    onSendMessage: (content: string) => void;
}

const taskTypeEnums = {
    PoiDiagnosis: '商家诊断',
};

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({
    visible,
    onClose,
    onSendMessage,
}) => {
    const [taskData, setTaskData] = useState<TaskListResponse | null>(null);
    const { top } = useSafeAreaInsets();

    // 动画值
    const translateX = useRef(new Animated.Value(screenWidth)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    // 轮询定时器
    const pollingTimer = useRef<NodeJS.Timeout | null>(null);

    const callerRequest = useCallerRequest();
    const getTaskList = async () => {
        const res = await callerRequest.get(
            '/bee/v2/bdaiassistant/job/poiDiagnosis/listToday',
        );
        return res.data;
    };
    // 获取任务列表数据
    const fetchTaskList = async () => {
        try {
            const data = await getTaskList();
            setTaskData(data as unknown as TaskListResponse);
        } catch (error) {
            console.error('获取任务列表失败:', error);
            Toast.open('获取任务列表失败');
        }
    };

    // 开始轮询
    const startPolling = () => {
        // 立即执行一次
        fetchTaskList();

        // 清除之前的定时器（如果存在）
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
        }

        // 设置轮询定时器，每3秒执行一次
        pollingTimer.current = setInterval(() => {
            fetchTaskList();
        }, 3000);
    };

    // 停止轮询
    const stopPolling = () => {
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
            pollingTimer.current = null;
        }
    };

    // 动画控制
    useEffect(() => {
        if (visible) {
            // 打开抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        } else {
            // 关闭抽屉动画
            Animated.parallel([
                Animated.timing(translateX, {
                    toValue: screenWidth,
                    duration: 250,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0,
                    duration: 250,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [visible, translateX, opacity]);

    // 当抽屉打开时开始轮询，关闭时停止轮询
    useEffect(() => {
        if (visible) {
            startPolling();
        } else {
            stopPolling();
        }
    }, [visible]);

    // 组件卸载时清除定时器
    useEffect(() => {
        return () => {
            stopPolling();
        };
    }, []);

    // 处理查看结果
    const handleViewResult = (item: TaskItemWithTime) => {
        if (item.status !== 'success' && item.status !== 'fail') {
            return;
        }

        // 构造查询消息
        onSendMessage(item.content);
        onClose();
    };

    // 处理关闭抽屉
    const handleClose = () => {
        Animated.parallel([
            Animated.timing(translateX, {
                toValue: screenWidth,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(opacity, {
                toValue: 0,
                duration: 250,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onClose();
        });
    };

    // 计算统计数据
    const allTasks = useMemo(() => {
        if (!taskData?.jobList) {
            return [];
        }
        const tasks: TaskItemWithTime[] = [];
        taskData.jobList.forEach((group) => {
            group.itemList.forEach((item) => {
                tasks.push({ ...item, createTime: group.createTime });
            });
        });
        return tasks;
    }, [taskData]);

    const runningCount = allTasks.filter((t) => t.status === 'init').length;
    const successCount = allTasks.filter((t) => t.status === 'success').length;
    const failCount = allTasks.filter((t) => t.status === 'fail').length;

    const sections = useMemo(() => {
        if (!taskData?.jobList) {
            return [];
        }
        return taskData.jobList.map((group) => ({
            title: group.createTime,
            type: group.type,
            data: group.itemList,
            createTime: group.createTime,
        }));
    }, [taskData]);

    const renderEmptyState = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无任务数据</Text>
        </View>
    );

    return (
        <Modal
            visible={visible}
            animationType="none"
            transparent={true}
            onRequestClose={handleClose}
        >
            <View style={styles.overlay}>
                <Animated.View
                    style={[
                        styles.overlayBackground,
                        {
                            opacity: opacity,
                        },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.overlayTouchable}
                        activeOpacity={1}
                        onPress={handleClose}
                    />
                </Animated.View>
                <Animated.View
                    style={[
                        styles.drawer,
                        {
                            transform: [{ translateX }],
                            paddingTop: Platform.OS === 'ios' ? top : 0,
                        },
                    ]}
                >
                    {/* 头部 */}
                    <View style={styles.header}>
                        <Text style={styles.title}>今日任务</Text>
                        <TouchableOpacity
                            onPress={handleClose}
                            style={styles.closeButton}
                            hitSlop={{
                                top: 10,
                                bottom: 10,
                                left: 10,
                                right: 10,
                            }}
                        >
                            <LeftArrowIcon />
                        </TouchableOpacity>
                    </View>

                    {/* 统计信息 */}
                    <View style={styles.statsContainer}>
                        <View style={styles.statItem}>
                            <Text style={styles.statLabel}>进行中</Text>
                            <Text style={[styles.statValue]}>
                                {runningCount}
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text style={styles.statLabel}>成功</Text>
                            <Text style={[styles.statValue]}>
                                {successCount}
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Text style={styles.statLabel}>失败</Text>
                            <Text style={[styles.statValue]}>{failCount}</Text>
                        </View>
                    </View>

                    {/* 任务列表 */}
                    <View style={styles.listContainer}>
                        <SectionList
                            sections={sections}
                            renderItem={({ item }) => (
                                <TaskListItem
                                    item={{
                                        ...item,
                                    }}
                                    onViewResult={handleViewResult}
                                />
                            )}
                            renderSectionHeader={({
                                section: { title, type },
                            }) => {
                                return (
                                    <View style={styles.sectionHeader}>
                                        <Text style={styles.sectionTime}>
                                            {dayjs(title).format('H:mm')}
                                        </Text>
                                        <Text style={styles.sectionTitle}>
                                            {taskTypeEnums[type]}
                                        </Text>
                                    </View>
                                );
                            }}
                            keyExtractor={(item, index) =>
                                `${item.poiId}_${index}`
                            }
                            showsVerticalScrollIndicator={false}
                            ListEmptyComponent={renderEmptyState}
                        />
                    </View>
                </Animated.View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    overlayBackground: {
        flex: 1,
    },
    overlayTouchable: {
        flex: 1,
    },
    drawer: {
        backgroundColor: '#ffffff',
        width: screenWidth * 0.8,
        height: screenHeight,
        shadowColor: '#000',
        shadowOffset: {
            width: -2,
            height: 0,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        paddingTop: 20, // 增加顶部安全区域
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#222222',
    },
    closeButton: {
        padding: 4,
    },
    statsContainer: {
        paddingHorizontal: 16,
        marginBottom: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    statItem: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        backgroundColor: '#F9FAFC',
        borderRadius: 8,
        paddingVertical: 12,
        marginHorizontal: 4,
    },
    statValue: {
        fontSize: 22,
        fontWeight: 'bold',
        marginTop: 4,
        color: '#222',
    },
    statLabel: {
        fontSize: 14,
        color: '#666',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        backgroundColor: '#fff',
    },
    sectionTime: {
        fontSize: 14,
        color: '#666666',
        marginRight: 8,
    },
    sectionTitle: {
        fontSize: 14,
        color: '#222222',
        fontWeight: '500',
    },
    listContainer: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#999999',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 14,
        color: '#999999',
        marginTop: 12,
    },
    iconContainer: {
        padding: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default TaskListDrawer;
