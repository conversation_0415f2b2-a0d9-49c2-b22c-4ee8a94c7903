import {
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';
import { Svg, Path } from 'react-native-svg';

import { TaskItem } from '../../api/taskApi';

// 扩展TaskItem以包含isUnread标志
interface TaskListItemProps {
    /** 任务项数据 */
    item: TaskItem & { isUnread?: boolean };
    /** 查看结果回调 */
    onViewResult: (item: TaskItem) => void;
}

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    // 任务结果在“success”或“fail”状态下可查看
    const canViewResult = item.status === 'success' || item.status === 'fail';
    const isProcessing = item.status === 'init';

    const renderStatusOverlay = () => {
        if (item.status === 'init') {
            return (
                <View style={styles.statusOverlay}>
                    <Svg
                        width="20"
                        height="20"
                        viewBox="-5 -16 45 45"
                        style={{ marginBottom: 2 }}
                    >
                        <Path
                            d="M20 2 L24 12 L34 16 L24 20 L20 30 L16 20 L6 16 L16 12 Z"
                            fill="#fff"
                            opacity="0.9"
                        />
                        <Path
                            d="M7 0 L9 5 L14 7 L9 9 L7 14 L5 9 L0 7 L5 5 Z"
                            fill="#fff"
                            opacity="0.9"
                        />
                    </Svg>
                    <Text style={styles.statusOverlayText}>进行中</Text>
                </View>
            );
        }
        if (item.status === 'fail') {
            return (
                <View style={styles.failStatusOverlay}>
                    <Text
                        style={[
                            styles.statusOverlayText,
                            styles.failStatusText,
                        ]}
                    >
                        失败
                    </Text>
                </View>
            );
        }
        return null;
    };

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={() => onViewResult(item)}
            disabled={!canViewResult}
            activeOpacity={0.7}
        >
            <View style={styles.avatarContainer}>
                {item.poiAvator ? (
                    <Image
                        source={{ uri: item.poiAvator }}
                        style={styles.avatar}
                    />
                ) : (
                    <View style={[styles.avatar, styles.defaultAvatar]}>
                        <Text style={styles.avatarText}>店</Text>
                    </View>
                )}
                {renderStatusOverlay()}
                {item.isUnread && <View style={styles.unreadDot} />}
            </View>

            <View style={styles.content}>
                <Text
                    style={[
                        styles.poiName,
                        isProcessing && styles.disabledPoiName,
                    ]}
                    numberOfLines={1}
                >
                    {item.poiName}
                </Text>
                <Text style={styles.poiId}>ID: {item.poiId}</Text>
            </View>

            <Icon type="right" size={16} style={{ tintColor: '#CCCCCC' }} />
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingLeft: 56,
        paddingRight: 16,
        backgroundColor: '#ffffff',
        borderBottomColor: '#f0f0f0',
    },
    avatarContainer: {
        marginRight: 12,
        overflow: 'hidden',
        borderRadius: 6,
    },
    avatar: {
        width: 48,
        height: 48,
    },
    defaultAvatar: {
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
        justifyContent: 'center',
    },
    avatarText: {
        fontSize: 16,
        color: '#999999',
        fontWeight: '500',
    },
    statusOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: 6,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
    },
    failStatusOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        paddingVertical: 2,
        alignItems: 'center',
        borderBottomLeftRadius: 6,
        borderBottomRightRadius: 6,
    },
    statusOverlayText: {
        color: '#FFFFFF',
        fontSize: 10,
        fontWeight: '500',
    },
    failStatusText: {
        fontSize: 10,
        fontWeight: '400',
    },
    unreadDot: {
        position: 'absolute',
        top: -3,
        right: -3,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#FF4D4F',
    },
    content: {
        flex: 1,
        marginRight: 12,
    },
    poiName: {
        fontSize: 14,
        fontWeight: '400',
        color: '#222222',
        marginBottom: 4,
    },
    disabledPoiName: {
        color: '#999999',
    },
    poiId: {
        fontSize: 12,
        color: '#999999',
    },
});

export default TaskListItem;
