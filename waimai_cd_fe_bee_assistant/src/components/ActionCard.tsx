import { openPage } from '@mfe/bee-foundation-utils';
import { TouchableOpacity, Text, View, StyleSheet } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Toast } from '@roo/roo-rn';
import React from 'react';

import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../types';
import { ActionCardMessage } from '../types/message';

interface Props {
    data: ActionCardMessage['insert']['actionCard'];
    onEndTyping?: () => void;
}

const styles = StyleSheet.create({
    gradientContainer: {
        borderRadius: 12,
        marginVertical: 8,
    },
    container: {
        borderWidth: 1,
        borderColor: '#B8C1FF',
        borderRadius: 11,
        paddingHorizontal: 15,
        paddingVertical: 11,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    contentContainer: {
        flex: 1,
        marginRight: 12,
    },
    title: {
        fontSize: 16,
        fontWeight: '700',
        color: '#6047FA',
        marginBottom: 4,
    },
    subTitle: {
        fontSize: 12,
        color: '#666666',
        lineHeight: 16,
    },
    button: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 14,
        fontWeight: '500',
    },
    primaryButton: {
        backgroundColor: '#FFD100',
    },
    normalButton: {
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    primaryButtonText: {
        color: '#333333',
    },
    normalButtonText: {
        color: '#333333',
    },
});

const ActionCard = ({ data }: Props) => {
    const { button, title, subTitle } = data;
    const { send } = useSendMessage();

    const handleButtonPress = async () => {
        try {
            // 优先处理action
            if (button.action === 'submitQuestion' && button.question) {
                send(
                    button.question,
                    EntryPointType.USER,
                    EntryPoint.action_card,
                );
                return;
            }

            // 处理URL跳转
            if (button.url) {
                openPage(button.url);
                return;
            }

            // 如果没有action和url，显示提示
            Toast.open('暂无可执行的操作');
        } catch (error) {
            console.error('ActionCard button press error:', error);
            Toast.open('操作失败，请重试');
        }
    };

    const getButtonStyle = () => {
        const baseStyle = [styles.button];

        // 优先使用自定义颜色
        if (button.color) {
            return [...baseStyle, { backgroundColor: button.color }];
        }

        // 使用type决定样式
        if (button.type === 'primary') {
            return [...baseStyle, styles.primaryButton];
        }

        return [...baseStyle, styles.normalButton];
    };

    const getButtonTextStyle = () => {
        const baseStyle = [styles.buttonText];

        // 如果有自定义颜色，使用对比色文字
        if (button.color) {
            // 简单的对比度判断，实际项目中可能需要更复杂的算法
            const isLightColor =
                button.color.toLowerCase().includes('ff') ||
                button.color.toLowerCase().includes('yellow') ||
                button.color.toLowerCase().includes('white');
            return [
                ...baseStyle,
                { color: isLightColor ? '#333333' : '#ffffff' },
            ];
        }

        if (button.type === 'primary') {
            return [...baseStyle, styles.primaryButtonText];
        }

        return [...baseStyle, styles.normalButtonText];
    };

    return (
        <LinearGradient
            colors={['rgba(184,193,255, 0.28)', 'rgba(196, 229, 255, 0)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientContainer}
        >
            <View style={styles.container}>
                <View style={styles.contentContainer}>
                    <Text style={styles.title} numberOfLines={1}>
                        {title}
                    </Text>
                    <Text style={styles.subTitle} numberOfLines={2}>
                        {subTitle}
                    </Text>
                </View>

                <TouchableOpacity
                    style={getButtonStyle()}
                    onPress={handleButtonPress}
                    activeOpacity={0.8}
                >
                    <Text style={getButtonTextStyle()} numberOfLines={1}>
                        {button.text}
                    </Text>
                </TouchableOpacity>
            </View>
        </LinearGradient>
    );
};

export default ActionCard;
