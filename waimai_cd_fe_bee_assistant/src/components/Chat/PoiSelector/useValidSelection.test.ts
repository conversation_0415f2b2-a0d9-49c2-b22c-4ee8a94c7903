/**
 * useValidSelection Hook 测试用例
 * 测试智能交集功能的正确性
 */

import { renderHook, act } from '@testing-library/react-hooks';
import { useState } from 'react';

import { useValidSelection } from './hooks/useValidSelection';
import { PoiItemData } from './types/PoiSelector';

describe('useValidSelection', () => {
    const mockPoiA = { id: 'A', name: '商家A' };
    const mockPoiB = { id: 'B', name: '商家B' };
    const mockPoiC = { id: 'C', name: '商家C' };
    const mockPoiD = { id: 'D', name: '商家D' };

    it('应该返回交集结果', () => {
        const { result } = renderHook(() => {
            const [selectedList, setSelectedList] = useState([
                mockPoiA,
                mockPoiB,
            ]);
            const combinedList = [mockPoiA, mockPoiC]; // 只有 A 在交集中

            return {
                validSelected: useValidSelection(
                    combinedList,
                    selectedList,
                    setSelectedList,
                ),
                selectedList,
                setSelectedList,
            };
        });

        // 验证交集结果
        expect(result.current.validSelected).toEqual([mockPoiA]);
    });

    it('应该自动清理无效选中项', () => {
        const { result, rerender } = renderHook(
            ({ combinedList, selectedList }) => {
                const [selected, setSelected] = useState(selectedList);

                return {
                    validSelected: useValidSelection(
                        combinedList,
                        selected,
                        setSelected,
                    ),
                    selected,
                    setSelected,
                };
            },
            {
                initialProps: {
                    combinedList: [mockPoiA, mockPoiB, mockPoiC],
                    selectedList: [mockPoiA, mockPoiB],
                },
            },
        );

        // 初始状态
        expect(result.current.validSelected).toEqual([mockPoiA, mockPoiB]);

        // 更新 combinedList，移除 mockPoiB
        act(() => {
            rerender({
                combinedList: [mockPoiA, mockPoiC, mockPoiD],
                selectedList: result.current.selected,
            });
        });

        // 验证自动清理后的结果
        expect(result.current.validSelected).toEqual([mockPoiA]);
        expect(result.current.selected).toEqual([mockPoiA]);
    });

    it('应该处理空列表情况', () => {
        const { result } = renderHook(() => {
            const [selectedList, setSelectedList] = useState<PoiItemData[]>([]);
            const combinedList: PoiItemData[] = [];

            return useValidSelection(
                combinedList,
                selectedList,
                setSelectedList,
            );
        });

        expect(result.current).toEqual([]);
    });

    it('应该处理完全不匹配的情况', () => {
        const { result } = renderHook(() => {
            const [selectedList, setSelectedList] = useState([
                mockPoiA,
                mockPoiB,
            ]);
            const combinedList = [mockPoiC, mockPoiD]; // 完全不匹配

            return {
                validSelected: useValidSelection(
                    combinedList,
                    selectedList,
                    setSelectedList,
                ),
                selectedList,
                setSelectedList,
            };
        });

        expect(result.current.validSelected).toEqual([]);
    });
});
