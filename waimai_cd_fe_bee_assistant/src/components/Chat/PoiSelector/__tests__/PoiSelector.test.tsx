import { apiCaller } from '@mfe/cc-api-caller-bee';
import { render, waitFor } from '@testing-library/react-native';
import React from 'react';

import PoiSelector from '../PoiSelector';

// Mock dependencies
jest.mock('@mfe/cc-api-caller-bee');
jest.mock('../../../hooks/useKeyboard', () => ({
    __esModule: true,
    default: () => ({ keyboardOffset: 0 }),
}));
jest.mock('../../../hooks/useSendMessage', () => ({
    useSendMessage: () => ({ send: jest.fn() }),
}));
jest.mock('../../../store/uiState', () => ({
    useUiState: () => ({
        poiSelectorOpen: true,
        setPoiSelectorOpen: jest.fn(),
        poiSelectorSelectedList: [],
        setPoiSelectorSelectedList: jest.fn(),
    }),
}));

const mockApiCaller = apiCaller as jest.Mocked<typeof apiCaller>;

describe('PoiSelector', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should call getComponentConfig API on mount', async () => {
        // Mock API responses
        mockApiCaller.get.mockImplementation((url) => {
            if (url === '/bee/v2/bdaiassistant/common/getComponentConfig') {
                return Promise.resolve({
                    code: 0,
                    msg: 'success',
                    data: {
                        poiSelector: {
                            type: 'SingleSelect',
                            defaultList: [
                                {
                                    id: 123,
                                    name: '测试门店',
                                    url: 'http://example.com/image.jpg',
                                    type: 'poi',
                                },
                            ],
                        },
                    },
                });
            }
            return Promise.resolve({
                code: 0,
                msg: 'success',
                data: { poiList: [], total: 0 },
            });
        });

        render(<PoiSelector />);

        await waitFor(() => {
            expect(mockApiCaller.get).toHaveBeenCalledWith(
                '/bee/v2/bdaiassistant/common/getComponentConfig',
                {},
            );
        });
    });

    it('should handle API error gracefully', async () => {
        // Mock API error
        mockApiCaller.get.mockImplementation((url) => {
            if (url === '/bee/v2/bdaiassistant/common/getComponentConfig') {
                return Promise.resolve({
                    code: 1,
                    msg: 'Error',
                });
            }
            return Promise.resolve({
                code: 0,
                msg: 'success',
                data: { poiList: [], total: 0 },
            });
        });

        render(<PoiSelector />);

        await waitFor(() => {
            expect(mockApiCaller.get).toHaveBeenCalledWith(
                '/bee/v2/bdaiassistant/common/getComponentConfig',
                {},
            );
        });
    });

    it('should use default config when API returns empty data', async () => {
        // Mock API with empty data
        mockApiCaller.get.mockImplementation((url) => {
            if (url === '/bee/v2/bdaiassistant/common/getComponentConfig') {
                return Promise.resolve({
                    code: 0,
                    msg: 'success',
                    data: null,
                });
            }
            return Promise.resolve({
                code: 0,
                msg: 'success',
                data: { poiList: [], total: 0 },
            });
        });

        render(<PoiSelector />);

        await waitFor(() => {
            expect(mockApiCaller.get).toHaveBeenCalledWith(
                '/bee/v2/bdaiassistant/common/getComponentConfig',
                {},
            );
        });
    });
});
