import { View, Text, ActivityIndicator } from '@mrn/react-native';
import React from 'react';

import Condition from '@/components/Condition/Condition';

interface SpinProps {
    loading: boolean;
    tip?: string;
    size?: 'small' | 'large';
    color?: string;
    children: React.ReactNode;
}

/**
 * Spin 组件 - 类似 antd 的 Spin 组件
 * 在子组件上方显示加载遮罩，阻止用户操作
 */
const Spin: React.FC<SpinProps> = ({
    loading,
    tip = '加载中...',
    size = 'large',
    color = '#FFD100',
    children,
}) => {
    return (
        <View style={{ flex: 1, position: 'relative' }}>
            {children}

            {loading && (
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 999,
                    }}
                >
                    <View
                        style={{
                            elevation: 3,
                            alignItems: 'center',
                        }}
                    >
                        <ActivityIndicator
                            size={size}
                            color={color}
                            style={{ marginBottom: 12 }}
                        />
                        <Condition condition={[tip]}>
                            <Text
                                style={{
                                    color: '#666',
                                    fontSize: 14,
                                    fontWeight: '500',
                                }}
                            >
                                {tip}
                            </Text>
                        </Condition>
                    </View>
                </View>
            )}
        </View>
    );
};

export default Spin;
