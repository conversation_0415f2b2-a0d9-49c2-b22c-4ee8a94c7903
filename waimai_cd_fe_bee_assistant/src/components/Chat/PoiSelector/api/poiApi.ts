import { apiCaller } from '@mfe/cc-api-caller-bee';

import {
    ApiResponse,
    GetPoiListResponse,
    PaginationData,
} from '../types/PoiSelector';

/**
 * 分页获取 POI 列表
 * @param pageNum 页码
 * @param pageSize 每页大小
 * @param keyword 搜索关键词
 * @returns POI 列表数据
 */
export const getPoiListByPage = async (
    pageNum: number,
    pageSize = 10,
    keyword = '',
): Promise<PaginationData | null> => {
    const res: ApiResponse<GetPoiListResponse> = await apiCaller.get(
        '/bee/v1/bdaiassistant/common/getOwnPoiListByPage',
        { pageSize, pageNum, data: keyword },
    );

    if (res.code === 0) {
        return {
            list: res.data?.poiList || [],
            total: res.data?.total || 0,
        };
    }

    return null;
};
