import {
    View,
    ActivityIndicator,
    Text,
    StyleSheet,
    ViewStyle,
} from '@mrn/react-native';
import React from 'react';

const styles = StyleSheet.create({
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(238, 238, 238, 0.8)',
        borderRadius: 4,
    },
    loadingText: {
        marginTop: 8,
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
});

interface ImageLoadingOverlayProps {
    visible: boolean;
    text?: string;
    style?: ViewStyle;
}

const ImageLoadingOverlay: React.FC<ImageLoadingOverlayProps> = ({
    visible,
    text = 'loading...',
    style,
}) => {
    if (!visible) {
        return null;
    }

    return (
        <View style={[styles.overlay, style]}>
            <ActivityIndicator size="small" color="#ff6a00" />
            {text && <Text style={styles.loadingText}>{text}</Text>}
        </View>
    );
};

export default ImageLoadingOverlay;
