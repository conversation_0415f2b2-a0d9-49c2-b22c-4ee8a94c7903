import { apiCaller, BFFResponse } from '@mfe/cc-api-caller-bee';

/**
 * 任务状态接口响应
 */
export interface TaskStatusResponse {
    /** 正在运行中的任务个数 */
    runnings: number;
    /** 是否有需要点击查看的任务 */
    needToClick: boolean;
}

/**
 * 任务项接口
 */
export interface TaskItem {
    /** 任务类型 */
    type: string;
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: number;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
}

/**
 * 任务组接口
 */
export interface TaskGroup {
    /** 任务类型 */
    type: string;
    /** 创建时间 */
    createTime: string;
    /** 任务名称 */
    jobName: string;
    itemList: TaskItem[];
}

/**
 * 任务列表接口响应
 */
export interface TaskListResponse {
    jobList: TaskGroup[];
    /** 总数 */
    total: number;
    /** 成功个数 */
    success: number;
    /** 失败个数 */
    fail: number;
}

/**
 * API响应基础结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data?: T;
}

/**
 * 获取任务状态
 * @returns 任务状态数据
 */
export const getTaskStatus = async (): Promise<TaskStatusResponse | null> => {
    try {
        const res: ApiResponse<TaskStatusResponse> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/poiDiagnosis/runningToday',
            {},
            { silent: true },
        );

        if (res.code === 0 && res.data) {
            return res.data;
        }

        return null;
    } catch (error) {
        console.error('获取任务状态失败:', error);
        return null;
    }
};

/**
 * 记录弹窗行为
 * @returns 记录结果
 */
export const recordPopup = async (): Promise<BFFResponse<any>> => {
    return apiCaller.get(
        '/bee/v2/bdaiassistant/job/recordPopup',
        {},
        { silent: true },
    );
};
