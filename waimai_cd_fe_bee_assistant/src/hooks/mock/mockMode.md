# 蜜蜂助手 Mock 模式使用说明

## 功能概述

为了方便本地开发和演示，我们为蜜蜂助手实现了完整的 Mock 模式功能。支持 **PC端（Web）** 和 **React Native端（移动端）** 两种环境，提供了丰富的Mock数据管理和场景切换能力。

### 平台支持

- **PC端（Web）**：通过URL参数启用，适合Web开发和演示
- **React Native端（移动端）**：通过本地存储管理，适合移动端开发和测试

## 使用方法

### PC端（Web）使用方法

#### 1. 启用 Mock 模式

在页面 URL 后添加 `devMock` 参数：

```
http://localhost:3000/knowledge/chat?devMock=1
```

或者：

```
http://localhost:3000/knowledge/chat?devMock=true
```

#### 2. PC端特性

-   ✅ **URL参数控制**：通过URL参数快速启用/禁用
-   ✅ **跳过权限检查**：无需登录或权限验证
-   ✅ **自动渲染对话**：页面加载后自动显示预设的问答对话
-   ✅ **真实数据格式**：使用真实的 `fetchAnswer` 接口返回数据格式
-   ✅ **视觉提示**：页面右上角显示 "Mock 模式" 标识

### React Native端（移动端）使用方法

#### 1. 启用 Mock 模式

React Native端提供了更丰富的Mock模式管理功能：

```typescript
import useMockMode from '@/hooks/mock/useMockMode';

const MyComponent = () => {
    const { isMockMode, enableMockMode, disableMockMode, renderMockData } = useMockMode();

    // 启用Mock模式并使用默认数据
    const handleEnableMock = () => {
        enableMockMode(); // 这会自动渲染defaultMockData
    };

    // 使用自定义Mock数据
    const handleCustomMock = () => {
        const customData = {
            code: 0,
            msg: '成功',
            data: {
                // ... 你的自定义数据
            }
        };
        renderMockData(customData);
    };

    return (
        <View>
            <Button title="启用Mock模式" onPress={handleEnableMock} />
            <Button title="使用自定义数据" onPress={handleCustomMock} />
            <Button title="禁用Mock模式" onPress={disableMockMode} />
        </View>
    );
};

const MyComponent = () => {
    const {
        isMockMode,
        enableMockMode,
        disableMockMode,
        switchMockScenario
    } = useMockMode();

    // 启用默认Mock场景
    const handleEnableMock = () => {
        enableMockMode('default');
    };

    // 启用特定场景
    const handleEnablePerformance = () => {
        enableMockMode('performance');
    };

    // 禁用Mock模式
    const handleDisableMock = () => {
        disableMockMode();
    };

    return (
        // 你的组件内容
    );
};
```

#### 2. 使用Mock模式管理器

项目提供了可视化的Mock模式管理组件：

```typescript
import MockModeIndicator from '@/components/MockModeIndicator/MockModeIndicator';

// 在你的页面中添加Mock模式指示器
<MockModeIndicator position="top-right" />
```

#### 3. React Native端特性

-   ✅ **本地存储**：使用AsyncStorage进行数据持久化
-   ✅ **场景管理**：支持多种预设Mock场景
-   ✅ **可视化管理**：提供Mock模式管理界面
-   ✅ **状态持久化**：Mock模式状态在应用重启后保持
-   ✅ **自定义数据**：支持自定义Mock数据
-   ✅ **实时切换**：支持运行时切换Mock场景

## Mock 场景和数据

### 预设Mock场景

React Native端提供了多种预设的Mock场景：

#### 1. default（默认演示）
- **用途**：展示Mock模式的基本功能
- **内容**：包含功能介绍、特性说明、使用指南
- **消息类型**：Markdown、ActionCard、SuffixOptions

#### 2. performance（绩效分析）
- **用途**：演示绩效分析功能
- **内容**：BD绩效报告、目标完成情况、改进建议
- **消息类型**：Markdown表格、数据展示

#### 3. analysis（商家分析）
- **用途**：演示商家经营分析功能
- **内容**：商家经营数据、关键指标、优化建议
- **消息类型**：结构化数据展示

### PC端Mock数据

PC端默认会渲染一个关于"新签攻克绩效"的分析报告，包含：

-   用户问题：帮我分析一下新签攻克绩效
-   AI 回答：包含详细的绩效分析表格和建议
-   后续选项：提供相关的追问选项

## 技术实现

### 关键改进

#### React Native端数据处理优化

最新版本的 `useMockMode` 已经完善了数据处理逻辑，确保与 `fetchAnswer` 接口完全一致：

1. **正确的数据结构**：
   - 不再直接将整个 `defaultMockData` 作为 `currentContent`
   - 正确提取 `defaultMockData.data` 中的各个字段
   - 构建符合 `Message` 接口规范的消息对象

2. **完整的消息属性**：
   - 包含所有必要的消息属性（msgId、questionMsgId、msgType等）
   - 保持与真实接口返回数据的一致性
   - 支持所有消息类型和状态

3. **自动数据解析**：
   - `currentContent` 中的JSON数据会被 `delta2message` 自动解析
   - 支持复杂的消息内容（Markdown、表格、卡片等）
   - 无需手动处理数据转换

### 核心文件

#### PC端文件
-   `src/pages/knowledge/chat/common/service/useMockMode.ts` - PC端Mock模式核心逻辑
-   `src/pages/knowledge/chat/common/service/openSession.tsx` - 集成Mock模式到会话初始化
-   `src/pages/knowledge/chat/main.tsx` - 添加Mock模式视觉提示

#### React Native端文件
-   `src/hooks/mock/useMockMode.ts` - React Native端Mock模式核心逻辑
-   `src/components/MockModeManager/MockModeManager.tsx` - Mock模式管理界面
-   `src/components/MockModeIndicator/MockModeIndicator.tsx` - Mock模式状态指示器

### 数据格式

#### PC端数据格式

PC端Mock数据直接使用 `fetchAnswer` 接口的真实返回格式：

```javascript
{
    "code": 0,
    "msg": "成功",
    "data": {
        "questionMsgId": "87027",
        "msgId": "87031",
        "type": 2,
        "abilityType": 1,
        "status": 1,
        "sensitive": false,
        "msgType": 1,
        "currentContent": "[{\"type\":\"markdown\",\"insert\":{\"markdown\":{\"text\":\"...\"}}}]",
        // ... 其他字段
    }
}
```

#### React Native端数据格式

React Native端使用适配移动端的消息格式：

```typescript
{
    code: 0,
    msg: '成功',
    data: {
        msgId: '87031',
        type: MessageType.ANSWER,
        abilityType: AbilityType.GENERAL,
        status: MessageStatus.DONE,
        msgType: MessageContentType.TEXT,
        currentContent: JSON.stringify([
            {
                type: 'markdown',
                insert: {
                    markdown: {
                        text: '### 标题\n内容...'
                    }
                }
            },
            {
                type: 'actionCard',
                insert: {
                    actionCard: {
                        title: '操作卡片',
                        subTitle: '描述',
                        button: {
                            text: '按钮文本',
                            action: 'submitQuestion',
                            type: 'primary'
                        }
                    }
                }
            }
        ]),
        // ... 其他字段
    }
}
```

### 工作流程

#### PC端工作流程

1. **URL 检测**：`useMockMode` hook 检测 URL 中的 `devMock` 参数
2. **会话初始化**：`openSession` 在 Mock 模式下跳过权限检查，直接生成 mock sessionId
3. **数据渲染**：自动添加用户问题和 AI 回答消息到消息列表
4. **数据解析**：使用 `parseDataFromMsgComponents` 解析 `currentContent` 中的 JSON 数据

#### React Native端工作流程

1. **Mock模式启用**：通过 `enableMockMode()` 启用Mock模式
2. **会话初始化**：生成 mock sessionId 并添加到消息状态中
3. **数据处理**：
   - 提取 `defaultMockData.data` 或自定义数据的 `data` 字段
   - 构建符合 `Message` 接口的消息对象
   - 保持与 `fetchAnswer` 接口相同的数据结构
4. **数据解析**：`currentContent` 中的JSON数据会被 `delta2message` 工具函数自动解析
5. **消息渲染**：通过 `useMessage` hook 添加消息到消息列表，支持：
   - Markdown文本渲染
   - 表格数据显示
   - 操作卡片交互
   - 后续选项展示
6. **状态管理**：Mock模式状态通过React状态管理

## 自定义 Mock 数据

### PC端自定义数据

编辑 `src/pages/knowledge/chat/common/service/useMockMode.ts` 文件中的 `defaultMockData` 对象：

```javascript
const defaultMockData = {
    code: 0,
    msg: '成功',
    data: {
        // 修改这里的数据
        currentContent: '[{"type":"markdown","insert":{"markdown":{"text":"你的自定义内容"}}}]',
    },
};
```

### React Native端自定义数据

#### 1. 修改预设场景

编辑 `src/hooks/mock/useMockMode.ts` 文件中的 `mockDataScenarios` 对象：

```typescript
const mockDataScenarios = {
    // 添加新的场景
    myCustomScene: {
        ...defaultMockData,
        data: {
            ...defaultMockData.data,
            currentContent: JSON.stringify([
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '### 我的自定义场景\n这是自定义的Mock数据内容'
                        }
                    }
                }
            ])
        }
    }
};
```

#### 2. 运行时设置自定义数据

```typescript
const { setCustomMockData } = useMockMode();

const customData = {
    code: 0,
    msg: '成功',
    data: {
        msgId: 'custom_001',
        type: MessageType.ANSWER,
        status: MessageStatus.DONE,
        currentContent: JSON.stringify([
            {
                type: 'text',
                insert: '这是运行时设置的自定义数据'
            }
        ])
    }
};

// 设置自定义数据
await setCustomMockData(customData);
```

#### 3. 支持的消息类型

React Native端支持以下消息类型：

- `text` - 纯文本消息
- `markdown` - Markdown格式消息
- `image` - 图片消息
- `link` - 链接消息
- `actionCard` - 操作卡片
- `suffixOptions` - 后续选项
- `selector` - 选择器
- `table` - 表格
- `buttons` - 按钮组
- `media` - 媒体消息

## 最佳实践

### React Native端最佳实践

1. **开发阶段使用**
   - 在开发新功能时启用Mock模式，快速验证UI和交互
   - 使用不同场景测试各种消息类型的渲染效果

2. **演示和展示**
   - 向产品经理或客户演示功能时使用Mock模式
   - 准备多个场景以展示不同的功能特性

3. **测试和调试**
   - 在网络不稳定时使用Mock模式进行离线开发
   - 测试异常情况和边界条件

4. **数据管理**
   - 定期清理不需要的Mock数据
   - 保持Mock数据与真实接口格式同步

### PC端最佳实践

1. **URL参数管理**
   - 使用有意义的参数值，如 `?devMock=performance`
   - 在团队中统一Mock参数的命名规范

2. **数据同步**
   - 定期更新Mock数据以反映最新的接口变化
   - 确保Mock数据的真实性和有效性

## 注意事项

### 通用注意事项

1. **仅用于开发**：Mock 模式仅用于本地开发和演示，不应在生产环境使用
2. **数据格式**：确保 Mock 数据格式与真实接口返回格式一致
3. **JSON 格式**：`currentContent` 字段必须是有效的 JSON 字符串
4. **性能考虑**：避免在Mock数据中包含过大的内容

### React Native端特殊注意事项

1. **存储管理**：Mock数据存储在AsyncStorage中，注意存储空间限制
2. **状态同步**：确保Mock模式状态在不同组件间正确同步
3. **内存管理**：及时清理不需要的Mock数据，避免内存泄漏
4. **平台兼容**：确保Mock功能在iOS和Android平台都能正常工作

## 常见问题

### PC端常见问题

#### Q: Mock 模式不生效？
A: 检查 URL 参数是否正确添加，确保包含 `devMock` 参数

#### Q: 如何添加新的 Mock 数据？
A: 修改 `useMockMode.ts` 中的 `defaultMockData` 对象，或扩展代码支持多个 Mock 数据文件

#### Q: Mock 数据格式错误？
A: 确保 `currentContent` 是有效的 JSON 字符串，可以使用在线 JSON 验证工具检查格式

### React Native端常见问题

#### Q: Mock模式启用后没有显示内容？
A: 检查以下几点：
1. 确认Mock数据格式正确
2. 查看控制台是否有错误信息
3. 确认消息状态管理是否正常工作

#### Q: 如何清除Mock模式数据？
A: 使用以下代码清除：
```typescript
const { disableMockMode } = useMockMode();
await disableMockMode();
// 或者直接清除AsyncStorage
import { AsyncStorage } from '@mrn/react-native';
await AsyncStorage.removeItem('BEE_INTELLIGENT_ASSISTANT-mockMode');
await AsyncStorage.removeItem('BEE_INTELLIGENT_ASSISTANT-mockData');
```

#### Q: Mock模式指示器不显示？
A: 确认以下几点：
1. 已正确导入并使用 `MockModeIndicator` 组件
2. Mock模式已成功启用
3. 组件的 `position` 属性设置正确

#### Q: 如何在不同场景间切换？
A: 使用 `switchMockScenario` 方法：
```typescript
const { switchMockScenario } = useMockMode();
await switchMockScenario('performance'); // 切换到绩效分析场景
```

#### Q: 自定义Mock数据不生效？
A: 检查数据格式是否符合要求：
1. `currentContent` 必须是JSON字符串
2. 消息类型必须是支持的类型
3. 数据结构必须完整

### 通用问题

#### Q: 如何获取真实的接口数据格式？
A:
- **PC端**：在浏览器开发者工具中查看 `fetchAnswer` 接口的返回数据
- **React Native端**：查看网络请求日志或使用调试工具

#### Q: Mock数据如何与真实数据保持同步？
A:
1. 定期从真实接口复制最新的返回数据
2. 建立Mock数据的版本管理机制
3. 在接口变更时及时更新Mock数据

## 示例用法

### PC端示例

```bash
# 启动开发服务器
npm run dev

# 在浏览器中访问
http://localhost:3000/knowledge/chat?devMock=1

# 页面会自动显示 Mock 对话内容
```

### React Native端示例

#### 1. 基础使用

```typescript
import React from 'react';
import { View, Button } from '@mrn/react-native';
import useMockMode from '@/hooks/mock/useMockMode';
import MockModeIndicator from '@/components/MockModeIndicator/MockModeIndicator';

const MyPage = () => {
    const {
        isMockMode,
        enableMockMode,
        disableMockMode,
        getAvailableScenarios
    } = useMockMode();

    return (
        <View>
            {/* Mock模式指示器 */}
            <MockModeIndicator position="top-right" />

            {/* 控制按钮 */}
            <Button
                title={isMockMode ? "禁用Mock" : "启用Mock"}
                onPress={isMockMode ? disableMockMode : () => enableMockMode()}
            />

            {/* 场景切换 */}
            {getAvailableScenarios().map(scenario => (
                <Button
                    key={scenario}
                    title={`切换到${scenario}`}
                    onPress={() => enableMockMode(scenario)}
                />
            ))}
        </View>
    );
};
```

#### 2. 高级用法

```typescript
import React, { useEffect } from 'react';
import useMockMode from '@/hooks/mock/useMockMode';

const AdvancedMockExample = () => {
    const {
        isMockMode,
        currentMockData,
        setCustomMockData,
        scenarios
    } = useMockMode();

    // 设置自定义Mock数据
    const setupCustomMock = async () => {
        const customData = {
            code: 0,
            msg: '成功',
            data: {
                msgId: 'custom_' + Date.now(),
                type: 2, // MessageType.ANSWER
                status: 1, // MessageStatus.DONE
                currentContent: JSON.stringify([
                    {
                        type: 'markdown',
                        insert: {
                            markdown: {
                                text: '### 自定义Mock数据\n这是通过代码设置的自定义Mock内容'
                            }
                        }
                    }
                ])
            }
        };

        await setCustomMockData(customData);
    };

    useEffect(() => {
        console.log('Mock模式状态:', isMockMode);
        console.log('当前Mock数据:', currentMockData);
        console.log('可用场景:', Object.keys(scenarios));
    }, [isMockMode, currentMockData, scenarios]);

    return (
        // 你的组件内容
        <View>
            {/* 组件内容 */}
        </View>
    );
};
```

#### 3. 完整集成示例

```typescript
// 在主页面中集成Mock功能
import React from 'react';
import { View } from '@mrn/react-native';
import ChatContent from '@/components/Chat/ChatContent';
import MockModeIndicator from '@/components/MockModeIndicator/MockModeIndicator';

const ChatPage = () => {
    return (
        <View style={{ flex: 1 }}>
            {/* 聊天内容 */}
            <ChatContent />

            {/* Mock模式指示器 - 仅在开发环境显示 */}
            {__DEV__ && <MockModeIndicator position="top-right" />}
        </View>
    );
};

export default ChatPage;
```

这样就可以在React Native环境中快速启用Mock模式，进行功能开发、测试和演示，无需依赖真实的后端接口。Mock模式提供了完整的状态管理、数据持久化和可视化管理功能，大大提升了开发效率。
