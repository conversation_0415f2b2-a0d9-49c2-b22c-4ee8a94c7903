# RejectCard 拒绝卡片消息组件

RejectCard 是一个双端支持的拒绝卡片式消息组件，用于在聊天界面中展示拒绝原因信息，支持多个拒绝项展示和展开收起功能。

## 功能特性

- ✅ 双端支持（React Native + React Web）
- ✅ 支持多个拒绝项展示
- ✅ 支持Markdown内容渲染
- ✅ 支持描述信息展示
- ✅ 支持URL跳转和提问操作
- ✅ 支持展开收起功能
- ✅ 自定义按钮样式和颜色
- ✅ 响应式设计

## 数据格式

```typescript
interface RejectCard {
    type: 'rejectCard';
    insert: {
        rejectCard: {
            content: {
                title: string;
                content: string; // md字符串
                descriptions: {
                    label: string;
                    value: string;
                }[];
                button: {
                    text: string; // 必填，展示文本
                    url?: string; // 跳链
                    question?: string; // 提问内容
                    action?: 'submitQuestion'; // action > url, 可以用来定义操作，每次新增操作需要开发
                    color: string; // color > type，css支持的color，hexColor等
                    type: string; // 'primary' | 'normal', primary: 美团黄，normal：白色
                };
            }[];
            extendButtonName: string; // 展开按钮文本，为空或者reasons数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
        };
    };
}
```

## 使用示例

### 1. 基础拒绝卡片

```json
{
    "type": "rejectCard",
    "insert": {
        "rejectCard": {
            "content": [
                {
                    "title": "门店信息不符合要求",
                    "content": "您的门店**营业执照**信息与实际不符，请重新提交正确信息。",
                    "descriptions": [
                        {
                            "label": "拒绝原因",
                            "value": "营业执照信息错误"
                        },
                        {
                            "label": "处理时间",
                            "value": "2024-01-15 14:30:00"
                        }
                    ],
                    "button": {
                        "text": "重新提交",
                        "url": "https://example.com/resubmit",
                        "color": "#ff4d4f",
                        "type": "primary"
                    }
                }
            ],
            "extendButtonName": "",
            "showNum": null
        }
    }
}
```

### 2. 多项拒绝卡片（带展开功能）

```json
{
    "type": "rejectCard",
    "insert": {
        "rejectCard": {
            "content": [
                {
                    "title": "门店资质审核失败",
                    "content": "您的门店资质审核未通过，主要原因如下：\n\n- 营业执照模糊不清\n- 食品经营许可证过期",
                    "descriptions": [
                        {
                            "label": "审核状态",
                            "value": "已拒绝"
                        },
                        {
                            "label": "审核员",
                            "value": "张三"
                        }
                    ],
                    "button": {
                        "text": "查看详情",
                        "url": "https://example.com/detail/1",
                        "color": "#1890ff",
                        "type": "normal"
                    }
                },
                {
                    "title": "门店地址信息错误",
                    "content": "门店地址与实际经营地址不符，请核实后重新填写。",
                    "descriptions": [
                        {
                            "label": "错误类型",
                            "value": "地址不符"
                        }
                    ],
                    "button": {
                        "text": "修改地址",
                        "question": "我要修改门店地址",
                        "action": "submitQuestion",
                        "color": "#52c41a",
                        "type": "primary"
                    }
                },
                {
                    "title": "联系方式无效",
                    "content": "提供的联系电话无法接通，请提供有效的联系方式。",
                    "descriptions": [
                        {
                            "label": "联系状态",
                            "value": "无法接通"
                        }
                    ],
                    "button": {
                        "text": "更新联系方式",
                        "url": "https://example.com/contact",
                        "color": "#faad14",
                        "type": "normal"
                    }
                }
            ],
            "extendButtonName": "查看更多拒绝原因",
            "showNum": 2
        }
    }
}
```

### 3. 带提问功能的拒绝卡片

```json
{
    "type": "rejectCard",
    "insert": {
        "rejectCard": {
            "content": [
                {
                    "title": "商品信息审核不通过",
                    "content": "您上传的商品图片质量不符合平台要求，请重新上传**高清商品图片**。",
                    "descriptions": [
                        {
                            "label": "问题类型",
                            "value": "图片质量问题"
                        },
                        {
                            "label": "要求",
                            "value": "图片分辨率不低于800x800"
                        }
                    ],
                    "button": {
                        "text": "咨询客服",
                        "question": "我想了解商品图片的具体要求",
                        "action": "submitQuestion",
                        "color": "#722ed1",
                        "type": "primary"
                    }
                }
            ],
            "extendButtonName": "",
            "showNum": null
        }
    }
}
```

## 实现文件

### React Native (waimai_cd_fe_bee_assistant)

- **组件文件**: `src/components/RejectCard.tsx`
- **类型定义**: `src/types/message.ts` (已添加 RejectCardMessage)
- **渲染逻辑**: `src/components/MessageBox/Answer/AnswerContent/textMessageUtils.tsx`

### React Web (waimai_cd_fe_crm_bdservice)

- **组件文件**: `src/pages/knowledge/chat/common/ui/messageContent/RejectCard.tsx`
- **样式文件**: `src/pages/knowledge/chat/common/ui/messageContent/RejectCard.scss`
- **类型定义**: `src/pages/knowledge/chat/common/type/message.ts` (已添加 RejectCardMessage)
- **渲染逻辑**: `src/pages/knowledge/chat/common/ui/message/messageItemRender.tsx`

## 样式设计

### 卡片样式
- 圆角: 12px
- 内边距: 16px
- 阴影: 轻微阴影效果
- 背景: 白色
- 边框: 浅灰色边框

### 文本样式
- **标题**: 16px, 粗体, 最多2行省略
- **内容**: 14px, 支持Markdown渲染
- **描述**: 14px, 标签灰色，值黑色

### 按钮样式
- **Primary**: 美团黄 (#ffdd10) 或自定义颜色
- **Normal**: 白色背景 + 边框
- 圆角: 20px
- 高度: 自适应

### 展开按钮样式
- 背景: 浅灰色 (#f5f5f5)
- 圆角: 20px
- 居中显示

## 交互逻辑

### 按钮交互
1. **优先级**: action > url
2. **submitQuestion**: 发送问题到聊天
3. **URL跳转**: 打开指定链接
4. **无操作**: 显示提示信息

### 展开收起
1. 当 `extendButtonName` 不为空且 `showNum` 小于内容数量时显示展开按钮
2. 点击展开按钮切换显示状态
3. 展开时显示所有内容，收起时显示限制数量的内容

## 注意事项

1. **Markdown支持**: content字段支持Markdown语法，会自动渲染
2. **按钮颜色**: 支持CSS颜色值，包括十六进制、颜色名称等
3. **描述信息**: descriptions数组为空时不显示描述区域
4. **展开功能**: showNum为null时不限制显示数量，不显示展开按钮
5. **响应式**: 组件会根据屏幕尺寸自动调整布局
