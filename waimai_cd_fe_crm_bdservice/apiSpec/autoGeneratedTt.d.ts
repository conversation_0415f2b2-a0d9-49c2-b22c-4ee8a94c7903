/**
 * pc
 * @namespace AutoGeneratedTtTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedTtTypes = {
    /** tt公告已读 */
    '/helpbd/w/notice/read': {
        request: Record<string, never>;
        response: boolean;
    };
    /** 埋点_pc */
    '/helpbd/w/tracking/entry': {
        request: {
            /** 来源，bee_my_help：通过蜜蜂上的在线提问，bee_poi_detail：通过蜜蜂上商家详情进入提问，daxiang_workstation_app：通过大象工作台进入提问 */
            source: string;
            /** 摩西机器人ID */
            robotId: string;
            /** 租户ID */
            tenantId?: number;
            /** 租户名称 */
            tenantName?: string;
            /** 业务ID */
            bizId?: number;
            /** 业务名称 */
            bizName?: string;
            robotType: number;
        };
        response: Record<string, never>;
    };
    /** 获取工单提醒 */
    '/helpbd/r/notice': {
        request: Record<string, never>;
        response: {
            content: string;
            count: number;
            message: string;
            messageForDx: string;
            alertNotice: boolean;
        };
    };
    /** 获取工单详情 */
    '/helpbd/r/ttInfo': {
        request: Record<string, never>;
        response: {
            uid: number;
            misId: string;
            unresolved: number;
            reporter: number;
            ttSchemeParams: {
                p1: string;
                hhh: string;
            };
        };
    };
    /** 获取当前用户的摩西机器人ID_pc */
    '/helpbd/r/moses': {
        request: {
            /** 租户ID，从“团队管理”的界面获取数据 */
            tenantId?: string;
            /** 租户名称 */
            tenantName?: string;
            /** 业务ID，从“团队管理”的界面获取数据 */
            bizId?: string;
            /** 业务名称 */
            bizName?: string;
            /** 系统名称，枚举值：android，ios */
            sysName?: string;
            /** 系统版本号 */
            sysVer?: string;
            /** 请求来源的app名称 */
            appName?: string;
            /** 请求来源app版本号 */
            appVer?: string;
            source: string;
        };
        response: {
            /** 摩西机器人 ID */
            id?: string;
            aiUrl?: string;
            robotType: number;
        };
    };
};
