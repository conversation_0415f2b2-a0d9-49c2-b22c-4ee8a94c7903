interface CommonItem {
    childrenList: CommonItem[];
    id: number;
    orgType: number;
    isLeaf: number;
    parentId: number;
}

export type COMMON_API = {
    '/uicomponent/getLoginUser': {
        request: Record<string, any>;
        response: {
            id: number;
            login: string;
            name: string;
        };
    };
    '/wmcrm/v1/c/menu/daxiang-user-info': {
        request: {
            misIds: string[];
        };
        response: {
            avatarUrl: string[];
            imUid: number;
            misId: string;
            name: string;
            org: string;
            orgId: string;
            uid: number;
        }[];
    };
    '/uicomponent/api/orgs/getByPid': {
        request: {
            sources: string;
            parentId: string;
        };
        response: {
            isHq: boolean;
            list: CommonItem[];
        };
    };
    '/honeycomb/virtualOrg/r/orgInfo': {
        request: {
            sourceId: string;
            orgId: string;
        };
        response: CommonItem;
    };
    '/uicomponent/employs': {
        request: {
            content: string;
            type: string;
        };
        response: {
            login: string;
            name: string;
            id: number;
        }[];
    };
};
