/**
 * live_script
 * @namespace AutoGeneratedLiveScriptTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedLiveScriptTypes = {
    /** 导入商品信息接口 */
    '/livescript/sku/import': {
        request: {
            /** 请求 ID，同时也作为任务 ID */
            requestId?: string;
            /** excel 文件链接 */
            url?: string;
        };
        response: string;
    };
    /** 生成直播脚本 */
    '/livescript/task/generate': {
        request: {
            /** 请求 ID，同时也作为任务 ID */
            requestId?: string;
        };
        response: string;
    };
    /** 自增 ID 生成接口 */
    '/livescript/id/generate': {
        request: Record<string, never>;
        response: string;
    };
    /** 商品信息编辑接口 */
    '/livescript/sku/edit': {
        request: {
            /** 折扣 */
            discount?: number;
            /** 关键字 */
            keywords?: string;
            /** 请求 ID，同时也作为任务 ID */
            requestId?: string;
            /** SKU ID */
            skuId?: string;
        };
        response: string;
    };
    /** 商品信息查询接口 */
    '/livescript/sku/list': {
        request: {
            /** 排序规则 */
            orderBy?: number;
            /** 当前页数 */
            pageNum?: number;
            /** 页面数量 */
            pageSize?: number;
            /** 请求 ID，同时也作为任务 ID */
            requestId?: string;
        };
        response: {
            data: {
                /** 门店品类 */
                category?: string;
                /** 内容描述 */
                content?: string;
                /** 折扣 */
                discount?: number;
                /** SKU id */
                id?: string;
                /** 关键词 */
                keywords?: string;
                /** 商家 ID */
                poiId?: number;
                /** 商家名称 */
                poiName?: string;
                /** 原价 */
                price?: number;
                /** 生成结果状态 */
                resultStatus?: number;
                /** 生成结果状态描述 */
                resultStatusDesc?: string;
                /** SKU 名称 */
                skuName?: string;
            }[];
            /** 当前页码 */
            pageNum?: number;
            /** 每页数量 */
            pageSize?: number;
            /** 数据总条数 */
            total?: number;
            /** 总页码数 */
            totalPage?: number;
        };
    };
    /** 商品信息删除接口 */
    '/livescript/sku/delete': {
        request: {
            /** 请求 ID，同时也作为任务 ID */
            requestId?: string;
            /** SKU ID */
            skuId?: string;
        };
        response: string;
    };
    /** 查询直播脚本生成结果，商品列表 */
    '/livescript/task/skuList': {
        request: {
            /** 当前页数 */
            pageNum?: number;
            /** 页面数量 */
            pageSize?: number;
            /** 任务 ID */
            taskId?: string;
        };
        response: {
            data: {
                /** 门店品类 */
                category?: string;
                /** 内容描述 */
                content?: string;
                /** 折扣 */
                discount?: number;
                /** SKU id */
                id?: string;
                /** 关键词 */
                keywords?: string;
                /** 商家 ID */
                poiId?: number;
                /** 商家名称 */
                poiName?: string;
                /** 原价 */
                price?: number;
                /** 生成结果状态 */
                resultStatus?: number;
                /** 生成结果状态描述 */
                resultStatusDesc?: string;
                /** SKU 名称 */
                skuName?: string;
            }[];
            /** 当前页码 */
            pageNum?: number;
            /** 每页数量 */
            pageSize?: number;
            /** 数据总条数 */
            total?: number;
            /** 总页码数 */
            totalPage?: number;
        };
    };
    /** 查询直播脚本生成结果导出 */
    '/livescript/task/export': {
        request: {
            /** ID */
            id?: string;
        };
        response: string;
    };
    /** 查询直播脚本生成任务列表 */
    '/livescript/task/list': {
        request: {
            /** 当前页数 */
            pageNum?: number;
            /** 页面数量 */
            pageSize?: number;
            /** 创建人 uid */
            uid?: number;
        };
        response: {
            data: {
                /** 生成内容概述 */
                content?: string;
                /** 创建时间 */
                createTime?: number;
                /** 完成时间 */
                finishTime?: number;
                /** 任务 ID */
                id?: string;
                /** 任务状态 */
                status?: number;
                /** 已读状态 */
                viewStatus?: number;
            }[];
            /** 当前页码 */
            pageNum?: number;
            /** 每页数量 */
            pageSize?: number;
            /** 数据总条数 */
            total?: number;
            /** 总页码数 */
            totalPage?: number;
        };
    };
    /** 查询直播脚本生成任务详情 */
    '/livescript/task/detail': {
        request: {
            /** ID */
            id?: string;
        };
        response: {
            /** 生成内容概述 */
            content?: string;
            /** 创建时间 */
            createTime?: number;
            /** 完成时间 */
            finishTime?: number;
            /** 任务 ID */
            id?: string;
            /** 任务状态 */
            status?: number;
            /** 已读状态 */
            viewStatus?: number;
        };
    };
    /** 表单直传加签接口 */
    '/livescript/sku/upload/signature': {
        request: Record<string, never>;
        response: {
            test: string;
        };
    };
    /** 生成任务结果已读 */
    '/livescript/task/detail/viewed': {
        request: {
            /** ID */
            id?: string;
        };
        response: {
            /** 状态码，0-成功、1-失败 */
            code?: number;
            /** 详细数据 */
            data?: string;
            /** 错误消息 */
            msg?: string;
        };
    };
};
