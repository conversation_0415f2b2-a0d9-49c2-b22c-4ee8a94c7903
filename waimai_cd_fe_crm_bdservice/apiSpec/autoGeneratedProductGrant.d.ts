/**
 * 投放模块
 * @namespace AutoGeneratedProductGrantTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedProductGrantTypes = {
    /** APP-查询投放产品列表 */
    '/bee/v1/crm/product/grant/searchGrantProductList': {
        request: {
            pageNum: number;
            pageSize: number;
            productId: number;
            productType: number;
            grant?: boolean;
            status?: number;
            grantTargetKeyword?: string;
            valid: number;
        };
        response: {
            row: {
                grantTargetId: string;
                grantTargetName: string;
                onlineTime: string;
                logo: string;
                grant: boolean;
                status: number;
                operator: {
                    uid: number;
                    mis: string;
                    name: string;
                };
                orgPath: string[];
                receiveTime: string;
                remindTime: string;
                discountPercent: number;
                receiveAmount: number;
                useAmount: number;
                remainUseAmount: number;
                useValidEndTime: string;
            }[];
            pageNum: number;
            pageSize: number;
            total: number;
        };
    };
    /** PC-导出投放产品列表 */
    '/product/grant/exportGrantProductList': {
        request: {
            pageNum?: number;
            pageSize?: number;
            productId: number;
            productType: number;
            grant?: boolean;
            status?: number;
            grantTargetKeyword?: string;
        };
        response: string;
    };
    /** PC-查询投放产品列表 */
    '/product/grant/searchGrantProductList': {
        request: {
            pageNum?: number;
            pageSize?: number;
            productId?: number;
            productType?: number;
            grant?: boolean;
            status?: number;
            grantTargetKeyword?: string;
            valid: number;
            containStatusList: number[];
        };
        response: {
            row: {
                grantTargetId: string;
                grantTargetName: string;
                onlineTime: string;
                grant: boolean;
                status: number;
                operator: {
                    uid: number;
                    mis: string;
                    name: string;
                };
                orgPath: string[];
                receiveTime: string;
                remindTime: string;
                discountPercent: number;
                receiveAmount: number;
                useAmount: number;
                remainUseAmount: number;
                useValidEndTime: string;
            }[];
            pageNum: number;
            pageSize: number;
            total: number;
        };
    };
};
