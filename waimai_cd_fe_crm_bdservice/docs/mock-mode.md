# 聊天机器人 Mock 模式使用说明

## 功能概述

为了方便本地开发和演示，我们实现了一个简单的 Mock 模式。当 URL 中包含 `devMock` 参数时，页面会自动进入 Mock 模式，跳过权限检查，并自动渲染预设的对话内容。

## 使用方法

### 1. 启用 Mock 模式

在页面 URL 后添加 `devMock` 参数：

```
http://localhost:3000/knowledge/chat?devMock=1
```

或者：

```
http://localhost:3000/knowledge/chat?devMock=true
```

### 2. Mock 模式特性

-   ✅ **跳过权限检查**：无需登录或权限验证
-   ✅ **自动渲染对话**：页面加载后自动显示预设的问答对话
-   ✅ **真实数据格式**：使用真实的 `fetchAnswer` 接口返回数据格式
-   ✅ **视觉提示**：页面右上角显示 "Mock 模式" 标识

### 3. 当前 Mock 数据

默认会渲染一个关于"新签攻克绩效"的分析报告，包含：

-   用户问题：帮我分析一下新签攻克绩效
-   AI 回答：包含详细的绩效分析表格和建议
-   后续选项：提供相关的追问选项

## 技术实现

### 核心文件

-   `src/pages/knowledge/chat/common/service/useMockMode.ts` - Mock 模式核心逻辑
-   `src/pages/knowledge/chat/common/service/openSession.tsx` - 集成 Mock 模式到会话初始化
-   `src/pages/knowledge/chat/main.tsx` - 添加 Mock 模式视觉提示

### 数据格式

Mock 数据直接使用 `fetchAnswer` 接口的真实返回格式：

```javascript
{
    "code": 0,
    "msg": "成功",
    "data": {
        "questionMsgId": "87027",
        "msgId": "87031",
        "type": 2,
        "abilityType": 1,
        "status": 1,
        "sensitive": false,
        "msgType": 1,
        "currentContent": "[{\"type\":\"markdown\",\"insert\":{\"markdown\":{\"text\":\"...\"}}}]",
        // ... 其他字段
    }
}
```

### 工作流程

1. **URL 检测**：`useMockMode` hook 检测 URL 中的 `devMock` 参数
2. **会话初始化**：`openSession` 在 Mock 模式下跳过权限检查，直接生成 mock sessionId
3. **数据渲染**：自动添加用户问题和 AI 回答消息到消息列表
4. **数据解析**：使用 `parseDataFromMsgComponents` 解析 `currentContent` 中的 JSON 数据

## 自定义 Mock 数据

### 修改默认数据

编辑 `src/pages/knowledge/chat/common/service/useMockMode.ts` 文件中的 `defaultMockData` 对象：

```javascript
const defaultMockData = {
    code: 0,
    msg: '成功',
    data: {
        // 修改这里的数据
        currentContent: '[{"type":"markdown","insert":{"markdown":{"text":"你的自定义内容"}}}]',
    },
};
```

### 支持多个 Mock 数据

可以扩展代码支持根据 `devMock` 参数值加载不同的 Mock 数据：

```javascript
// 例如：?devMock=performance 加载绩效数据
// ?devMock=analysis 加载分析数据
```

## 注意事项

1. **仅用于开发**：Mock 模式仅用于本地开发和演示，不应在生产环境使用
2. **数据格式**：确保 Mock 数据格式与真实接口返回格式一致
3. **JSON 格式**：`currentContent` 字段必须是有效的 JSON 字符串
4. **消息解析**：使用现有的 `parseDataFromMsgComponents` 函数确保消息正确解析和渲染

## 常见问题

### Q: Mock 模式不生效？

A: 检查 URL 参数是否正确添加，确保包含 `devMock` 参数

### Q: 如何添加新的 Mock 数据？

A: 修改 `useMockMode.ts` 中的 `defaultMockData` 对象，或扩展代码支持多个 Mock 数据文件

### Q: Mock 数据格式错误？

A: 确保 `currentContent` 是有效的 JSON 字符串，可以使用在线 JSON 验证工具检查格式

### Q: 如何获取真实的接口数据格式？

A: 在浏览器开发者工具中查看 `fetchAnswer` 接口的返回数据，复制完整的响应体

## 示例用法

```bash
# 启动开发服务器
npm run dev

# 在浏览器中访问
http://localhost:3000/knowledge/chat?devMock=1

# 页面会自动显示 Mock 对话内容
```

这样就可以快速预览和演示聊天机器人的功能，无需真实的后端接口和用户权限。
