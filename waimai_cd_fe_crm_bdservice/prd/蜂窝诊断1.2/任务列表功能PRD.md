# 蜂窝诊断 1.2 - 任务列表功能 PRD

## 1. 功能概述

### 1.1 背景

为了提升用户体验，让用户能够实时查看和管理自己发起的诊断任务，需要在现有系统中新增任务列表功能。用户可以通过任务列表查看任务执行状态，并在任务完成后快速查看结果。

### 1.2 目标

-   提供任务状态的实时查看能力
-   支持任务结果的快速查看
-   提升用户对任务执行过程的感知度
-   优化用户操作流程

## 2. 功能需求

### 2.1 核心功能

1. **任务列表入口**

    - 在主界面添加任务列表入口按钮
    - 按钮显示当前运行中的任务数量

2. **任务列表展示**

    - 以抽屉面板形式展示任务列表
    - 支持任务状态分类查看（进行中/成功/失败）
    - 显示任务详细信息（商家信息、创建时间、状态等）

3. **任务触发功能**
    - 支持成功/失败任务的结果查看
    - 自动向聊天机器人发起问题查询

### 2.2 交互流程

#### 2.2.1 任务列表查看流程

```
用户点击任务列表按钮 → 调用接口获取任务状态 → 展示抽屉面板 → 显示任务列表
```

#### 2.2.2 任务触发流程

```
用户点击任务项 → 检查当前聊天状态 →
├─ 正在交互中：显示等待提示
└─ 无交互：自动发起问题查询
```

## 3. 接口定义

### 3.1 获取任务状态接口

-   **接口名称**：获取当前用户任务列表状态
-   **请求方式**：GET
-   **接口路径**：`/bee/v2/bdaiassistant/common/task/list
-   **响应格式**：参考 apiSpec/autoGeneratedChat.d.ts

### 3.2 获取任务列表接口

-   **接口名称**：获取用户任务列表
-   **请求方式**：GET
-   **接口路径**：`/bee/v2/bdaiassistant/job/list`
-   **响应格式**：参考 apiSpec/autoGeneratedChat.d.ts

## 4. UI 设计规范

**请参考原型图实现，原型图![原型图](./image.png)**

### 4.1 任务列表入口按钮

-   位置：主界面右上角或侧边栏
-   样式：遵循 Ant Design 设计规范
-   状态指示：显示运行中任务数量的徽章

### 4.2 抽屉面板设计

-   宽度：建议 600px
-   标题：今日任务
-   内容区域：任务统计 + 任务列表

### 4.3 任务列表项设计

-   商家头像 + 商家名称 + 商家 ID
-   任务状态标识（进行中/成功/失败）
-   创建时间
-   操作按钮（查看结果），按钮点击后关闭抽屉面板并发送消息

## 5. 技术实现

### 5.1 前端技术栈

-   React + TypeScript
-   Ant Design 组件库
-   状态管理：使用 useRef 避免不必要的重渲染

### 5.2 关键实现点

1. **实时状态更新**：定时轮询，基于 useRequest 实现，轮询间隔为 2s
2. **抽屉面板**：使用 Ant Design 的 Drawer 组件
3. **状态管理**：合理使用 useState 和 useRef
4. **错误处理**：优雅降级，显示原始内容而非空白状态
