# 知识详情页面 PRD

## 接口文档

接口定义详见 `apiSpec/autoGeneratedKnowledge.d.ts`，

## 1. 功能概述

知识详情页面主要展示单个知识库的详细信息,包括知识库基本信息、文档列表以及相关操作功能。

## 2. 页面模块

-   是一个 CRUD 页面，整体基于 ahooks 的 useAntdTable 实现
-   原型图：![image](./image.png)

### 2.1 页面顶部按钮组

-   上传知识按钮：点击跳转至【上传知识页面】, window.open('/knowledge/upload')
-   修改信息按钮
    -   点击触发修改信息弹窗（新增组件 ModifyKnowledgeInfoModal），原型图：![image](./image2.png)
    -   知识库简介修改，textArea
    -   业务线修改，select

### 2.2 搜索框模块

-   支持 3 个筛选项
    -   input 框，placeholder 为<按原文档名称/名称关键词/URL>搜索
    -   支持按工单四级归档目录下拉搜索（新增组件 ArchiveTreeSelect），placeholder 为<请选择需要搜索的目录>
        -   使用 antd TreeSelect 组件，支持多选&只支持选择四级节点
        -   数据源通过接口：/manage/dataset/archive/child 获取，为懒加载
        -   加载一级节点时，请求参数传 datasetId，即知识库 id
        -   加载非一级节点时，请求参数传 id，即父节点 id
    -   input 框，placeholder 为<请选择最新更新人(输入 mis 号)>
-   按钮组
    -   搜索按钮：触发请求 /manage/dataset/data/list，更新 Table 表格数据
    -   清空按钮：点击清空表单内容

### 2.3 知识库列表模块

-   Table 表格分页展示
-   表格字段：
    -   名称
    -   来源
    -   最近更新人, 格式为<用户名(mis 号)>
    -   更新时间, 格式为 yyyy-mm-dd hh:mm:ss
    -   是否引用文档，<是/否>
    -   标签
    -   操作
        -   查看切片：唤起右侧抽屉面板，原型图：![image](./image4.png)，使用 antd Drawer 组件
        -   删除文档：二次确认弹窗，原型图：![image](./image3.png)
