# 知识上传页面

## 页面概述

知识上传页面用于用户上传和管理知识文档,分为四个步骤 知识上传=>文档解析 loading=>知识切片预览=>知识入库

## 接口文档

接口定义详见 `apiSpec/autoGeneratedKnowledge.d.ts`，

## 功能模块

### 1. 知识上传

#### 1.1 上传配置模块

-   参考原型图样式布局实现: ![image](./image.png)
-   业务线及文档链接 容器：
    -   业务线选择: select 单选
    -   文档链接: input 输入框。输入完成后
-   文档链接配置 容器：
    -   自动更新：switch 开关
    -   上传子文档: checkbox 勾选，勾选之后发起请求解析子文档列表, 使用 /manage/wiki/child 接口
    -   上传引用文档: checkbox 勾选
-   子文档列表 容器（勾选上传子文档后展示）：
    -   子文档列表: 表格形式展示上传的文件信息,包含以下字段:
    -   勾选项（可全选）
    -   文档名称

#### 1.2 添加文档

-   添加文档：使用 /manage/wiki/detail 接口获取文档标题后，追加该行数据到上传列表中展示，同时清空当前上传配置模块表单值

#### 1.3 上传列表展示

表格形式展示上传的文件信息,包含以下字段:

-   业务线
-   文件名称
-   是否自动更新
-   是否子文档
-   是否上传引用文档
-   操作(删除)

#### 1.4 开始上传

-   按钮点击后，使用 /manage/dataset/data/import/files 对上传列表的数据进行上传，同时进入下一步展示 文档解析进度

### 2. 文档质检

-   基于 useRequest 轮询后端接口，更新进度条，使用 /manage/dataset/data/import/status 接口
-   进度条展示
    -   文案：知识上传解析中，请耐心等待（<percent>）
    -   进度条: 根据<percent>展示横向进度条
-   进度条解析完成后，展示质检页面使用 /manage/dataset/data/import/wiki/list 接口获取已上传的 wiki 列表并在左侧展示，这个接口是需要分页的，请基于 ahooks 的 useInfiniteScroll 滚动到底部自动加载并追加下一页数据
-   点击任意 wiki，并发请求 wiki 内容质检接口 和 wiki 格式质检接口并将结果展示在右侧。这两个接口都基于 useRequest 进行轮询

### 3. 知识切片预览

-   原型图: ![image](./image2.png)
-   左侧为知识库列表（分页加载），使用 /manage/dataset/data/import/wiki/list 接口
-   右侧为知识切片预览（分页加载），使用 /manage/dataset/data/import/fragment/list 接口
-   点击任意知识库卡片，右侧展示该知识库的知识切片预览
-   点击下一步按钮，进入下一步展示 知识入库确认
-   点击上一步按钮，返回到第一步 知识上传

### 4. 知识入库确认

-   原型图: ![image](./image3.png)
-   展示知识入库<成功提示信息>, 使用 /manage/dataset/data/import/apply 接口
-   显示成功入库的知识切片数量
-   提供知识库查看入口

## 技术实现要点
