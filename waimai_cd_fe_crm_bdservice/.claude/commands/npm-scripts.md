# NPM Scripts Assistant

Help me with NPM scripts: $ARGUMENTS

## Task

I'll help you work with package.json scripts by:

1. Analyzing existing npm scripts in your project
2. Creating new scripts or modifying existing ones
3. Explaining what specific scripts do
4. Suggesting improvements or optimizations
5. Troubleshooting script execution issues

## Process

I'll follow these steps:

1. Examine your package.json file to understand current scripts
2. Analyze dependencies and devDependencies for available tools
3. Identify common patterns and conventions in your scripts
4. Imple<PERSON> requested changes or create new scripts
5. Provide explanations of how the scripts work
6. Test scripts when possible to verify functionality

## Common Script Types I Can Help With

-   Build processes (webpack, rollup, esbuild, etc.)
-   Development servers and hot reloading
-   Testing (unit, integration, e2e)
-   Linting and code formatting
-   Type checking
-   Deployment and CI/CD
-   Database migrations
-   Code generation
-   Environment setup
-   Pre/post hooks for git operations

## Script Optimization Techniques

-   Parallelizing tasks for faster execution
-   Adding cross-platform compatibility
-   Improving error reporting and logging
-   Implementing watch modes for development
-   Creating composite scripts for common workflows
-   Adding appropriate exit codes for CI/CD pipelines

I'll adapt my approach based on your project's specific needs, dependencies, and build tools.
