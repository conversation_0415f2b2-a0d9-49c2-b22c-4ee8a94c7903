# Code Refactoring Assistant

Refactor $ARGUMENTS following modern JavaScript/TypeScript best practices.

## Task

I'll help you refactor code by:

1. Analyzing the current implementation
2. Identifying improvement opportunities
3. Applying modern patterns and practices
4. Maintaining existing functionality
5. Ensuring type safety and test coverage
6. Documenting the changes made

## Process

I'll follow these steps:

1. Examine the code to understand its purpose and structure
2. Identify code smells, anti-patterns, or outdated approaches
3. Plan the refactoring strategy with clear goals
4. Implement changes incrementally while maintaining behavior
5. Verify refactored code with tests
6. Document improvements and benefits

## Refactoring Techniques

I can apply various refactoring techniques:

-   Converting to modern JavaScript/TypeScript features
-   Improving type definitions and type safety
-   Extracting reusable functions and components
-   Applying design patterns appropriately
-   Converting callbacks to Promises or async/await
-   Simplifying complex conditionals and loops
-   Removing duplicate code
-   Improving naming and readability
-   Optimizing performance
-   Enhancing error handling

## Modern Practices I Can Apply

-   ES modules and import/export syntax
-   Optional chaining and nullish coalescing
-   Array and object destructuring
-   Spread and rest operators
-   Template literals
-   Arrow functions
-   Class fields and private methods
-   TypeScript utility types
-   Functional programming patterns
-   React hooks (for React components)

I'll ensure the refactored code maintains compatibility with your project's requirements while improving quality and maintainability.
