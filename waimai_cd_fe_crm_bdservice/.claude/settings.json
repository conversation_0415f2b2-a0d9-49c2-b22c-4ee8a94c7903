{"permissions": {"allow": ["<PERSON><PERSON>", "Edit", "MultiEdit", "Write", "Bash(npm:*)", "Bash(yarn:*)", "Bash(npx:*)", "Bash(node:*)", "Bash(git:*)", "Bash(eslint:*)", "<PERSON><PERSON>(prettier:*)", "<PERSON><PERSON>(tsc:*)", "<PERSON><PERSON>(jest:*)", "Bash(vitest:*)", "Bash(webpack:*)", "Bash(vite:*)"], "deny": ["<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(rm -rf:*)"], "defaultMode": "allowEdits"}, "env": {"BASH_DEFAULT_TIMEOUT_MS": "60000", "BASH_MAX_OUTPUT_LENGTH": "20000", "CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR": "1", "NODE_ENV": "development"}, "includeCoAuthoredBy": true, "cleanupPeriodDays": 30, "hooks": {"PreToolUse": [{"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "jq -r '\"\\(.tool_input.command) - \\(.tool_input.description // \"No description\")\"' >> ~/.claude/bash-command-log.txt"}]}, {"matcher": "Write", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"); CONTENT=$(echo $STDIN_JSON | jq -r '.tool_input.content // \"\"); if [[ \"$FILE\" =~ \\.(js|jsx|ts|tsx)$ ]] && echo \"$CONTENT\" | grep -q 'console\\.log'; then echo 'Warning: console.log statements should be removed before committing' >&2; exit 2; fi"}]}, {"matcher": "Write", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"'); if [[ \"$FILE\" == \"package.json\" ]]; then echo 'Checking for vulnerable dependencies...'; npm audit --audit-level=moderate; fi", "timeout": 60}]}], "PostToolUse": [{"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"'); if [[ \"$FILE\" =~ \\.(js|jsx|ts|tsx)$ ]]; then npx prettier --write \"$FILE\"; fi", "timeout": 30}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"'); if [[ \"$FILE\" =~ \\.(ts|tsx)$ ]]; then RESULT=$(npx tsc --noEmit 2>&1); if [ $? -ne 0 ]; then echo \"TypeScript errors found: $RESULT\" >&2; exit 2; fi; fi", "timeout": 30}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"'); if [[ \"$FILE\" =~ \\.(js|jsx|ts|tsx)$ ]] && grep -q 'import \\* from' \"$FILE\"; then echo 'Warning: Avoid wildcard imports for better tree-shaking' >&2; exit 2; fi"}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "FILE=$(echo $STDIN_JSON | jq -r '.tool_input.file_path // \"\"'); if [[ \"$FILE\" =~ \\.(js|jsx|ts|tsx)$ && \"$FILE\" != *\".test.\"* && \"$FILE\" != *\".spec.\"* ]]; then DIR=$(dirname \"$FILE\"); BASENAME=$(basename \"$FILE\" | sed -E 's/\\.(js|jsx|ts|tsx)$//'); for TEST_FILE in \"$DIR/$BASENAME.test.\"* \"$DIR/$BASENAME.spec.\"*; do if [ -f \"$TEST_FILE\" ]; then echo \"Running tests for $TEST_FILE...\"; if command -v jest >/dev/null 2>&1; then npx jest \"$TEST_FILE\" --passWithNoTests; elif command -v vitest >/dev/null 2>&1; then npx vitest run \"$TEST_FILE\"; fi; break; fi; done; fi", "timeout": 60}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "echo \"Claude Code notification: $(date)\" >> ~/.claude/notifications.log"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "if [[ -f package.json && $(git status --porcelain | grep -E '\\.js$|\\.jsx$|\\.ts$|\\.tsx$') ]]; then echo 'Running linter on changed files...'; npx eslint $(git diff --name-only --diff-filter=ACMR | grep -E '\\.js$|\\.jsx$|\\.ts$|\\.tsx$'); fi", "timeout": 60}]}, {"matcher": "", "hooks": [{"type": "command", "command": "if [[ -f package.json && $(git status --porcelain | grep -E '\\.js$|\\.jsx$|\\.ts$|\\.tsx$') ]]; then echo 'Analyzing bundle size impact...'; if command -v bundlesize >/dev/null 2>&1; then npx bundlesize; elif npm list webpack-bundle-analyzer >/dev/null 2>&1; then echo 'Use: npx webpack-bundle-analyzer build/static/js/*.js'; else echo 'No bundle analysis tool found'; fi; fi", "timeout": 60}]}]}}