module.exports = {
    env: {
        node: true,
        es2021: true,
        browser: true,
    },
    extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended', 'prettier'],
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
    },
    plugins: ['@typescript-eslint/eslint-plugin', 'eslint-plugin-unused-imports', 'eslint-plugin-react'],
    rules: {
        indent: 'off',
        '@typescript-eslint/indent': 'off',
        quotes: ['error', 'single', { avoidEscape: true }],
        semi: ['error', 'always'],
        'space-infix-ops': ['error', { int32Hint: false }],
        '@typescript-eslint/ban-ts-comment': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        'unused-imports/no-unused-imports': 'error',
        'react/jsx-key': ['error'],
    },
};
