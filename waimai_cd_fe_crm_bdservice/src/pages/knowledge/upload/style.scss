@mixin card-style {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
}

.knowledge-upload {
    padding: 24px;
    margin: 0 auto;

    &__form {
        margin-bottom: 32px;
        padding: 24px;
        border-radius: 8px;
        background-color: #f5f6fa;
        transition: all 0.3s;

        &-container {
            display: flex; 
            gap: 32px;
            flex-wrap: wrap;

            @media screen and (max-width: 768px) {
                flex-direction: column;
                gap: 16px;
            }

            &-title {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 16px;
                color: #333;
            }
        }

        .ant-form-item {
            margin-bottom: 16px;
            flex: 1;
            min-width: 200px;

            &-label {
                font-weight: 500;
            }
        }

        .ant-switch {
            transition: all 0.3s;
        }

        .ant-checkbox-wrapper {
            color: #333;
            transition: all 0.3s;
        }
    }

    &__table {
        @include card-style;
        margin-top: 24px;
        padding: 24px;

        h2 {
            margin-bottom: 24px;
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }

        .ant-table {
            margin-bottom: 24px;
        }

        .ant-table-thead > tr > th {
            background-color: #fafafa;
            font-weight: 500;
        }

        .ant-btn {
            transition: all 0.3s;
        }
    }

    &__loading {
        text-align: center;
        padding: 48px 0;

        &-text {
            margin-top: 16px;
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ant-spin {
            transition: all 0.3s;
        }
    }

    &__preview {
        display: flex;
        gap: 24px;
        margin-top: 24px;
        display: flex;
        flex-direction: column;

        @media screen and (max-width: 768px) {
            flex-direction: column;
        }

        &-left,
        &-right {
            @include card-style;
            flex: 1;
            padding: 24px;
            min-height: 300px;
        }
    }

    &__success {
        text-align: center;
        padding: 48px 0;

        &-icon {
            font-size: 56px;
            color: #52c41a;
            transition: all 0.3s;

            &:hover {
                transform: scale(1.1);
            }
        }

        &-title {
            margin-top: 24px;
            font-size: 24px;
            font-weight: 500;
            color: #333;
        }

        &-desc {
            margin-top: 12px;
            color: #666;
            font-size: 14px;
        }

        &-action {
            margin-top: 32px;

            .ant-btn {
                min-width: 120px;
                height: 40px;
                transition: all 0.3s;

                & + .ant-btn {
                    margin-left: 16px;
                }
            }
        }
    }

    .ant-tag {
        border-radius: 4px;
        padding: 2px 8px;
        font-size: 13px;
        transition: all 0.3s;

        &:hover {
            transform: translateY(-1px);
        }
    }

    .sub-wiki-table {
        .ant-table-row-expand-icon-cell::after {
            content: '';
        }
    }
} 

.preview-fragment-content-tooltip {
    .ant-tooltip-content {
        .ant-tooltip-inner {
            max-height: 500px;
            overflow: scroll;
        }
    }
}