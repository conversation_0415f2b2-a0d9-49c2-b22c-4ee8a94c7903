import React, { useState } from 'react';
import { Steps } from 'antd';
import UploadForm from './components/UploadForm';
import LoadingStatus from './components/qualityInspection/index';
import PreviewContent from './components/PreviewContent';
import SuccessResult from './components/SuccessResult';
import { WikiItem } from './types';

const App: React.FC = () => {
    const [currentStep, setCurrentStep] = useState<number>(0);
    const [uploadList, setUploadList] = useState<WikiItem[]>([]);
    const [batchId, setBatchId] = useState<string>('');
    const [datasetId, setDatasetId] = useState<number>();

    const steps = [{ title: '知识上传' }, { title: '知识质检' }, { title: '知识切片预览' }, { title: '知识入库' }];

    const handleNextStep = () => {
        setCurrentStep(prev => prev + 1);
    };

    const handlePrevStep = (prevSteps = 1) => {
        setCurrentStep(prev => prev - prevSteps);
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return (
                    <UploadForm
                        uploadList={uploadList}
                        setUploadList={setUploadList}
                        onNext={(id: string, selectedDatasetId: number) => {
                            setBatchId(id);
                            setDatasetId(selectedDatasetId);
                            handleNextStep();
                        }}
                    />
                );
            case 1:
                return <LoadingStatus batchId={batchId} onComplete={handleNextStep} onPrev={() => handlePrevStep()} />;
            case 2:
                return <PreviewContent batchId={batchId} onNext={handleNextStep} onPrev={() => handlePrevStep()} />;
            case 3:
                return <SuccessResult batchId={batchId} datasetId={datasetId} />;
            default:
                return null;
        }
    };

    return (
        <div className="knowledge-upload">
            <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
            {renderStepContent()}
        </div>
    );
};

export default App;
