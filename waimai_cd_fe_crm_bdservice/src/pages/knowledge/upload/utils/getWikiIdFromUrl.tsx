import { message } from 'antd';

// 根据wiki链接换取wikiid
export const getWikiIdFromUrl = (url: string) => {
    let urlObj;
    try {
        urlObj = new URL(url);
    } catch (error) {
        message.error('请输入正确的wiki链接格式');
        return;
    }

    const pathname = urlObj.pathname;
    const pathnameArr = pathname.split('/');
    if (!pathnameArr.length) {
        message.error('请输入正确的wiki链接格式');
        return '';
    }
    return pathnameArr[pathnameArr.length - 1];
};
