import { apiCaller } from '@mfe/cc-api-caller-pc';
import { getWikiIdFromUrl } from '../utils/getWikiIdFromUrl';

export const fetchBizLineList = async () => {
    const res = await apiCaller.send('/manage/dataset/list', { pageSize: 100, pageNum: 1 });
    if (res.code !== 0) {
        return [];
    }
    return (res.data.list || []).map((item: any) => ({
        name: item.name,
        datasetId: item.id,
    }));
};

export const fetchWikiDetail = async (id: string) => {
    const res = await apiCaller.get('/manage/wiki/detail', { wikiId: id });
    if (res.code !== 0) {
        return;
    }
    return res.data;
};

export const fetchSubWikiList = async (wikiUrl: string) => {
    const urlObj = new URL(wikiUrl).pathname;
    const params: any = {};
    const id = getWikiIdFromUrl(wikiUrl);
    if (urlObj.startsWith('/collabpage')) {
        // 为普通 wiki
        params.contentId = id;
    } else {
        // 为空间
        const isNaNId = isNaN(Number(id));
        params[isNaNId ? 'spaceKey' : 'spaceId'] = id;
    }
    const res = await apiCaller.send('/manage/wiki/childV2', params);
    if (res.code !== 0) {
        return [];
    }
    return res.data || [];
};

export const startUpload = async (wikiList: any) => {
    const res = await apiCaller.send('/manage/dataset/data/import/files', {
        wikiList,
    });
    if (res.code !== 0) {
        return;
    }
    return res.data.batchId;
};
