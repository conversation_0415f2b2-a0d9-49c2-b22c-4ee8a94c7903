import { APISpec } from '@mfe/cc-api-caller-pc';

export interface WikiItem {
    wikiId: number | string;
    title: string;
    autoUpdate: boolean;
    needSubWiki: boolean;
    needReferWiki: boolean;
    syncWikiAuth: boolean;
    type: 'wiki' | 'sub_wiki' | 'refer_wiki';
    datasetId: number;
}

export interface UploadFormData {
    bizLine: string;
    url: string;
    autoUpdate: boolean;
    needSubWiki: boolean;
    needReferWiki: boolean;
    syncWikiAuth: boolean;
}

export type BizLineResponse = APISpec['/manage/dataset/bizline']['response'];
export type WikiDetailResponse = APISpec['/manage/wiki/detail']['response'];
export type WikiChildResponse = APISpec['/manage/wiki/childV2']['response'];

export enum UploadWikiEnum {
    wiki = 'wiki',
    subWiki = 'sub_wiki',
    referWiki = 'refer_wiki',
}
