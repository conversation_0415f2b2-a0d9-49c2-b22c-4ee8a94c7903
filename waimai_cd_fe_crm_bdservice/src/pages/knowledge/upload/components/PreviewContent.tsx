import React, { useState } from 'react';
import { Card, List, Button, Space, Tooltip } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';

interface PreviewContentProps {
    batchId: string;
    onNext: () => void;
    onPrev: () => void;
}

interface WikiListResponse {
    wikiList: Array<{
        wikiId: number;
        title: string;
    }>;
    total: number;
}

interface FragmentListResponse {
    fragmentList: string[];
    total: number;
}

const PreviewContent: React.FC<PreviewContentProps> = ({ batchId, onNext, onPrev }) => {
    const [selectedWiki, setSelectedWiki] = useState<{
        wikiId: number;
        datasetId: number;
    }>();
    const [wikiCurrentPage, setWikiCurrentPage] = useState(1);
    const [fragmentCurrentPage, setFragmentCurrentPage] = useState(1);

    const { data: wikiListData = { wikiList: [], total: 0 } } = useRequest(
        async () => {
            const res = await apiCaller.get('/manage/dataset/data/import/wiki/list', {
                batchId,
                pageNum: wikiCurrentPage,
                pageSize: 20,
            });

            if (res.code !== 0) {
                return { wikiList: [], total: 0 };
            }

            return {
                wikiList: res.data.wikiList,
                total: res.data.total,
            };
        },
        {
            refreshDeps: [wikiCurrentPage],
            onSuccess: data => {
                if (data.wikiList && data.wikiList.length > 0 && !selectedWiki) {
                    const firstWiki = data.wikiList[0];
                    setTimeout(() => {
                        setSelectedWiki({
                            wikiId: firstWiki.wikiId,
                            datasetId: firstWiki.datasetId,
                        });
                    }, 300);
                }
            },
        },
    );

    const { data: fragmentListData = { fragmentList: [], total: 0 } } = useRequest<FragmentListResponse, [void]>(
        async () => {
            if (!selectedWiki) return { fragmentList: [], total: 0 };

            const res = await apiCaller.send('/manage/dataset/data/import/fragment/list', {
                batchId,
                wikiId: selectedWiki.wikiId,
                datasetId: selectedWiki.datasetId,
                pageNum: fragmentCurrentPage,
                pageSize: 20,
            });

            if (res.code !== 0) {
                return { fragmentList: ['测试分片1', '测试分片2'], total: 2 };
            }

            return {
                fragmentList: res.data.fragmentList,
                total: res.data.total,
            };
        },
        {
            refreshDeps: [selectedWiki, fragmentCurrentPage],
        },
    );

    return (
        <div className="knowledge-upload__preview">
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div className="knowledge-upload__preview-left">
                    <List
                        dataSource={wikiListData.wikiList}
                        renderItem={(item: any) => (
                            <Card
                                hoverable
                                style={{
                                    marginBottom: 16,
                                    borderColor: item.wikiId === selectedWiki?.wikiId ? '#1890ff' : undefined,
                                }}
                                onClick={() =>
                                    setSelectedWiki({
                                        wikiId: item.wikiId,
                                        datasetId: item.datasetId,
                                    })
                                }
                            >
                                <div style={{ wordBreak: 'break-all' }}>{item.title}</div>
                            </Card>
                        )}
                        pagination={{
                            current: wikiCurrentPage,
                            pageSize: 20,
                            total: wikiListData.total,
                            onChange: setWikiCurrentPage,
                        }}
                    />
                </div>
                <div className="knowledge-upload__preview-right" style={{ marginLeft: '50px' }}>
                    <List
                        dataSource={fragmentListData.fragmentList}
                        renderItem={item => (
                            <Card style={{ marginBottom: 16 }}>
                                <Tooltip
                                    title={item}
                                    placement="bottomRight"
                                    rootClassName="preview-fragment-content-tooltip"
                                >
                                    <div
                                        style={{
                                            wordBreak: 'break-all',
                                            WebkitLineClamp: 5,
                                            display: '-webkit-box',
                                            overflow: 'hidden',
                                            WebkitBoxOrient: 'vertical',
                                        }}
                                    >
                                        {item}
                                    </div>
                                </Tooltip>
                            </Card>
                        )}
                        pagination={{
                            current: fragmentCurrentPage,
                            pageSize: 20,
                            total: fragmentListData.total,
                            onChange: setFragmentCurrentPage,
                        }}
                    />
                </div>
            </div>
            <Space style={{ justifyContent: 'flex-end' }}>
                <Button onClick={onPrev}>上一步</Button>
                <Button type="primary" onClick={onNext}>
                    下一步
                </Button>
            </Space>
        </div>
    );
};

export default PreviewContent;
