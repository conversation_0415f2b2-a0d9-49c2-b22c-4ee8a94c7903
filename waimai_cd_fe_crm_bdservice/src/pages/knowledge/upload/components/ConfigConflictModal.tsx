import React, { useEffect, useMemo } from 'react';
import { Modal, Radio, Space, Typography, Tag, Divider } from 'antd';
import type { WikiItem } from '../types';
import { YesTextTag, NoTextTag } from './UploadForm';

interface ConfigConflictModalProps {
    open: boolean;
    onCancel: () => void;
    onOk: (selectedConfigs: { [key: string]: WikiItem }) => void;
    conflictItems: WikiItem[];
}

interface GroupedConflictItems {
    [key: string]: {
        title: string;
        datasetId: number;
        items: WikiItem[];
    };
}

const ConfigConflictModal: React.FC<ConfigConflictModalProps> = ({ open, onCancel, onOk, conflictItems }) => {
    const [selectedConfigIndexMap, setSelectedConfigIndexMap] = React.useState<{ [key: string]: number }>({});

    // 按文档ID和业务线分组
    const groupedConflictItems = useMemo(() => {
        return conflictItems.reduce<GroupedConflictItems>((acc, item) => {
            const key = `${item.wikiId}-${item.datasetId}`;
            if (!acc[key]) {
                acc[key] = {
                    title: item.title,
                    datasetId: item.datasetId,
                    items: [],
                };
            }
            acc[key].items.push(item);
            return acc;
        }, {});
    }, [conflictItems]);

    // 初始化选中状态
    useEffect(() => {
        const initialSelectedMap = Object.keys(groupedConflictItems).reduce((acc, key) => {
            acc[key] = 0;
            return acc;
        }, {} as { [key: string]: number });
        setSelectedConfigIndexMap(initialSelectedMap);
    }, [groupedConflictItems]);

    const handleOk = () => {
        // 构建每个文档的选中配置
        const selectedConfigs = Object.entries(groupedConflictItems).reduce((acc, [key, group]) => {
            const selectedIndex = selectedConfigIndexMap[key] || 0;
            acc[key] = group.items[selectedIndex];
            return acc;
        }, {} as { [key: string]: WikiItem });

        onOk(selectedConfigs);
    };

    return (
        <Modal
            title="配置冲突"
            open={open}
            onCancel={onCancel}
            onOk={handleOk}
            width={800}
            okText="确认"
            cancelText="取消"
        >
            <Typography.Paragraph>检测到以下文档存在不同配置，请为每个文档选择要使用的配置：</Typography.Paragraph>
            <Space direction="vertical" style={{ width: '100%', height: '500px', overflowY: 'scroll' }}>
                {Object.entries(groupedConflictItems).map(([key, group], groupIndex) => (
                    <div key={key} style={{ width: '100%' }}>
                        {groupIndex > 0 && <Divider />}
                        <Typography.Title level={5} style={{ marginBottom: 16 }}>
                            文档：{group.title}
                        </Typography.Title>
                        <Radio.Group
                            value={selectedConfigIndexMap[key] || 0}
                            onChange={e => {
                                setSelectedConfigIndexMap(prev => ({
                                    ...prev,
                                    [key]: e.target.value,
                                }));
                            }}
                        >
                            <Space direction="vertical" style={{ width: '100%', paddingLeft: 16 }}>
                                {group.items.map((item, index) => (
                                    <div key={index}>
                                        <Typography.Text style={{ marginBottom: 8, display: 'block' }}>
                                            {index === 0 ? (
                                                <Tag color="#87d068">新配置</Tag>
                                            ) : (
                                                <Tag color="#108ee9">原配置</Tag>
                                            )}
                                        </Typography.Text>
                                        <Radio
                                            value={index}
                                            style={{
                                                padding: '12px',
                                                border: '1px solid #f0f0f0',
                                                borderRadius: '8px',
                                                width: '100%',
                                                marginRight: 0,
                                            }}
                                        >
                                            <Space direction="vertical" style={{ width: '100%' }}>
                                                <Space>
                                                    <span>
                                                        自动更新：{item.autoUpdate ? <YesTextTag /> : <NoTextTag />}
                                                    </span>
                                                    <span>
                                                        上传子文档：{item.needSubWiki ? <YesTextTag /> : <NoTextTag />}
                                                    </span>
                                                    <span>
                                                        上传引用文档：
                                                        {item.needReferWiki ? <YesTextTag /> : <NoTextTag />}
                                                    </span>
                                                    <span>
                                                        同步知识库权限：
                                                        {item.syncWikiAuth ? <YesTextTag /> : <NoTextTag />}
                                                    </span>
                                                </Space>
                                            </Space>
                                        </Radio>
                                    </div>
                                ))}
                            </Space>
                        </Radio.Group>
                    </div>
                ))}
            </Space>
        </Modal>
    );
};

export default ConfigConflictModal;
