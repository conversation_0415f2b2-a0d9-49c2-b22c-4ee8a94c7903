import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { apiCaller } from '@mfe/cc-api-caller-pc';

interface SuccessResultProps {
    batchId: string;
    datasetId?: number;
}

const SuccessResult: React.FC<SuccessResultProps> = ({ batchId, datasetId }) => {
    const [tips, setTips] = useState('');
    const [isSuccess, setIsSuccess] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const applyImport = async () => {
            try {
                const res = await apiCaller.send('/manage/dataset/data/import/apply', {
                    batchId,
                });

                if (res.code !== 0) {
                    setIsSuccess(false);
                    setTips(res.msg || '知识入库失败');
                    return;
                }

                setIsSuccess(true);
                setTips(res.data.tips);
            } finally {
                setLoading(false);
            }
        };

        applyImport();
    }, [batchId]);

    const handleViewKnowledge = () => {
        if (datasetId) {
            window.location.href = `./detail/${datasetId}`;
        } else {
            window.location.href = './home';
        }
    };

    if (loading) {
        return (
            <div className="knowledge-upload__success" style={{ textAlign: 'center' }}>
                <Spin size="large" tip="知识入库中..." />
            </div>
        );
    }

    return (
        <div className="knowledge-upload__success">
            {isSuccess ? (
                <CheckCircleOutlined className="knowledge-upload__success-icon" style={{ fontSize: 48 }} />
            ) : (
                <CloseCircleOutlined
                    className="knowledge-upload__error-icon"
                    style={{ fontSize: 48, color: '#ff4d4f' }}
                />
            )}
            <div className="knowledge-upload__success-title">{isSuccess ? '知识入库成功' : '知识入库失败'}</div>
            <div className="knowledge-upload__success-desc">{tips}</div>
            <div className="knowledge-upload__success-action">
                {isSuccess && (
                    <Button type="primary" onClick={handleViewKnowledge}>
                        查看知识库
                    </Button>
                )}
            </div>
        </div>
    );
};

export default SuccessResult;
