import React from 'react';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
const CustomExpandIcon = ({ expanded, onExpand, record }) => {
    if (!record.childCount || record.childCount === 0) {
        return null;
    }
    return expanded ? (
        <DownOutlined style={{ fontSize: 12 }} onClick={e => onExpand(record, e)} />
    ) : (
        <RightOutlined style={{ fontSize: 12 }} onClick={e => onExpand(record, e)} />
    );
};

export default CustomExpandIcon;
