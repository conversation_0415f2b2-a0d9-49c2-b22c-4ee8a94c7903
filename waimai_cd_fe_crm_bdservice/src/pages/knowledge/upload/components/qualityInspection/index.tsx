import React, { useEffect, useRef, useState } from 'react';
import {
    Progress,
    Modal,
    Table,
    Spin,
    message,
    Card,
    Row,
    Col,
    Empty,
    Typography,
    Button,
    Space,
    Divider,
    Tag,
    Tooltip,
} from 'antd';
import { useRequest, useInfiniteScroll } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import './index.scss';
import ReactMarkdown from 'react-markdown';
import { QuestionCircleFilled } from '@ant-design/icons';

const { Title, Text } = Typography;

interface LoadingStatusProps {
    batchId: string;
    onComplete: (step?: number) => void;
    onPrev: () => void;
}

interface FailWikiItem {
    wikiId: number;
    title: string;
    url: string;
}

interface QualityCheckResult {
    formatResult: Array<FormatResultItem>;
    contentResult: string;
}

interface FormatResultItem {
    /** 标题 */
    title: string;
    /** 描述 */
    message: string;
    /** 路径 */
    path: string | null;
    /** 节点ID */
    nodeId: string;
    /** 级别 */
    level: number;
}

const getFormatProblemLevel = (level: number) => {
    if (level === 0) {
        return <Tag color="rgb(244, 81, 30)">P0</Tag>;
    } else if (level === 1) {
        return <Tag color="rgb(255, 160, 0)">P1</Tag>;
    } else if (level === 2) {
        return <Tag color="rgb(251, 187, 27)">P2</Tag>;
    } else if (level === 3) {
        return <Tag color="rgb(25, 118, 210)">P3</Tag>;
    }

    return null;
};

const LoadingStatus: React.FC<LoadingStatusProps> = ({ batchId, onComplete, onPrev }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [failWikiList, setFailWikiList] = useState<FailWikiItem[]>([]);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [isParsingCompleted, setIsParsingCompleted] = useState(false);
    const [selectedWikiId, setSelectedWikiId] = useState<number | null>(null);
    const [isFormatChecking, setIsFormatChecking] = useState(false);
    const [isContentChecking, setIsContentChecking] = useState(false);
    const [qualityCheckResult, setQualityCheckResult] = useState<QualityCheckResult>({
        formatResult: [],
        contentResult: '',
    });

    const wikiListContainerRef = useRef<HTMLDivElement>(null);

    const columns = [
        {
            title: '知识ID',
            dataIndex: 'wikiId',
            key: 'wikiId',
        },
        {
            title: '知识标题',
            dataIndex: 'title',
            key: 'title',
        },
        {
            title: '知识链接',
            dataIndex: 'url',
            key: 'url',
            render: (url: string) => (
                <a href={url} target="_blank" rel="noopener noreferrer">
                    {url}
                </a>
            ),
        },
    ];

    // 解析进度轮询
    const {
        data: progressData,
        cancel: cancelProgressCheck,
        run: runProgressCheck,
    } = useRequest(
        async () => {
            const res = await apiCaller.get('/manage/dataset/data/import/progress', { batchId });

            if (res.code !== 0) {
                return { percent: 0, failWikiList: [] };
            }

            if (res.data.percent === 100) {
                cancelProgressCheck();
                setIsParsingCompleted(true);
                reloadWikiList();
                setTimeout(() => {
                    if (res.data.failWikiList?.length > 0) {
                        setFailWikiList(res.data.failWikiList);
                        setIsModalVisible(true);
                    }
                }, 100);
            }
            return res.data;
        },
        {
            pollingInterval: 1000,
            pollingWhenHidden: false,
        },
    );

    const getWikiList = async (pageNum: number) => {
        const res = await apiCaller.get('/manage/dataset/data/import/wiki/list', {
            batchId,
            pageNum,
            pageSize: 50,
        });

        if (res.code !== 0) {
            throw new Error('获取Wiki列表失败');
        }

        return {
            list: res.data.wikiList || [],
            total: res.data.total || 0,
            hasMore: res?.data?.wikiList?.length === 50,
        };
    };

    const {
        data: wikiListData,
        loadingMore: wikiListLoading,
        noMore: wikiListNoMore,
        reload: reloadWikiList,
    } = useInfiniteScroll(
        d => {
            const pageNum = d ? Math.ceil(d.list.length / 50) + 1 : 1;
            return getWikiList(pageNum);
        },
        {
            target: wikiListContainerRef,
            isNoMore: d => !d?.hasMore,
            manual: true,
        },
    );

    // 自动选中第一个文档
    useEffect(() => {
        if (wikiListData?.list?.length && !selectedWikiId) {
            setSelectedWikiId(wikiListData.list[0].wikiId);
        }
    }, [wikiListData?.list]);

    // 格式质检轮询
    const { run: runFormatCheck, cancel: cancelFormatCheck } = useRequest(
        async () => {
            if (!selectedWikiId) return null;

            const res = await apiCaller.send('/manage/dataset/wiki/format/check', { wikiId: selectedWikiId });

            if (res.code !== 0) {
                cancelFormatCheck();
                setIsFormatChecking(false);
                return null;
            }

            if (res.data.finish) {
                cancelFormatCheck();
                setIsFormatChecking(false);
                setQualityCheckResult(prev => ({
                    ...prev,
                    formatResult: (res.data?.infos || []).sort((a, b) => a.level - b.level),
                }));
            }
        },
        {
            pollingInterval: 1000,
            pollingWhenHidden: false,
            manual: true,
        },
    );

    // 内容质检轮询
    const { run: runContentCheck, cancel: cancelContentCheck } = useRequest(
        async () => {
            if (!selectedWikiId) return null;

            const res = await apiCaller.send('/manage/dataset/wiki/content/check', { wikiId: selectedWikiId });

            if (res.code !== 0) {
                cancelContentCheck();
                setIsContentChecking(false);
                return null;
            }

            if (res.data.finish) {
                cancelContentCheck();
                setIsContentChecking(false);
                setQualityCheckResult(prev => ({
                    ...prev,
                    contentResult: res.data?.content,
                }));
            }
        },
        {
            pollingInterval: 1000,
            pollingWhenHidden: false,
            manual: true,
        },
    );

    useEffect(() => {
        if (selectedWikiId) {
            setIsFormatChecking(true);
            // setIsContentChecking(true);
            // 重置质检结果
            setQualityCheckResult({
                formatResult: [],
                contentResult: '',
            });
            // 开始新的质检
            runFormatCheck();
            // runContentCheck();
        }
    }, [selectedWikiId]);

    const handleModalClose = async () => {
        try {
            setConfirmLoading(true);
            const exportRes = await apiCaller.get('/manage/dataset/data/import/importFail/export', {
                batchId,
            });
            if (exportRes.code === 0 && exportRes.data) {
                window.open(exportRes.data, '_blank');
            }
            const res = await apiCaller.get('/manage/dataset/data/import/wiki/list', {
                batchId,
                pageNum: 1,
                pageSize: 10,
            });

            if (res.code !== 0) {
                return;
            }

            if (!res.data?.total) {
                message.error('请给it_bdassistant添加文档管理权限，否则将无法上传');
                onPrev();
                return;
            } else {
                setIsModalVisible(false);
            }
        } catch (error) {
            console.error('获取导入列表失败:', error);
        } finally {
            setConfirmLoading(false);
        }
    };

    const handleNextStep = () => {
        onComplete();
    };

    // 解析中的进度条状态
    if (!isParsingCompleted) {
        return (
            <>
                <div className="knowledge-upload__loading">
                    <Progress type="circle" percent={progressData?.percent || 0} />
                    <div className="knowledge-upload__loading-text">
                        知识上传解析中，请耐心等待（{progressData?.percent || 0}%）
                        <Spin style={{ marginLeft: 8 }} spinning />
                    </div>
                </div>
            </>
        );
    }

    // 渲染内容检验结果
    const renderQualityCheckResult = (type: 'format' | 'content') => {
        const loding = type === 'format' ? isFormatChecking : isContentChecking;
        const result = type === 'format' ? qualityCheckResult.formatResult : qualityCheckResult.contentResult;
        if (loding) {
            return <Spin tip="检验中..." />;
        }

        if (!result || !result?.length) {
            return <span style={{ color: '#87d068' }}>未发现{type === 'format' ? '格式' : '内容'}问题</span>;
        }

        return (
            <div className="content-result">
                {type === 'content' ? (
                    <ReactMarkdown
                        children={result as string}
                        components={{
                            a: props => {
                                const { href, children } = props;
                                return (
                                    <a
                                        href={href} // 链接，如果该项有内容则按链接展示
                                        target="_blank"
                                    >
                                        {children}
                                    </a>
                                );
                            },
                        }}
                    />
                ) : (
                    ((result || []) as FormatResultItem[]).map(item => {
                        return (
                            <div key={item.nodeId} style={{ marginBottom: 20 }}>
                                <div
                                    style={{
                                        fontWeight: 500,
                                        fontSize: 18,
                                        marginBottom: 2,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <div style={{ marginRight: 10 }}>{item.title}</div>
                                    {getFormatProblemLevel(item.level)}
                                </div>
                                <div style={{ color: 'rgb(175 175 175)' }}>{item.message}</div>
                            </div>
                        );
                    })
                )}
            </div>
        );
    };

    // 质检状态
    return (
        <div className="knowledge-upload__quality-check">
            <Row gutter={16} className="wiki-list-row">
                <Col span={10} className="wiki-list-col">
                    <Card
                        title="文档名称"
                        className="wiki-list-card"
                        extra={
                            <div>
                                文档数量：{wikiListData?.total || 0}
                                <Tooltip title="数量包含父文档及其所有次级子文档">
                                    <QuestionCircleFilled style={{ marginLeft: 5 }} />
                                </Tooltip>
                            </div>
                        }
                    >
                        <div className="wiki-list-container" ref={wikiListContainerRef}>
                            {wikiListData?.list?.length ? (
                                wikiListData.list.map(wiki => (
                                    <Card
                                        key={wiki.wikiId}
                                        className={`wiki-item ${selectedWikiId === wiki.wikiId ? 'selected' : ''}`}
                                        onClick={() => setSelectedWikiId(wiki.wikiId)}
                                    >
                                        <div className="wiki-title">
                                            <span
                                                style={{
                                                    maxWidth: 'calc(100% - 100px)',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                }}
                                            >
                                                {wiki.title}
                                            </span>
                                            {wiki.hasFormatProblem ? <Tag color="red">有格式问题</Tag> : null}
                                        </div>
                                    </Card>
                                ))
                            ) : (
                                <Empty description="暂无数据" />
                            )}
                            {wikiListLoading && (
                                <div className="loading-more">
                                    <Spin tip="加载更多..." />
                                </div>
                            )}
                            {wikiListNoMore && <div className="wiki-list-no-more">没有更多了~~</div>}
                        </div>
                    </Card>
                </Col>
                <Col span={14} className="wiki-list-col quality-section-col">
                    {selectedWikiId ? (
                        <>
                            <div className="quality-section">
                                <div className="quality-section-title">格式检验结果</div>
                                <Divider style={{ margin: '5px 0 12px 0' }} />
                                {renderQualityCheckResult('format')}
                            </div>
                            {/* <div className="quality-section">
                                <div className="quality-section-title">内容检验结果</div>
                                <Divider style={{ margin: '5px 0 12px 0' }} />
                                {renderQualityCheckResult('content')}
                            </div> */}
                        </>
                    ) : (
                        <Empty description="请选择一个知识库进行质检" />
                    )}
                    <Space style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                        <Button
                            style={{ width: 300, marginTop: 10 }}
                            type="primary"
                            onClick={() => {
                                const url = wikiListData?.list?.find(item => item.wikiId === selectedWikiId)?.url;
                                if (url) {
                                    window.open(url, '_blank');
                                }
                            }}
                        >
                            去修改
                        </Button>
                    </Space>
                </Col>
            </Row>

            <Space style={{ justifyContent: 'flex-end', width: '100%', marginTop: 30 }}>
                <Button onClick={onPrev}>上一步</Button>
                <Button type="primary" onClick={handleNextStep}>
                    下一步
                </Button>
            </Space>

            <Modal
                title={
                    <div>
                        上传失败列表
                        <span style={{ fontSize: 12, color: 'rgb(179 179 179)' }}>
                            {' '}
                            您可下载附件查看需授权it_bdaiassistant
                            的文档清单。授权后可重新上传。如文档为空，请先补充内容再上传。
                        </span>
                    </div>
                }
                closeIcon={null}
                open={isModalVisible}
                onOk={handleModalClose}
                onCancel={handleModalClose}
                width={800}
                maskClosable={false}
                cancelButtonProps={{ style: { display: 'none' } }}
                confirmLoading={confirmLoading}
                keyboard={false}
                okText="下载失败列表"
            >
                <Table
                    columns={columns}
                    dataSource={failWikiList}
                    rowKey="wikiId"
                    pagination={false}
                    scroll={{ y: 400 }}
                />
            </Modal>
        </div>
    );
};

export default LoadingStatus;
