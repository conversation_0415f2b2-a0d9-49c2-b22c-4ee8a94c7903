.knowledge-upload__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;

  .ant-progress {
    width: 80%;
    max-width: 600px;
    margin-bottom: 20px;
  }

  &-text {
    font-size: 16px;
    color: #666;
    display: flex;
    align-items: center;
  }
}

.knowledge-upload__quality-check {
  padding: 20px 0;

  .wiki-list-row {
    height: 100%;
  }

  .wiki-list-col {
    height: 100%;
  }

  .wiki-list-card {
    height: 600px;

    .ant-card-body {
      padding: 20px;
    }
  }

  .wiki-list-container {
    height: 480px;
    overflow-y: auto;
    padding-right: 10px;

    .wiki-item {
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &.selected {
        border-color: rgb(24, 144, 255);
      }

      .wiki-title {
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .wiki-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999;
      }
    }

    .loading-more {
      text-align: center;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8));
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0 -12px;
      padding: 20px 0;
      margin-top: 30px;

      .ant-spin {
        .ant-spin-dot-item {
          background-color: #FFCC33;
        }
      }
    }

    .wiki-list-no-more {
      text-align: center;
      color: #d3d5dc;
      padding: 12px 0;
    }
  }

  .quality-section-col {
    max-height: 600px;
    overflow: auto;
  }

  .quality-section {
    margin-bottom: 24px;

    .quality-section-title {
      font-size: 20px;
      font-weight: bold;
      position: relative;
      padding-left: 10px;
      margin-bottom: 10px;
      display: flex;
      align-items: baseline;

      &::before {
        position: absolute;
        background-color: #ffc34d;
        width: 4px;
        height: 24px;
        top: 3px;
        left: 0;
        content: "";
        display: inline-block;
      }
    }

    .content-result {
      overflow-y: auto;
    }
  }
}