import { Row, Image, Space, Popover, Divider } from 'antd';
import useToolbar from '../../service/toolbar';
import Association from './association';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import Condition from '@src/components/Condition/Condition';
import ToolbarImg from '@src/assets/images/chat/menu.png';
import DragImg from '@src/assets/images/chat/drag.png';
import { useEffect, useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { OptionItem } from '../../type/message';
import useCallerRequest from '../../service/request';
import useTrace from '../../service/trace';
import useSceneTips from '../../service/sceneTips';
import useSendMessage, { EntryPoint } from '../../service/sendMessage/sendMessage';
import ArrowRightImg from '@src/assets/images/chat/arrow_right.png';
import useAnnouncement from '../../service/announcement';
import AnnouncementIcon from '@src/assets/images/chat/announcement.png';
import AnnouncementCloseIcon from '@src/assets/images/chat/announcement_close.png';
import AnnouncementCollapseIcon from '@src/assets/images/chat/announcement_collapse.png';
import AnnouncementExpandIcon from '@src/assets/images/chat/announcement_expand.png';

const Toolbar = ({ style = {} }) => {
    const { options, onToolbarPress, refreshToolBar } = useToolbar();
    const associationConfig = useAiStore(v => v.association);
    const [toolBarOpen, setToolBarOpen] = useState(false);
    const callerRequest = useCallerRequest();
    const [draggableOptions, setDraggableOptions] = useState<OptionItem[]>([]);
    const { announcement, announcementVisible, closeAnnouncement, isExpanded, toggleExpand } = useAnnouncement();

    const trace = useTrace();
    const handleToolbarClick = () => {
        !toolBarOpen && trace('toolbar_sort', 'trigger');
        setToolBarOpen(!toolBarOpen);
    };

    useEffect(() => {
        if (options) {
            setDraggableOptions(options);
        }
    }, [options]);

    const { sceneTips } = useSceneTips();
    const file = useAiStore(v => v.file);
    const sendMessage = useSendMessage();

    const onDragEnd = result => {
        if (!result.destination) return;

        const items = Array.from(draggableOptions);
        const topItems = items.filter(item => item.top);
        const draggableItems = items.filter(item => !item.top);

        const [reorderedItem] = draggableItems.splice(result.source.index, 1);
        draggableItems.splice(result.destination.index, 0, reorderedItem);
        const newList = [...topItems, ...draggableItems];
        callerRequest
            .post('/bee/v2/bdaiassistant/common/toolbar/order', {
                toolNameList: draggableItems.map(item => item.content),
            })
            .then(res => {
                if (res.code === 0) {
                    refreshToolBar();
                }
            });
        setDraggableOptions(newList);
    };

    const onToolbarPressInner = (item, index) => {
        trace(`toolbar${index + 1}`, 'trigger', JSON.stringify(item));
        onToolbarPress(item, EntryPoint.toolbar + `${index + 1}`);
    };

    const topDraggableOptions = draggableOptions?.filter(item => item.top);
    // 工具栏内容
    const toolbarContent = (
        <>
            {/* 置顶工具列表 */}
            {topDraggableOptions?.map((item, index) => (
                <div
                    key={item.content}
                    style={{
                        padding: '6px 0',
                        marginBottom: 10,
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                    }}
                    onClick={() => {
                        onToolbarPressInner(item, index);
                        setToolBarOpen(false);
                    }}
                >
                    {item?.link ? (
                        <Image
                            style={{ verticalAlign: 'baseline' }}
                            src={item.link}
                            width={16}
                            height={16}
                            preview={false}
                        />
                    ) : null}
                    <span style={{ marginLeft: 3, fontSize: 14, lineHeight: '20px', flex: 1 }}>{item.content}</span>
                </div>
            ))}

            {/* 可拖动工具列表分割线 */}
            {draggableOptions?.filter(item => item.top)?.length > 0 &&
                draggableOptions?.filter(item => !item.top)?.length > 0 && <Divider style={{ margin: '10px 0' }} />}

            {/* 可拖动工具列表 */}
            <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="droppable-tools">
                    {provided => (
                        <div {...provided.droppableProps} ref={provided.innerRef}>
                            {draggableOptions
                                ?.filter(item => !item.top)
                                ?.map((item, index) => (
                                    <Draggable key={item.content} draggableId={item.content} index={index}>
                                        {provided => (
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                style={{
                                                    padding: '6px 0',
                                                    marginBottom: 10,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    cursor: 'pointer',
                                                    ...provided.draggableProps.style,
                                                }}
                                                onClick={() => {
                                                    onToolbarPressInner(item, index + topDraggableOptions?.length);
                                                    setToolBarOpen(false);
                                                }}
                                            >
                                                {item?.link ? (
                                                    <Image
                                                        style={{ verticalAlign: 'baseline' }}
                                                        src={item.link}
                                                        width={16}
                                                        height={16}
                                                        preview={false}
                                                    />
                                                ) : null}
                                                <span
                                                    style={{ marginLeft: 3, fontSize: 14, lineHeight: '20px', flex: 1 }}
                                                >
                                                    {item.content}
                                                </span>
                                                <div {...provided.dragHandleProps}>
                                                    <img src={DragImg} width={14} height={14} />
                                                </div>
                                            </div>
                                        )}
                                    </Draggable>
                                ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </DragDropContext>
        </>
    );

    return (
        <Row
            style={{
                ...style,
                position: 'relative',
                marginLeft: 0,
                marginRight: 0,
                maxWidth: 800,
                margin: '0px auto',
                borderRadius: '12px 12px 0 0',
                backgroundColor: '#fff',
                minHeight: '38px',
                // overflow: isExpanded ? 'visible' : 'hidden',
            }}
            className={!file?.length ? 'toolbar-container' : ''}
            gutter={16}
            justify={'space-between'}
            wrap={true}
        >
            <Condition condition={[associationConfig.show]}>
                <Association style={{ position: 'absolute', bottom: 0 }} />
            </Condition>
            {/* 公告展示条件 1. 公告内容存在 2. 公告为手动关闭 3. 非多模态模式（已上传图片） */}
            <Condition condition={[announcement && announcementVisible && file.length === 0]}>
                <div
                    style={{
                        position: 'absolute',
                        bottom: 0,
                        borderRadius: '12px 12px 0 0',
                        background: '#000000B7',
                        display: 'flex',
                        flexDirection: 'row',
                        zIndex: 9999,
                        alignItems: 'center',
                        width: '100%',
                        height: isExpanded ? 'auto' : 38,
                        padding: '10px 24px',
                        overflow: 'hidden',
                        transition: 'all 0.3s ease',
                        justifyContent: 'space-between',
                    }}
                >
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8, maxWidth: 'calc(100% - 65px)' }}>
                        <img src={AnnouncementIcon} width={24} height={24} />
                        <div
                            style={{
                                color: '#fff',
                                fontSize: 14,
                                lineHeight: '20px',
                                flex: 1,
                                overflow: isExpanded ? 'visible' : 'hidden',
                                textOverflow: isExpanded ? 'unset' : 'ellipsis',
                                whiteSpace: isExpanded ? 'normal' : 'nowrap',
                            }}
                        >
                            {announcement}
                        </div>
                    </div>

                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'flex-end',
                            gap: 12,
                            flexShrink: 0,
                            minWidth: 10,
                            marginLeft: '3px',
                            width: '50px',
                            alignSelf: 'end',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                cursor: 'pointer',
                                borderRadius: '50%',
                                transition: 'all 0.2s ease',
                                padding: 2,
                            }}
                            onClick={e => {
                                e.stopPropagation();
                                toggleExpand();
                            }}
                        >
                            <img
                                src={!isExpanded ? AnnouncementExpandIcon : AnnouncementCollapseIcon}
                                width={14}
                                height={14}
                            />
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                cursor: 'pointer',
                                borderRadius: '50%',
                            }}
                            onClick={e => {
                                e.stopPropagation();
                                closeAnnouncement();
                            }}
                        >
                            <img src={AnnouncementCloseIcon} width={18} height={18} style={{ cursor: 'pointer' }} />
                        </div>
                    </div>
                </div>
            </Condition>
            <Space
                style={{
                    flex: 1,
                    overflowX: 'scroll',
                    scrollbarWidth: 'none',
                    marginRight: 8,
                    justifyContent: 'flex-start',
                }}
            >
                {file.length > 0 &&
                    sceneTips?.picture?.recommendedQuestions?.map((item, index) => {
                        return (
                            <Space
                                key={index}
                                onClick={() => {
                                    sendMessage(item, { entryPoint: EntryPoint.picture_tip });
                                    trace('picture_tips', 'trigger', JSON.stringify({ content: item }));
                                }}
                                style={{
                                    padding: '8px 12px',
                                    background: '#f5f6fa',
                                    borderRadius: 50,
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    marginTop: 8,
                                    height: 36,
                                }}
                                rootClassName={'toolbar-item-container'}
                            >
                                <div style={{ whiteSpace: 'nowrap', marginRight: 4 }} className="toolbar-item-content">
                                    {item}
                                </div>
                                <Image
                                    src={ArrowRightImg}
                                    width={9}
                                    preview={false}
                                    style={{ position: 'relative', top: -1 }}
                                />
                            </Space>
                        );
                    })}
                <Condition condition={[options?.length && file.length === 0]}>
                    <div
                        style={{
                            display: 'flex',
                            gap: 8,
                            paddingRight: 80,
                            maxWidth: 'calc(100% - 80px)', // 限制最大宽度，为工具按钮留出空间
                            overflow: 'hidden',
                        }}
                    >
                        {options?.map((item, index) => {
                            return (
                                <Space
                                    key={index}
                                    onClick={() => onToolbarPressInner(item, index)}
                                    style={{
                                        background: '#fff',
                                        display: 'flex',
                                        alignItems: 'center',
                                        cursor: 'pointer',
                                        paddingLeft: 16,
                                        borderRadius: index === 0 ? '12px 0 0  0' : '0',
                                        flexShrink: 0, // 防止项目被压缩
                                    }}
                                    rootClassName={'toolbar-item-container'}
                                >
                                    {item?.link ? (
                                        <img
                                            src={item.link}
                                            style={{ width: 16, height: 16, verticalAlign: 'sub', marginRight: 3 }}
                                        />
                                    ) : null}
                                    <span style={{ whiteSpace: 'nowrap' }} className="toolbar-item-content">
                                        {item.content}
                                    </span>
                                </Space>
                            );
                        })}
                    </div>
                </Condition>

                <Condition condition={[file.length === 0]}>
                    <Popover
                        open={toolBarOpen}
                        onOpenChange={setToolBarOpen}
                        trigger="click"
                        placement="topRight"
                        arrow={false}
                        styles={{
                            body: {
                                width: 168,
                                marginBottom: 8,
                            },
                        }}
                        content={<div style={{ width: 152 }}>{toolbarContent}</div>}
                    >
                        <div
                            style={{
                                position: 'absolute',
                                right: 15,
                                top: 6,
                                cursor: 'pointer',
                                zIndex: toolBarOpen ? 1051 : 1,
                                height: 26,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                            onClick={e => {
                                e.stopPropagation();
                                handleToolbarClick();
                            }}
                        >
                            <Divider type="vertical" />
                            <div
                                style={{
                                    background: '#F5F6FA',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: 100,
                                    padding: '3px 5px',
                                    width: 58,
                                }}
                            >
                                <img src={ToolbarImg} width={16} height={16} />
                                <span
                                    style={{
                                        color: '#222',
                                        fontSize: 12,
                                        marginLeft: 4,
                                    }}
                                >
                                    工具
                                </span>
                            </div>
                        </div>
                    </Popover>
                </Condition>
            </Space>
        </Row>
    );
};
export default Toolbar;
