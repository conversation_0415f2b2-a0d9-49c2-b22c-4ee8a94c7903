import usePoiList, { PoiItem } from '@src/pages/knowledge/chat/common/service/poiList';
import noContentImg from '@src/assets/images/no-content.png';
import { Avatar, Checkbox, Empty, Input, List, Radio, Space, Spin, message } from 'antd';
import VirtualList from 'rc-virtual-list';
import { useEffect, useMemo, useState } from 'react';
import { usePrevious } from 'ahooks';
import { usePoiSelectorConfig } from '../../hooks/usePoiSelectorConfig';
import Condition from '@src/components/Condition/Condition';

interface PoiSelectorProps {
    onChange: (selectedPois?: PoiItem[]) => void;
}

const PoiSelector = ({ onChange }: PoiSelectorProps) => {
    const { isMultiSelect: multiple, defaultList: defaultPois } = usePoiSelectorConfig();

    const { onScroll, data, setSearchValue, loading, searchValue } = usePoiList();
    const [selectedPois, setSelectedPois] = useState<PoiItem[]>(defaultPois);
    const previousData = usePrevious(data);

    useEffect(() => {
        if (defaultPois.length) {
            setSelectedPois(multiple ? defaultPois : [defaultPois[0]]);
        }
    }, [defaultPois]);

    // 合并默认列表和API获取的列表
    const mergedList = useMemo(() => {
        if (searchValue) {
            return data?.list || [];
        }
        return [
            ...defaultPois,
            ...(data?.list || []).filter(item => !defaultPois.some(defaultItem => defaultItem.id === item.id)),
        ];
    }, [data?.list, defaultPois]);

    useEffect(() => {
        if (previousData?.list?.length !== data?.list?.length) {
            // 当列表变化时，保留已选中项，不再自动选中第一个
        }
    }, [data?.list]);

    const intersectionPoiList = selectedPois.filter(sp => mergedList.some(ml => ml.id === sp.id));
    useEffect(() => {
        // 搜索中阻止提交
        if (loading) {
            return;
        }
        onChange?.(intersectionPoiList);
    }, [selectedPois, loading, mergedList]);

    const handleSelect = (poi: PoiItem, checked: boolean) => {
        if (checked) {
            if (multiple) {
                if (intersectionPoiList.length >= 30) {
                    message.warning('最多选择30个商家');
                    return;
                }
                setSelectedPois(prev => [...prev, poi]);
            } else {
                setSelectedPois([poi]);
            }
        } else {
            setSelectedPois(prev => prev.filter(item => item.id !== poi.id));
        }
    };

    return (
        <Space direction={'vertical'} style={{ width: '100%' }}>
            <Input.Search
                placeholder={'请输入商家名称'}
                onChange={e => setSearchValue(e.target.value)}
                style={{ width: '100%' }}
            />
            <Spin spinning={loading}>
                <List style={{ width: '100%' }} locale={{ emptyText: '暂无数据' }}>
                    {!mergedList.length ? (
                        <Empty
                            description={'仅支持查询您名下的商家，请确保输入信息无误'}
                            image={noContentImg}
                            style={{ margin: '30px auto' }}
                        />
                    ) : (
                        <VirtualList
                            data={mergedList}
                            height={400}
                            itemHeight={47}
                            style={{ scrollbarWidth: 'none' }}
                            itemKey={item => item?.id}
                            onScroll={onScroll}
                        >
                            {item => {
                                const isSelected = selectedPois.some(poi => poi.id === item.id);
                                return (
                                    <Space
                                        onClick={() => handleSelect(item, !isSelected)}
                                        className={'pointer'}
                                        style={{ padding: '8px', width: '100%' }}
                                    >
                                        <Condition condition={[multiple, !multiple]}>
                                            <Checkbox checked={isSelected} />
                                            <Radio checked={isSelected} />
                                        </Condition>
                                        <Avatar src={item.url} />
                                        <div>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                {item.tagNameList && item.tagNameList.length > 0 && (
                                                    <div
                                                        style={{
                                                            margin: '4px 0',
                                                        }}
                                                    >
                                                        {item.tagNameList.map((tag, index) => (
                                                            <span
                                                                key={index}
                                                                style={{
                                                                    display: 'inline-block',
                                                                    padding: '2px 6px',
                                                                    margin: '0 4px 2px 0',
                                                                    fontSize: '11px',
                                                                    color: '#FF192D',
                                                                    backgroundColor: '#FFF5F6',
                                                                    borderRadius: '4px',
                                                                    maxWidth: '100px',
                                                                }}
                                                            >
                                                                {tag}
                                                            </span>
                                                        ))}
                                                    </div>
                                                )}
                                                <div
                                                    className={'f_14 c_222'}
                                                    style={{
                                                        flex: 1,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                    }}
                                                >
                                                    {item.name}
                                                </div>
                                            </div>

                                            <div className={'f_12 c_666'}>ID: {item.id}</div>
                                        </div>
                                    </Space>
                                );
                            }}
                        </VirtualList>
                    )}
                </List>
            </Spin>
        </Space>
    );
};
export default PoiSelector;
