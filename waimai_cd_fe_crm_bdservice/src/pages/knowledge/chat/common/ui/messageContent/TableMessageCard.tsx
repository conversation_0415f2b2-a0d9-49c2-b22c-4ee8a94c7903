import { Table, Tooltip } from 'antd';
import { TableMessage as TableMessageType } from '@src/pages/knowledge/chat/common/type/message';
import { CSSProperties } from 'react';
import Condition from '@src/components/Condition/Condition';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useToggle } from 'ahooks';
import { renderStandardQuestionItem } from '../message/messageItemRender';

const TableMessage = ({
    columns,
    data,
    showCollapse,
    collapseDesc,
    collapseState,
    comment,
    style,
}: TableMessageType['insert']['table'] & { style?: CSSProperties }) => {
    // 如果不展示折叠按钮，则默认展开
    const [collapse, { toggle }] = useToggle(showCollapse ? collapseState : true);
    return (
        <div style={style} className={'table-message'}>
            <Condition condition={[showCollapse]}>
                <div onClick={toggle} style={{ marginBottom: 12, color: '#222' }} className={'pointer'}>
                    <Tooltip title={'点击展开'}>
                        {collapseDesc} {!collapse ? <DownOutlined /> : <UpOutlined />}
                    </Tooltip>
                </div>
            </Condition>
            <Condition condition={[comment]}>
                <div style={{ marginBottom: 12, color: '#666' }}>
                    <span> {comment}</span>
                </div>
            </Condition>
            <Condition condition={[collapse]}>
                <Table
                    columns={columns.map(v => {
                        return {
                            ...v,
                            render: text => {
                                if (typeof text === 'object') {
                                    return renderStandardQuestionItem(() => {}, null, true)(text, 0);
                                }
                                return text;
                            },
                        };
                    })}
                    dataSource={data}
                    pagination={false}
                    bordered
                    style={{ fontSize: 10 }}
                />
            </Condition>
        </div>
    );
};
export default TableMessage;
