import MediaRender from '@src/pages/knowledge/chat/common/ui/messageContent/MediaRender';
import './MarkdownRender.scss';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import BaseText from '@src/pages/knowledge/chat/common/ui/messageContent/BaseTextCard';
import { getStyle } from '@src/pages/knowledge/chat/common/ui/message/indexNew';
import EnhancedMarkdownTable from './EnhancedMarkdownTable';

const castMediaItem = (deltaWithType: any[]) => {
    const res: any[] = [];
    // 图片和视频同行展示
    for (let i = 0; i < deltaWithType.length; i++) {
        const cur = deltaWithType[i];
        if (!['image', 'video'].includes(cur.type)) {
            res.push(cur);
            continue;
        }

        const lastEle = res[res.length - 1] || { type: '' };
        if (lastEle.type === 'media') {
            lastEle.insert.media.push(cur.insert);
            continue;
        }
        res.push({
            type: 'media',
            insert: { media: [cur.insert] },
        });
    }
    return res;
};

const pic = {
    test: (str: string) => /!\[.*?]\((.*?)\)/.test(str),
    text2Obj: (str: string) => {
        const value = str.split(/!\[.*?]\((.*?)\)/).filter(v => v);
        return { url: value[0] };
    },
};

// 处理表格：将整个表格作为一个整体
const processContent = (text: string) => {
    const lines = text.split('\n');
    const result: string[] = [];
    let currentTable: string[] = [];
    let isInTable = false;
    let otherLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const isTableLine = /^\|/.test(line);

        if (isTableLine) {
            if (!isInTable) {
                isInTable = true;
                if (otherLines.length > 0) {
                    result.push(otherLines.join('\n'));
                    otherLines = [];
                }
            }
            currentTable.push(line);
        } else {
            if (isInTable) {
                // 表格结束，将整个表格作为一个整体
                result.push(currentTable.join('\n'));
                currentTable = [];
                isInTable = false;
            }
            otherLines.push(line);
        }
    }

    // 处理最后一个表格
    if (currentTable.length > 0) {
        result.push(currentTable.join('\n'));
    }
    if (otherLines.length > 0) {
        result.push(otherLines.join('\n'));
    }

    return result.filter(v => v && !/^\s*$/.test(v));
};

// 从md文本解析图片和表格数据
export const splitMediaFromMarkdown = (md: string) => {
    const splitPattern = /(!\[.*?]\(.*?\))/; // md图片

    const castMarkdown = (children: string) => {
        let res: any[] = [];
        if (pic.test(children)) {
            const parts = children.split(splitPattern);
            res = parts
                .filter(v => v && !/^\s*$/.test(v))
                .filter(Boolean)
                .map(p => {
                    if (pic.test(p)) {
                        const { url } = pic.text2Obj(p);
                        return {
                            type: 'image',
                            insert: { image: url },
                        };
                    }
                    return {
                        type: 'text',
                        insert: p,
                    };
                });
        } else {
            res = [{ type: 'text', insert: children }];
        }
        return res;
    };

    const parsedData1: any[] = castMediaItem(castMarkdown(md));
    const parsedData2 = parsedData1
        .map(v => {
            if (v.type !== 'text') {
                return [v];
            }
            const str = v.insert;
            const processedContent = processContent(str);
            return processedContent.map(content => {
                const isTable = /^\|.*/.test(content.split('\n')[0]);
                if (isTable) {
                    return {
                        type: 'table',
                        insert: content,
                    };
                }
                return {
                    type: 'text',
                    insert: content,
                };
            });
        })
        .reduce((acc, cur) => {
            return [...acc, ...cur];
        }, []);

    return parsedData2;
};

const openHorizontalTable = (tableData: string) => {
    // 将表格数据存储在 localStorage 中，添加日期标记
    const storageKey = 'table_data_' + Date.now();
    const dataWithDate = {
        content: tableData,
        createdAt: new Date().toISOString().split('T')[0], // 添加当前日期标记，格式：YYYY-MM-DD
    };
    localStorage.setItem(storageKey, JSON.stringify(dataWithDate));

    // 在新窗口中打开表格页面，通过 key 参数传递 storageKey
    const url = `${import.meta.env.DEV ? '' : '/page/bdservice'}/knowledge/horizontalTable?key=${storageKey}`;
    window.open(url, '_blank');
};

const MarkdownRender = ({ data }: { data: string }) => {
    if (!data) {
        return null;
    }
    const renderData = splitMediaFromMarkdown(data);

    const baseMdRender = (item: any) => {
        return (
            <ReactMarkdown
                key={item.insert}
                children={item.insert}
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
                components={{
                    a: props => {
                        const { href, children } = props;
                        const Comp: any = BaseText;
                        return (
                            <Comp
                                key={'react-md'}
                                text={children as string}
                                link={href}
                                style={getStyle([{ wordBreak: 'break-all' }, { whiteSpace: 'pre-wrap' }])}
                                {...props}
                            />
                        );
                    },
                    table: props => (
                        <div className="markdown-table-wrapper">
                            <table {...props} className="markdown-table" />
                        </div>
                    ),
                    th: props => <th {...props} className="markdown-table-header" />,
                    td: props => <td {...props} className="markdown-table-cell" />,
                    br: () => <br />,
                }}
            />
        );
    };

    return (
        <>
            {renderData.map((item, index) => {
                if (item.type === 'media') {
                    return <MediaRender item={item} key={index} />;
                }
                if (item.type === 'table') {
                    return (
                        <div key={item.insert} className="markdown-table-container">
                            <EnhancedMarkdownTable
                                markdownTable={item.insert}
                                onFullScreen={() => openHorizontalTable(item.insert)}
                            />
                        </div>
                    );
                }
                return baseMdRender(item);
            })}
        </>
    );
};
export default MarkdownRender;
