.reject-card-container {
    margin: 12px 0;

    .reject-card-item {
        background: #ffffff;
        border: 1px solid #f0f0f0;
        border-radius: 12px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:last-child {
            margin-bottom: 0;
        }

        .ant-card-body {
            padding: 16px !important;
        }

        .reject-card-content {
            .reject-card-header {
                margin-bottom: 12px;

                .reject-card-title {
                    color: #333333;
                    font-size: 16px;
                    font-weight: 700;
                    margin: 0;
                    line-height: 1.4;
                    word-break: break-word;

                    // 最多显示2行，超出省略
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .reject-card-markdown {
                margin-bottom: 16px;
                color: #666666;
                font-size: 14px;
                line-height: 1.6;
                word-break: break-word;
            }

            .reject-card-descriptions {
                margin-bottom: 16px;

                .ant-descriptions {
                    .ant-descriptions-item-label {
                        color: #999999;
                        font-size: 14px;
                        font-weight: normal;
                        min-width: 80px;
                    }

                    .ant-descriptions-item-content {
                        color: #333333;
                        font-size: 14px;
                    }
                }
            }

            .reject-card-button-wrapper {
                display: flex;
                justify-content: flex-start;

                .reject-card-button {
                    border-radius: 20px !important;
                    padding: 6px 16px;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    height: initial;

                    &.ant-btn-primary {
                        background-color: #ffdd10;
                        border-color: #ffdd10;
                        color: #333333;

                        &:hover {
                            background-color: #ffcc00 !important;
                            border-color: #ffcc00 !important;
                        }

                        &:focus {
                            background-color: #ffcc00;
                            border-color: #ffcc00;
                        }
                    }

                    &.ant-btn-default {
                        background-color: #f5f5fa;
                        border-color: #e0e0e0;
                        color: #333333;

                        &:hover {
                            background-color: #ebebeb !important;
                            border-color: #d0d0d0 !important;
                        }

                        &:focus {
                            background-color: #ebebeb;
                            border-color: #d0d0d0;
                        }
                    }
                }
            }
        }
    }

    .reject-card-expand-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 8px;

        .reject-card-expand-button {
            background-color: #f5f5f5;
            border-radius: 20px;
            padding: 6px 16px;
            font-size: 14px;
            color: #666666;
            border: none;
            height: initial;

            &:hover {
                background-color: #ebebeb !important;
                color: #333333 !important;
            }

            &:focus {
                background-color: #ebebeb;
                color: #333333;
            }
        }
    }
}
