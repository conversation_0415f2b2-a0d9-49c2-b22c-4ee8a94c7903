# FormCard 组件 PRD

## 1. 功能概述

FormCard 是一个通用的表单卡片组件，用于在对话界面中收集用户输入信息。支持单选和输入框两种输入形式。

## 2. 交互设计

### 2.1 布局结构

-   表单项垂直排列
-   底部包含确定按钮
-   label 需要对齐

### 2.2 表单项类型

1. 输入框类型 (input)

    - 支持文本输入
    - 可设置默认值
    - 支持占位符文本
    - 输入框高度固定

2. 单选类型 (radio)
    - 横向排列的单选按钮组
    - 选项文字位于按钮右侧
    - 支持默认选中值
    - 选项间距均匀

### 2.3 确定按钮

-   位于表单底部
-   使用主题色背景
-   圆角设计
-   可自定义按钮文案

## 3. 视觉规范

### 3.1 卡片样式

-   不需要容器

### 3.2 表单项样式

-   标签文字：14px，#222222
-   输入框(input)：

    -   边框：1px solid #E8E8E8
    -   圆角：4px
    -   内边距：8px 12px
    -   文字：14px，#222222
    -   占位符：14px，#999999

-   单选按钮（radio）：
    -   按钮大小：16px
    -   选中色：主题色
    -   选项文字：14px，#222222
    -   选项间距：16px

### 3.3 确定按钮样式

-   高度：40px
-   文字：14px，#FFFFFF
-   背景色：美团黄
-   圆角：20px

## 4. 交互响应

-   表单提交时进行必要的数据验证
-   历史消息或者已提交的卡片按钮隐藏且选项置灰

## 5. 接口定义

### Props 定义

```typescript
interface FormCardProps {
    config: {
        label: string; // 表单项标签
        type: 'radio' | 'input'; // 表单项类型
        options?: string[]; // 单选项选项列表
        defaultValue?: string; // 默认值
    }[];
    buttonText?: string; // 确定按钮文案
    history?: boolean; // 是否是历史消息
}
```

## 6. 使用示例

```typescript
const config = [
    {
        label: 'BDmis',
        type: 'input',
        defaultValue: '',
    },
    {
        label: '绩效目标',
        type: 'radio', // radio为单选按钮组，即色块组，而非传统意义上的radio
        options: ['100%', '120%', '150%'],
        defaultValue: '100%',
    },
];

<FormCard config={config} buttonText="确定" history />;
```
