# EnhancedImage 组件

## 功能说明

`EnhancedImage` 是一个增强的图片组件，基于 Ant Design 的 `Image` 组件封装，主要用于处理 Wiki 图片链接的自动转换功能。

### 主要特性

1. **自动转换功能**：当以 `https://km.sankuai.com` 开头的图片加载失败时，自动调用转换接口获取新的可用链接
2. **加载状态指示**：转换过程中显示加载指示器，提供良好的用户体验
3. **缓存机制**：避免重复转换同一张图片，提高性能
4. **优化的状态管理**：使用 useRef 优化性能，避免不必要的重渲染
5. **错误处理**：完善的错误处理机制，接口失败时保持原始图片地址
6. **兼容性**：完全兼容 Ant Design Image 组件的所有属性和方法

## 使用方法

### 基本用法

```tsx
import EnhancedImage from './EnhancedImage';

// 基本使用，与 Ant Design Image 组件完全一致
<EnhancedImage src="https://km.sankuai.com/example.jpg" />;
```

### 高级用法

```tsx
import EnhancedImage from './EnhancedImage';

<EnhancedImage
    src="https://km.sankuai.com/example.jpg"
    width={200}
    height={150}
    enableAutoConvert={true} // 是否启用自动转换，默认为 true
    onConvertSuccess={(originalSrc, newSrc) => {
        console.log('图片转换成功:', originalSrc, '->', newSrc);
    }}
    onConvertFailed={(originalSrc, error) => {
        console.log('图片转换失败:', originalSrc, error);
    }}
    onError={event => {
        console.log('图片加载失败:', event);
    }}
/>;
```

## API 参数

### EnhancedImageProps

继承自 Ant Design `ImageProps`，额外支持以下属性：

| 参数              | 说明                 | 类型                                          | 默认值 |
| ----------------- | -------------------- | --------------------------------------------- | ------ |
| enableAutoConvert | 是否启用自动转换功能 | boolean                                       | true   |
| onConvertSuccess  | 转换成功时的回调函数 | (originalSrc: string, newSrc: string) => void | -      |
| onConvertFailed   | 转换失败时的回调函数 | (originalSrc: string, error?: any) => void    | -      |

## 工作原理

1. **域名检查**：组件会检查图片 URL 是否以 `https://km.sankuai.com` 开头
2. **加载失败监听**：监听图片的 `onError` 事件
3. **加载状态显示**：转换过程中显示 Spin 加载指示器
4. **自动转换**：当符合条件的图片加载失败时，调用 `/bee/v2/bdaiassistant/common/convertWikiPicToS3Url` 接口
5. **缓存优化**：转换结果会被缓存，避免重复请求
6. **状态管理**：使用 useRef 防止重复转换，优化性能
7. **错误处理**：接口失败时保持原始图片地址，确保用户能看到图片或默认的加载失败状态

## 性能优化

1. **useRef 优化**：`hasTriedConvert` 使用 useRef 而非 useState，避免不必要的重渲染
2. **缓存机制**：转换结果缓存，避免重复转换同一张图片
3. **状态控制**：防止重复转换和无限重试

## 错误处理策略

1. **接口失败**：网络错误或接口异常时，不进行重试，保持使用原始图片地址
2. **转换失败**：转换接口返回失败时，保持使用原始图片地址
3. **用户体验**：确保用户能看到原始图片或默认的图片加载失败状态，而不是空白

## 注意事项

1. 只有以 `https://km.sankuai.com` 开头的图片才会触发自动转换
2. 每张图片只会尝试转换一次，避免无限重试
3. 转换过程中会显示加载指示器，提供良好的用户体验
4. 组件完全兼容 Ant Design Image 的所有功能和属性

## 已应用的位置

该组件已经在以下位置替换了原有的 Image 组件：

-   `MediaRender.tsx` - 消息中的媒体图片渲染
-   `indexNew.tsx` - 用户上传的图片显示
-   `welcome.tsx` - 热门问题的图标
-   `toolbar.tsx` - 工具栏中的图标

## 相关工具函数

### imageConverter.ts

提供了图片转换相关的工具函数：

-   `shouldConvertImage(imageUrl)` - 检查图片是否需要转换
-   `convertWikiPicToS3Url(wikiPictureUrl)` - 调用转换接口
-   `convertWikiPicToS3UrlWithCache(wikiPictureUrl)` - 带缓存的转换函数
-   `clearConversionCache()` - 清理转换缓存
