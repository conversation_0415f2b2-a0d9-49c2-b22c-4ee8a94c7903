import React from 'react';
import { message } from 'antd';
import { RejectCardMessage } from '@src/pages/knowledge/chat/common/type/message';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { EntryPointType, EntryPoint } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import './RejectCard.scss';
import { createRejectHtml } from '../../utils/createRejectHtml';

interface Props {
    data: RejectCardMessage['insert']['rejectCard'];
    serverId?: string;
    history?: boolean;
}

const RejectCard: React.FC<Props> = ({ data, serverId, history }) => {
    const sendMessage = useSendMessage();
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();
    const [iframeHeight, setIframeHeight] = React.useState(200); // 初始高度
    const [isLoading, setIsLoading] = React.useState(true); // 添加loading状态

    // 处理来自 iframe 的消息
    React.useEffect(() => {
        const handleMessage = async (event: MessageEvent) => {
            if (event.data.type === 'submitQuestion') {
                try {
                    await sendMessage(event.data.payload, {
                        entryPointType: EntryPointType.USER,
                        entryPoint: EntryPoint.reject_card,
                    });
                } catch (error) {
                    console.error('Send message error:', error);
                    message.error('发送消息失败，请重试');
                }
            } else if (event.data.type === 'navigate') {
                try {
                    openLink(event.data.payload, serverId, sessionId, bizInfo?.uid, history);
                } catch (error) {
                    console.error('Navigate error:', error);
                    message.error('跳转失败，请重试');
                }
            } else if (event.data.type === 'heightChanged') {
                // 更新iframe高度，添加一些额外的边距
                const newHeight = Math.max(event.data.payload + 20, 100); // 最小高度100px
                setIframeHeight(newHeight);
                // 高度变化表示内容已加载完成
                setIsLoading(false);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [sendMessage, serverId, sessionId, bizInfo?.uid, history]);

    const htmlContent = React.useMemo(() => createRejectHtml(data), [data]);

    return (
        <div style={{ position: 'relative' }}>
            {isLoading && (
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f7f7f8',
                        borderRadius: '8px',
                        zIndex: 1,
                    }}
                >
                    <div style={{ color: '#666', fontSize: '14px' }}>加载中...</div>
                </div>
            )}
            <iframe
                srcDoc={htmlContent}
                style={{
                    width: '100%',
                    height: `${iframeHeight}px`,
                    border: 'none',
                    borderRadius: '8px',
                    transition: 'height 0.3s ease', // 添加高度变化动画
                    overflow: 'hidden', // 隐藏iframe滚动条
                    opacity: isLoading ? 0.5 : 1,
                }}
                sandbox="allow-scripts allow-same-origin"
            />
        </div>
    );
};

export default RejectCard;
