import React, { useEffect, useState } from 'react';
import { message, Skeleton } from 'antd';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import useSendMessage, {
    EntryPointType,
    EntryPoint,
} from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import { WebviewMessage } from '../../type/message';
import openLink from '../../utils/openLink';
import useAiStore from '../../data/core';
import { throttle } from 'lodash';
import { useCallback, useRef } from 'react';

interface Props {
    data: WebviewMessage['insert']['webview'];
    serverId?: string;
    history?: boolean;
}

const WebviewCom: React.FC<Props> = ({ data, serverId, history }) => {
    console.log('webview data', data);
    const sendMessage = useSendMessage();
    const { data: bizInfo } = useBizInfo();
    const sessionId = useAiStore(state => state.sessionId);
    const [iframeHeight, setIframeHeight] = useState(200); // 初始高度
    const [isLoading, setIsLoading] = useState(true); // 添加loading状态
    const iframeRef = useRef<HTMLIFrameElement>(null); // 添加 iframe 引用

    const handleHeightChange = useCallback(
        throttle((newHeight: number, currentIsLoading: boolean) => {
            // 更新iframe高度，添加一些额外的边距
            newHeight = Math.max(newHeight + 20, 100); // 最小高度100px
            !isNaN(newHeight) && setIframeHeight(newHeight);
            // 高度变化表示内容已加载完成
            currentIsLoading && setIsLoading(false);
        }, 1000),
        [], // 空依赖数组，确保 throttle 函数只创建一次
    );

    // 处理来自 iframe 的消息
    useEffect(() => {
        const handleMessage = async (event: MessageEvent) => {
            // 验证消息来源，确保只处理来自当前 iframe 的消息
            if (!iframeRef.current || event.source !== iframeRef.current.contentWindow) {
                return;
            }

            let eventData;
            try {
                eventData = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
            } catch (error) {
                console.log('webview event error', event.data);
            }
            if (eventData.type === 'submitQuestion') {
                try {
                    await sendMessage(eventData.payload, {
                        entryPointType: EntryPointType.USER,
                        entryPoint: EntryPoint.reject_card,
                    });
                } catch (error) {
                    console.error('Send message error:', error);
                    message.error('发送消息失败，请重试');
                }
            } else if (eventData.type === 'navigate') {
                try {
                    openLink(eventData.payload, serverId, sessionId, bizInfo?.uid, history);
                } catch (error) {
                    console.error('Navigate error:', error);
                    message.error('跳转失败，请重试');
                }
            } else if (eventData.type === 'heightChanged') {
                handleHeightChange(eventData.payload, isLoading);
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [sendMessage, serverId, sessionId, bizInfo?.uid, history]);

    return (
        <div style={{ position: 'relative' }}>
            {isLoading && (
                <div
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f7f7f8',
                        borderRadius: '8px',
                        zIndex: 1,
                    }}
                >
                    <Skeleton style={{ padding: 15 }} active />
                </div>
            )}
            <iframe
                ref={iframeRef}
                src={data.url}
                style={{
                    width: '100%',
                    height: `${iframeHeight}px`,
                    border: 'none',
                    borderRadius: '8px',
                    transition: 'height 0.3s ease', // 添加高度变化动画
                    overflow: 'hidden', // 隐藏iframe滚动条
                    opacity: isLoading ? 0.5 : 1,
                }}
                onLoad={() => {
                    setIsLoading(false);
                }}
                sandbox="allow-scripts allow-same-origin"
            />
        </div>
    );
};

export default WebviewCom;
