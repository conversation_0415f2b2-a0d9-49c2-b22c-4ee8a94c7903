import React from 'react';
import { <PERSON>, Button, message } from 'antd';
import { ActionCardMessage } from '@src/pages/knowledge/chat/common/type/message';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { EntryPointType, EntryPoint } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import './ActionCard.scss';

interface Props {
    data: ActionCardMessage['insert']['actionCard'];
    serverId?: string;
    history?: boolean;
}

const ActionCard: React.FC<Props> = ({ data, serverId, history }) => {
    const { button, title, subTitle, backgroundColor } = data;
    const sendMessage = useSendMessage();
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();

    const handleButtonClick = async () => {
        try {
            // 优先处理action
            if (button.action === 'submitQuestion' && button.question) {
                await sendMessage(button.question, {
                    entryPointType: EntryPointType.USER,
                    entryPoint: EntryPoint.action_card,
                });
                return;
            }

            // 处理URL跳转
            if (button.url) {
                openLink(button.url, serverId, sessionId, bizInfo?.uid, history);
                return;
            }

            // 如果没有action和url，显示提示
            message.warning('暂无可执行的操作');
        } catch (error) {
            console.error('ActionCard button click error:', error);
            message.error('操作失败，请重试');
        }
    };

    const getButtonType = () => {
        return button.type === 'primary' ? 'primary' : 'default';
    };

    const getButtonStyle = () => {
        const style: React.CSSProperties = {};

        // 优先使用自定义颜色
        if (button.color) {
            style.borderColor = button.color;
            if (button.type === 'primary') {
                style.backgroundColor = button.color;
            } else {
                style.color = button.color;
            }
        }

        return style;
    };

    const cardStyle: React.CSSProperties = {
        backgroundColor: backgroundColor || '#f5f6fa',
        borderRadius: '12px',
        marginBottom: '16px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        margin: '12px 0',
    };

    return (
        <Card className="action-card" style={cardStyle} bordered={false}>
            <div className="action-card-content">
                <div className="action-card-title-wrapper">
                    <h3 className="action-card-title">{title}</h3>
                    <p className="action-card-subtitle">{subTitle}</p>
                </div>
                <div className="action-card-button-wrapper">
                    <Button
                        type={getButtonType()}
                        style={getButtonStyle()}
                        className="action-card-button"
                        onClick={handleButtonClick}
                        size="large"
                        block
                    >
                        {button.text}
                    </Button>
                </div>
            </div>
        </Card>
    );
};

export default ActionCard;
