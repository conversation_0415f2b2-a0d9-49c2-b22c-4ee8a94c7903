import { useState, useRef, useEffect } from 'react';
import { Row, Typography } from 'antd';
import { CollapsibleTextMessage } from '../../type/message';
import Condition from '@src/components/Condition/Condition';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import MediaRender from './MediaRender';
import BaseText from './BaseTextCard';
import { getStyle } from '../message/messageItemRender';
import { castMediaMessage } from '../../service/sendMessage/adaptServerMessage';

const { Paragraph } = Typography;

interface CollapsibleTextProps {
    content: CollapsibleTextMessage['insert']['collapsibleText']['content'];
    extendButtonName?: string;
    numberOfLines: number;
    msgId?: string;
    history?: boolean;
    maxHeight?: number;
}

const CollapsibleText = (props: CollapsibleTextProps) => {
    const { content: originContent, extendButtonName, msgId, history, maxHeight = 200 } = props;
    const content = castMediaMessage(originContent);
    const [expanded, setExpanded] = useState(false);
    const [isOverflow, setIsOverflow] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        // 检查内容是否超出指定行数
        const checkOverflow = () => {
            if (contentRef.current) {
                const actualHeight = contentRef.current.scrollHeight;
                setIsOverflow(actualHeight > maxHeight);
            }
        };

        checkOverflow();
        // 监听窗口大小变化，重新检查是否溢出
        window.addEventListener('resize', checkOverflow);
        return () => window.removeEventListener('resize', checkOverflow);
    }, [content]);

    const renderContent = () => {
        return content.map((item: any, index) => {
            switch (item.type) {
                case 'styledText':
                case 'link':
                case 'text':
                    return (
                        <BaseText
                            key={index}
                            text={item.insert}
                            link={item.attributes?.link} // 链接，如果该项有内容则按链接展示
                            style={getStyle([
                                { wordBreak: 'break-all', textAlign: 'justify' }, // 强制换行，防止英文过长超出容器的情况
                                item.attributes?.bold ? { fontWeight: 'bold' } : undefined, // 支持加粗
                                item.attributes?.color ? { color: item.attributes.color } : undefined, // 支持颜色
                                { whiteSpace: 'pre-wrap' }, // 保留换行符
                            ])}
                        />
                    );
                case 'media':
                    return <MediaRender item={item} />;
            }
        });
    };

    return (
        <div className="collapsible-text">
            <div
                ref={contentRef}
                style={{
                    overflow: 'hidden',
                    transition: 'max-height 0.2s ease-in-out',
                    maxHeight: !expanded && isOverflow ? `${maxHeight}px` : 'none',
                }}
            >
                <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>{renderContent()}</Paragraph>
            </div>
            <Condition condition={[isOverflow && extendButtonName]}>
                <Row
                    justify="center"
                    onClick={() => setExpanded(!expanded)}
                    className="pointer"
                    style={{ alignItems: 'center' }}
                >
                    <>
                        <Typography.Link>{expanded ? '收起' : extendButtonName}</Typography.Link>
                        <Condition condition={[expanded, !expanded]}>
                            <UpOutlined style={{ color: '#FF6A00' }} />
                            <DownOutlined style={{ color: '#FF6A00' }} />
                        </Condition>
                    </>
                </Row>
            </Condition>
        </div>
    );
};

export default CollapsibleText;
