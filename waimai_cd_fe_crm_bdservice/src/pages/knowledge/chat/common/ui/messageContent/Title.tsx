import { Typography } from 'antd';
import MessageContext from '../message/messageContext';
import { useContext, useEffect } from 'react';
import './Title.scss';

const Title = ({ title, subTitle }: { title: string; subTitle: string }) => {
    const { setWithGradientHeader } = useContext(MessageContext);
    useEffect(() => {
        setWithGradientHeader(true);
    }, []);
    return (
        <>
            {title && (
                <div className="form-title">
                    <Typography.Title level={5}>{title}</Typography.Title>
                    <img
                        src="https://s3plus.meituan.net/bdaiassistant-public/rn_assets/common/yellow_ball.png"
                        className="title-decoration"
                        alt="decoration"
                    />
                </div>
            )}
            {subTitle && (
                <Typography.Text type="secondary" className="form-subtitle">
                    {subTitle}
                </Typography.Text>
            )}
        </>
    );
};

export default Title;
