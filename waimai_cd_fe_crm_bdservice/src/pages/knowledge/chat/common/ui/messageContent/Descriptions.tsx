import { Col, Row, Typography } from 'antd';
import { DescriptionsMessage } from '../../type/message';

const Descriptions = ({ list }: DescriptionsMessage['insert']['descriptions']) => {
    return (
        <>
            {list.map((item, index) => (
                <Row key={index} style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Col className="descriptions-text" span={8}>
                        {item.label}
                    </Col>
                    <Col className="descriptions-text" span={16} style={{ textAlign: 'end' }}>
                        <Typography.Text ellipsis={{ tooltip: item.value }}>{item.value}</Typography.Text>
                    </Col>
                </Row>
            ))}
        </>
    );
};

export default Descriptions;
