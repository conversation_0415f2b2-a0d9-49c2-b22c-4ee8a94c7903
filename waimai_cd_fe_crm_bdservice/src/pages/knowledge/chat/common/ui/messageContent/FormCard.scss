.form-card {
    border-radius: 8px;
    padding: 4px;

    .form-title {
        position: relative;
        margin-bottom: 4px;

        .ant-typography {
            font-size: 16px;
            font-weight: 700;
            position: relative;
            z-index: 2;
            margin: 0;
        }

        .title-decoration {
            position: absolute;
            left: 16px;
            top: 0;
            width: 30px;
            height: 24px;
            z-index: 1;
        }

        .form-title-text {
            font-family: 'MeituanRegular';
            font-size: 16px;
            font-weight: 700;
            position: relative;
            z-index: 2;
        }
    }

    .form-subtitle {
        display: block;
        font-size: 12px;
        color: #999;
        margin-bottom: 16px;
    }

    .form-item {
        display: flex;
        align-items: center;

        &.label-wrap {
            flex-direction: column;

            .form-label-container {
                margin-bottom: 8px;
                width: 100%;
            }

            .form-content {
                width: 100%;
            }
        }

        .form-label-container {
            display: flex;
            align-items: center;
            margin-right: 4px;

            .form-label {
                font-size: 14px;
                color: #222222;
                margin-right: 4px;
            }

            .tooltip-icon {
                color: #999;
                cursor: pointer;
                font-size: 14px;
            }
        }
    }

    .custom-radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .radio-button {
            flex: 1;
            height: 40px;
            padding: 4px 12px;
            background-color: #F5F6FA;
            border: 1px solid transparent;
            border-radius: 8px;
            font-size: 12px;
            color: #222222;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;

            &:hover {
                background-color: #FFFBE0;
            }

            &.active {
                background-color: #FFFBE0;
                border-color: #FFDD00;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                background-color: #F5F5F5;
                color: #999999;
            }
        }
    }

    .ant-input {
        height: 40px;
        border-color: #E8E8E8;
        border-radius: 8px;
        font-size: 12px;
        background-color: #F5F6FA;

        &:disabled {
            background-color: #F5F5F5;
            border-color: #E8E8E8;
            color: #999999;
        }
    }

    .submit-button {
        width: 100%;
        height: 40px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        background: linear-gradient(97deg, #FFE74D 0%, #FFDD1A 100%);
        border: none;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 4pt;
        justify-content: center;
        align-items: center;
        padding: 10pt 22pt;
        cursor: pointer;

        &:hover {
            background: linear-gradient(97deg, #FFE74D 10%, #FFDD1A 90%);
        }

        &:active {
            background: linear-gradient(97deg, #FFDD1A 0%, #FFE74D 100%);
        }
    }
}
