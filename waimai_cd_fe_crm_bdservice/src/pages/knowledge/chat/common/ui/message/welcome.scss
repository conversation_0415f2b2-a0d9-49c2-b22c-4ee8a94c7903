.welcome-message-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 20px;
    max-width: 480px;
    margin: 0 auto;

    .welcome-icon {
        width: 198px;
        height: 198px;
    }

    .welcome-text-container {
        text-align: center;
        margin-bottom: 20px;
        margin-top: -25px;

        .welcome-text {
            font-weight: 900;
            font-size: 24px;
            line-height: 32px;
            margin-bottom: 0;
            color: #222222;
        }
    }

    .hot-questions-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        width: 100%;

        .hot-question-item-img {
            width: 10px;
            height: 13px;
            margin-right: 7px;
            flex-shrink: 0;
        }

        .hot-question-item {
            padding: 12px 16px;
            border-radius: 16px !important;
            color: #222;
            font-size: 14px;
            border-color: transparent !important;
            background-color: #fff;
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .hot-question-item-content-text {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
        }

        .hot-question-item-content-text > span:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .hot-question-tag {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 7px 7px 7px 2px;
            background: #FF192D;
            min-width: 19px;
            height: 14px;
            line-height: 14px;
            color: #fff;
            font-size: 11px;
            margin-left: 4px;
            flex-shrink: 0;
            margin-bottom: 10px;
        }
    }
}