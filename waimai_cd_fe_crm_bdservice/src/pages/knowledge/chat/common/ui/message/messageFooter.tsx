import Condition from '@src/components/Condition/Condition';
import { FeedbackType, MessageFrom, MessageStatus } from '../../type/message';
import { App, Divider, Input, Row, Space } from 'antd';
import { DislikeFilled, LikeFilled } from '@ant-design/icons';
import useAiStore from '../../data/core';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import _ from 'lodash';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';
import getCopyData from '../../utils/copyText';
import CopyIcon from '@src/assets/images/chat/copy.png';
import GoodIcon from '@src/assets/images/chat/good.png';
import BadIcon from '@src/assets/images/chat/bad.png';

const MessageFooter = ({ data }) => {
    const stopPollingMessage = useAiStore(v => v.stopPollingMessage);
    const modifyMessage = useAiStore(v => v.modifyMessage);
    const messageArray = useAiStore(v => v.messageArray);
    const { modal, message } = App.useApp();

    const callerRequest = useCallerRequest();
    const submitFeedback = async (type, feedBackContent?) => {
        const res = await callerRequest.send('/bee/v1/bdaiassistant/feedBackForChat', {
            chatRecordId: data.serverId,
            type,
            feedBackContent,
        });
        if (res.code !== 0) {
            return false;
        }

        modifyMessage(data.id, { feedbackType: type });
        type === FeedbackType.dislike && message.info('提交成功');
        return true;
    };
    const { getWidth } = useClientWidth();
    const copyData = getCopyData(data.data);

    return (
        <Condition condition={[data.from === MessageFrom.left && !data.noFooter]}>
            <>
                <Divider style={{ marginTop: 10, marginBottom: 10 }} />
                <Row justify={'space-between'}>
                    <Condition condition={[[MessageStatus.done, MessageStatus.error].includes(data.localStatus)]}>
                        <span style={{ color: '#999', fontSize: 12 }}>
                            {data.isStopped ? '用户已取消会话' : '此条回答由AI生成'}
                        </span>
                        <Space
                            onClick={() => {
                                submitFeedback(FeedbackType.block_answer);
                                stopPollingMessage(true);
                            }}
                            className="pointer"
                        >
                            <div
                                style={{
                                    width: 13,
                                    height: 13,
                                    borderRadius: 6.5,
                                    border: '1px solid #666',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        width: 5,
                                        height: 5,
                                        border: '1px solid #666',
                                        background: '#666',
                                    }}
                                />
                            </div>
                            <span className="c_666">停止回答</span>
                        </Space>
                    </Condition>
                    <div className="pointer" style={{ display: 'flex', alignItems: 'center' }}>
                        {/* 复制按钮常驻 */}
                        <Condition
                            condition={[
                                data.localStatus === MessageStatus.done && data.from === MessageFrom.left && copyData,
                            ]}
                        >
                            <div
                                className={'h_center'}
                                onClick={() => {
                                    navigator.clipboard.writeText(copyData);
                                    message.success('复制成功~');
                                }}
                            >
                                <img style={{ width: 18, height: 18 }} src={CopyIcon} />
                            </div>
                        </Condition>
                        <Condition
                            condition={[
                                !data.feedbackType && // 未评价
                                    data.id === _.last(messageArray)?.id && // 最新消息
                                    data.localStatus === MessageStatus.done, // 回答完成
                                data.feedbackType,
                            ]}
                        >
                            <Space className="pointer" align={'center'} style={{ marginLeft: 16, columnGap: 16 }}>
                                <div
                                    onClick={() => {
                                        submitFeedback(FeedbackType.like);
                                    }}
                                    className={'h_center'}
                                >
                                    <img style={{ width: 18, height: 18 }} src={GoodIcon} />
                                </div>
                                <div
                                    className={'h_center'}
                                    onClick={() => {
                                        let inputText = '';
                                        modal.confirm({
                                            title: '您的反馈将有助于小蜜助手的进步',
                                            content: (
                                                <Input.TextArea
                                                    placeholder="请留下您的宝贵意见"
                                                    rows={8}
                                                    onChange={e => (inputText = e.target.value)}
                                                />
                                            ),
                                            width: getWidth(0.92),
                                            closable: true,
                                            cancelButtonProps: { style: { display: 'none' } },
                                            okText: '提交',
                                            icon: null,
                                            onOk: () => {
                                                if (!inputText) {
                                                    message.warning('请输入反馈内容');
                                                    return Promise.reject();
                                                }
                                                submitFeedback(FeedbackType.dislike, inputText);
                                            },
                                        });
                                    }}
                                >
                                    <img style={{ width: 18, height: 18 }} src={BadIcon} />
                                </div>
                            </Space>
                            <Condition
                                condition={[
                                    data.feedbackType === FeedbackType.like,
                                    data.feedbackType === FeedbackType.dislike,
                                ]}
                            >
                                <LikeFilled />
                                <DislikeFilled />
                            </Condition>
                        </Condition>
                    </div>
                </Row>
            </>
        </Condition>
    );
};
export default MessageFooter;
