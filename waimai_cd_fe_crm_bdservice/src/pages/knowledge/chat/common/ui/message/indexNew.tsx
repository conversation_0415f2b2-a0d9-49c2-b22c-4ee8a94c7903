import { CSSProperties, useState } from 'react';
import {
    AdditionMessage,
    MarkdownMessage,
    MessageData,
    MessageFrom,
    MessageStatus,
    ThinkContentMessage,
} from '../../type/message';
import { Divider, Image, Row, Space, Spin, Tag, Typography } from 'antd';
import MessageContext from './messageContext';
import MessageFooter from './messageFooter';
import useGetChatHistory from '@src/pages/knowledge/chat/common/service/getChatHistory';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useSendMessage, { EntryPoint } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import Condition from '@src/components/Condition/Condition';
import { LoadingOutlined } from '@ant-design/icons';
import ErrorImg from '@src/assets/images/chat/error.png';
import MarkdownRender from '@src/pages/knowledge/chat/common/ui/messageContent/MarkdownRender';
import { renderStandardQuestionItem } from './messageItemRender';
import ArrowRightImg from '@src/assets/images/chat/arrow_right.png';
import ThinkContent from '@src/pages/knowledge/chat/common/ui/messageContent/ThinkContent';
import _ from 'lodash';
import useClientWidth from '../../utils/screenWidth';

export const getStyle = (style: (CSSProperties | undefined)[]) => {
    return style.filter(Boolean).reduce((acc, cur) => {
        return { ...acc, ...cur };
    }, {});
};
interface Props {
    data: Partial<MessageData>;
    noFooter?: boolean;
    style?: CSSProperties;
}
const MessageUI = ({ data, noFooter, style = {} }: Props) => {
    const getChatHistory = useGetChatHistory();
    const messageArray = useAiStore(state => state.messageArray);
    const sendMessage = useSendMessage();
    const { getWidth } = useClientWidth();

    if (data.from === MessageFrom.middle) {
        const isOpenSession = data.data?.[0]?.localId?.startsWith('openSession_');
        // 系统消息
        return (
            <div
                style={{ paddingLeft: 80, paddingRight: 80 }}
                className={isOpenSession ? 'pointer' : ''}
                onClick={() => {
                    if (isOpenSession && messageArray[0].id === data.id) {
                        getChatHistory();
                    }
                }}
            >
                <Divider>
                    <span style={{ color: '#666', fontSize: 12 }}>{data.data?.[0].insert as string}</span>
                </Divider>
            </div>
        );
    }
    const withOptions = data.data?.some(item => ['options', 'form'].includes(item.type));
    const withWebview = data.data?.some(item => ['webview', 'rejectCard'].includes(item.type));
    const [withGradientHeader, setWithGradientHeader] = useState(false);
    const addition = data.data?.find(item => item.type === 'addition') as AdditionMessage;
    const onlyAddition = data.data?.every(item => item.type === 'addition');
    const file = addition?.insert.addition.additionList;

    return (
        <MessageContext.Provider
            value={{
                msgId: data.id || '',
                serverId: data.serverId || '',
                history: Boolean(data.history),
                withGradientHeader,
                setWithGradientHeader,
            }}
        >
            <div
                className="message-container"
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    ...(data.from === MessageFrom.left
                        ? { alignItems: 'flex-start' }
                        : { alignItems: 'flex-end', paddingLeft: 54 }),
                    ...style,
                }}
            >
                <Condition condition={[file?.length]}>
                    <Space align="end" style={{ marginTop: 16 }}>
                        {file?.map(item => {
                            if (item.type === 'image') {
                                return (
                                    <Image
                                        src={item.src}
                                        width={80}
                                        height={80}
                                        key={item.src}
                                        style={{ objectFit: 'cover' }}
                                    />
                                );
                            }
                            return null;
                        })}
                    </Space>
                </Condition>

                <Condition condition={[!onlyAddition]}>
                    <Row
                        style={{ width: '100%', alignItems: 'center' }}
                        justify={data.from === MessageFrom.right ? 'end' : 'start'}
                    >
                        {/*消息主体*/}
                        <div
                            style={{
                                marginTop: 16,
                                padding: 12,
                                lineHeight: '20px',
                                borderRadius: 16,
                                position: 'relative',
                                border: '1px solid #FFFFFF',
                                boxShadow: '0px 0px 5px 0px #EEEEEE3F',
                                width: withOptions || withWebview ? '100%' : undefined, // 带有选项消息则提前铺满宽度，防止选项消息忽长忽短不美观
                                ...(data.from === MessageFrom.left
                                    ? {
                                          background: withGradientHeader
                                              ? 'linear-gradient(180deg, #FFFEF2 5px, #FFFFFF 76px),linear-gradient(180deg, #FFFFFF00 0%, #FFFFFF 52px)'
                                              : '#fff',
                                          minWidth: 300,
                                          maxWidth: getWidth() - 80,
                                      }
                                    : { background: '#FEF9D4' }),
                                ...(data.config?.style || {}), // 支持消息体内自定义样式
                            }}
                        >
                            <Condition
                                condition={[
                                    data.from === MessageFrom.right && data.localStatus === MessageStatus.sending,
                                    data.from === MessageFrom.right && data.localStatus === MessageStatus.error,
                                ]}
                            >
                                <Spin
                                    indicator={
                                        <LoadingOutlined
                                            spin
                                            className={'float-btn'}
                                            style={{
                                                marginTop: 8,
                                                marginRight: 18,
                                                position: 'absolute',
                                                left: -50,
                                                top: 0,
                                                zIndex: 10,
                                            }}
                                        />
                                    }
                                />
                                <Image
                                    src={ErrorImg}
                                    width={30}
                                    className={'float-btn pointer'}
                                    style={{
                                        marginTop: 18,
                                        marginRight: 18,
                                        position: 'absolute',
                                        left: -50,
                                        top: -35,
                                        zIndex: 10,
                                    }}
                                    preview={false}
                                    onClick={() => {
                                        data.retryParams?.length && sendMessage(...data.retryParams);
                                    }}
                                />
                            </Condition>
                            {/*如果是回答消息则在消息体为空时展示Spin*/}
                            <Condition
                                condition={[
                                    !data.data?.length &&
                                        data.from === MessageFrom.left &&
                                        data.localStatus === MessageStatus.generating,
                                ]}
                            >
                                <Spin />
                            </Condition>
                            <ThinkContent
                                key="merged_think_content"
                                status={_.last(data.typingData)?.type === 'thinkContent' ? 'thinking' : 'done'}
                                children={data?.data?.filter(v => v.type === 'thinkContent') as ThinkContentMessage[]}
                            />
                            {/*markdown数据特殊处理*/}
                            <MarkdownRender
                                data={(data.data as any)
                                    ?.filter(v => v.type === 'markdown')
                                    .map((v: MarkdownMessage) => v.insert.markdown.text)
                                    .join('')}
                            />
                            {/*标准问或多轮数据*/}
                            {data.data?.map(renderStandardQuestionItem(sendMessage, data.serverId, data.history))}
                            <Condition condition={[!noFooter]}>
                                <MessageFooter data={data} />
                            </Condition>
                        </div>
                    </Row>
                </Condition>

                {/* 猜你想问 */}
                <Condition condition={[data?.suffixOptions?.options?.length]}>
                    <Space style={{ marginTop: 10 }} direction={'vertical'} size={'small'}>
                        <div>
                            <Typography.Text className={'c_999'}>{data?.suffixOptions?.descriptions}</Typography.Text>
                        </div>
                        <Row gutter={12}>
                            {data?.suffixOptions?.options?.map(({ content }) => (
                                <Tag
                                    bordered={false}
                                    className="pointer mt_10"
                                    key={content}
                                    color={'#fff'}
                                    style={{
                                        padding: '8px 16px',
                                        borderRadius: 12,
                                    }}
                                    onClick={() => {
                                        sendMessage(content, { entryPoint: EntryPoint.floating_option_list });
                                    }}
                                >
                                    <Typography.Text
                                        ellipsis={{ tooltip: content }}
                                        style={{ maxWidth: 400, fontSize: 14 }}
                                    >
                                        {content}
                                        <img src={ArrowRightImg} width={9} height={9} style={{ marginLeft: 4 }} />
                                    </Typography.Text>
                                </Tag>
                            ))}
                        </Row>
                    </Space>
                </Condition>
            </div>
        </MessageContext.Provider>
    );
};
export default MessageUI;
