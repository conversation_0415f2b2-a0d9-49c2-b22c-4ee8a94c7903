import _ from 'lodash';

interface File {
    key: string;
    type: 'image' | 'file';
    src?: string;
    localSrc: string;
    status: 'uploading' | 'success' | 'error';
}
const defaultState = {
    file: [] as File[],
};

type State = typeof defaultState;
const getActions = (set: Setter, get: Getter) => ({
    addFile: (file: Omit<File, 'key'>) => {
        const draftFile = { ...file, key: _.uniqueId('file_') };
        set({
            file: [...get().file, draftFile],
        });
        return draftFile.key;
    },
    setFile: (file: Omit<File, 'key'>[]) => {
        const draftFile = file.map(f => ({ ...f, key: _.uniqueId('file_') }));
        set({
            file: draftFile,
        });
        return draftFile.map(f => f.key);
    },
    updateFile: (key: string, file: Partial<File>) => {
        set({
            file: get().file.map(f => (f.key === key ? { ...f, ...file } : f)),
        });
    },
    removeFile: (key: string) => {
        set({
            file: get().file.filter(f => f.key !== key),
        });
    },
    clearFile: () => {
        set({
            file: [],
        });
    },
    getValidFile: () => {
        return get().file.filter(f => f.status === 'success');
    },
});
export type FileStateAndActions = State & ReturnType<typeof getActions>;
type Setter = (v: Partial<State>) => void;
type Getter = () => FileStateAndActions;

export default { getActions, defaultState, key: 'file' };
