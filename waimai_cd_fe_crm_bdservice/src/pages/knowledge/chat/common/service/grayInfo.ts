import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useAsyncEffect } from 'ahooks';
import { useState } from 'react';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';

const useGrayInfo = () => {
    const [grayInfo, setGrayInfo] = useState<{ gray: boolean }>();
    const { data: bizInfo } = useBizInfo();
    useAsyncEffect(async () => {
        if (!bizInfo?.bizId) {
            return;
        }
        const res = await apiCaller.get('/bee/v1/bdaiassistant/getGraySwitch', { bizId: bizInfo.bizId });
        if (res.code !== 0) {
            return;
        }
        setGrayInfo(res.data);
    }, [bizInfo]);
    return grayInfo as { gray: boolean } | undefined;
};
export default useGrayInfo;
