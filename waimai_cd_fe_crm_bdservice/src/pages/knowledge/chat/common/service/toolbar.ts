import { MessageFrom, MessageStatus, OperationType, OptionItem } from '../type/message';
import useSendMessage, { EntryPoint } from './sendMessage/sendMessage';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import { useEffect, useState } from 'react';
import _ from 'lodash';
import { openFullscreenIframe } from '../utils/fullscreenIframe';

// 缓存机制
const cache = {
    options: [] as OptionItem[],
    timestamp: 0,
    listeners: [] as (() => void)[],
    // 请求锁，防止并发请求
    requestPromise: null as Promise<void> | null,

    // 添加监听器
    subscribe(listener: () => void) {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    },

    // 通知所有监听器
    notify() {
        this.listeners.forEach(listener => listener());
    },

    // 更新数据
    update(options: OptionItem[]) {
        this.options = options;
        this.timestamp = Date.now();
        this.notify();
    },
};

const useToolbar = () => {
    const sendMessage = useSendMessage();
    const appendMessage = useAiStore(v => v.appendMessage);
    const callerRequest = useCallerRequest();
    const sessionId = useAiStore(v => v.sessionId);
    const [options, setOptions] = useState<OptionItem[]>(cache.options);
    const setUiConfig = useAiStore(v => v.setUiConfig);

    // 订阅缓存变更
    useEffect(() => {
        const unsubscribe = cache.subscribe(() => {
            setOptions([...cache.options]);
        });
        return unsubscribe;
    }, []);

    // 初始加载和sessionId变化时获取数据
    useEffect(() => {
        if (sessionId) {
            // 如果没有缓存数据，或者没有时间戳，或者缓存正在加载中但请求已经结束，则发起请求
            if (!cache.options.length || !cache.timestamp) {
                fetchToolbarOptions();
            }
        }
    }, [sessionId]);

    // 获取工具栏选项的函数
    const fetchToolbarOptions = async () => {
        if (!sessionId?.length) {
            return;
        }

        // 如果已经有请求在进行中，则等待该请求完成
        if (cache.requestPromise) {
            return cache.requestPromise;
        }

        // 创建新的请求
        const requestPromise = (async () => {
            try {
                const res = await callerRequest.get('/bee/v1/bdaiassistant/common/getToolbarConfig', {});

                if (res.code === 0) {
                    cache.update((res.data?.options as OptionItem[]) || []);
                } else {
                    cache.update([]);
                }
            } catch (error) {
                console.error('获取工具栏选项失败:', error);
                cache.update([]);
            } finally {
                cache.requestPromise = null;
            }
        })();

        // 保存请求Promise
        cache.requestPromise = requestPromise;

        return requestPromise;
    };

    const showHome = useAiStore(v => v.config.ui.showHome);
    const onToolbarPress = (item: OptionItem, entryPoint: string = EntryPoint.toolbar) => {
        if (item.operationType === OperationType.JUMP_LINK) {
            if (item.openWay === 'inCurrentTabFull') {
                window.parent.postMessage({ type: 'ASSISTANT_DRAWER', data: { openType: 'expand' } }, '*');
                setUiConfig({ fullScreen: true, showBackButton: true, isCopilotIframeShow: true });
                setTimeout(() => {
                    openFullscreenIframe(item.url);
                }, 50);
            } else {
                openLink(item.url);
            }
        } else if (item.operationType === OperationType.SEND_MESSAGE) {
            showHome &&
                appendMessage({
                    localStatus: MessageStatus.done,
                    from: MessageFrom.middle,
                    id: _.uniqueId('message_'),
                    data: [
                        {
                            insert: '点击查看历史会话',
                            type: 'text',
                            localId: _.uniqueId('openSession_'),
                        },
                    ],
                });
            sendMessage(
                item.content,
                {
                    abilityType: item.abilityType,
                    entryPoint,
                },
                false, // 不带图
            );
        } else {
            console.log(item, '无效操作');
        }
    };

    return {
        options,
        onToolbarPress,
        refreshToolBar: fetchToolbarOptions,
    };
};

export default useToolbar;
