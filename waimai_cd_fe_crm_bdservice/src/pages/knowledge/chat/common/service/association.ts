import useSendMessage, { EntryPointType } from './sendMessage/sendMessage';
import { useRequest } from 'ahooks';
import useAiStore from '../data/core';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';

const useAssociation = () => {
    const sendMessage = useSendMessage();
    const text = useAiStore(state => state.inputText);
    const setInputText = useAiStore(state => state.setInputText);
    const callerRequest = useCallerRequest();
    const { data: bizInfo } = useBizInfo();
    const fetchAssociation = async () => {
        if (text.length < 2) {
            return;
        }

        // setVisible(true);
        const res = await callerRequest.get(
            '/bee/v1/bdaiassistant/getRelatedQuestion',
            { input: text, bizId: bizInfo?.bizId },
            { silent: true },
        );

        if (res.code !== 0) {
            return;
        }

        return res.data.questions;
    };

    const { data: associationData = [], mutate } = useRequest(fetchAssociation, {
        refreshDeps: [text],
        throttleWait: 300,
    });

    const onAssociationPress = (text: string) => {
        mutate([]);
        sendMessage(text, { entryPointType: EntryPointType.ASSOCIATION }, true, () => setInputText(''));
    };

    return {
        associationData,
        setAssociationData: mutate,
        onAssociationPress,
    };
};
export default useAssociation;
