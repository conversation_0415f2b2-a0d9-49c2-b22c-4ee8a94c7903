import { useEffect, useState } from 'react';
import { useQuery } from '@src/hooks/useQuery';
import useAiStore from '../data/core';
import { MessageFrom, MessageStatus } from '../type/message';
import { parseDataFromMsgComponents } from './sendMessage/adaptServerMessage';
import _ from 'lodash';

// 默认mock数据 - 直接使用fetchAnswer接口返回的数据格式
const defaultMockData = {
    code: 0,
    msg: '成功',
    data: {
        questionMsgId: '97900',
        msgId: '97903',
        type: 2,
        abilityType: 1,
        status: 1,
        sensitive: false,
        msgType: 1,
        currentContent: [
            {
                type: 'text',
                insert: '小蜜正在努力诊断中，预计5分钟完成～请放心，我会及时通知你，你也可随时在上方',
            },
            {
                type: 'inlineImage',
                insert: {
                    inlineImage:
                        'https://s3plus.meituan.net/bdaiassistant-public/%E5%B8%A6%E6%95%B0%E5%AD%97%E7%9A%84%E8%BF%9B%E8%A1%8C%401x.png',
                },
            },
            {
                type: 'text',
                insert: '查看诊断进度哦～',
            },
        ],
        previousContent: null,
        prefixTextContent: null,
        postTextContent: null,
        selectionItems: null,
        imageList: null,
        feedbackType: null,
        subAbilityType: null,
        hasNext: false,
        pageNum: null,
        respTime: 1753322838853,
        tags: null,
    },
};

/**
 * Mock模式Hook - 检测devMock参数并自动渲染mock数据
 */
const useMockMode = () => {
    const { query, loaded } = useQuery(['devMock']);
    const [isMockMode, setIsMockMode] = useState(false);
    const appendMessage = useAiStore(v => v.appendMessage);
    const appendSessionId = useAiStore(v => v.appendSessionId);
    const setShowHome = useAiStore(v => v.setShowHome);
    const setTypingConfig = useAiStore(v => v.setTypingConfig);

    // 渲染mock数据
    const renderMockData = async (mockData: any) => {
        console.log('Mock模式：开始渲染数据', mockData);

        // 隐藏首页
        setShowHome(false);

        // 在mock模式下禁用打字动画，确保消息能够正常显示
        setTypingConfig({ needPlay: false });

        // 生成mock sessionId
        appendSessionId('mock_session_' + Date.now());

        // 添加用户问题消息
        appendMessage({
            id: _.uniqueId('mock_question_'),
            data: [{ insert: '这是 mock 发出，请忽略', type: 'text' }],
            from: MessageFrom.right,
            status: MessageStatus.done,
            localStatus: MessageStatus.done,
        });

        // 延迟一下再显示回答
        setTimeout(() => {
            try {
                console.log('Mock模式：开始解析currentContent', mockData.data.currentContent);

                // 参考fetchAnswer接口的处理逻辑
                let messageDataParsed: any[] = [
                    {
                        insert: mockData.data.currentContent,
                        type: 'text',
                        localId: _.uniqueId('messageContent_'),
                    },
                ];

                try {
                    messageDataParsed = Array.isArray(mockData.data.currentContent)
                        ? mockData.data.currentContent
                        : (JSON.parse(mockData.data.currentContent) as any[]);
                    // 数字字符串不会产生错误，但不符合预期
                    if (typeof messageDataParsed === 'number') {
                        throw new Error('currentContent是字符串型数字');
                    }
                } catch (e) {
                    // 解析失败则直接展示
                    console.log('Mock模式：JSON解析失败，使用纯文本', e);
                }
                console.log('Mock模式：解析后的messageDataParsed', messageDataParsed);
                const { data, suffixOptions, config } = parseDataFromMsgComponents(messageDataParsed);
                console.log('Mock模式：解析后的消息数据', { data, suffixOptions, config });

                // 添加AI回答消息
                const messageData = {
                    id: _.uniqueId('mock_answer_'),
                    serverId: mockData.data.msgId,
                    data,
                    typingData: data, // 添加typingData字段
                    suffixOptions,
                    config,
                    from: MessageFrom.left,
                    localStatus: MessageStatus.done,
                    status: MessageStatus.done as MessageStatus.done,
                };

                appendMessage(messageData);
            } catch (error: any) {
                console.error('Mock模式：解析数据失败', error);
            }
        }, 150);
    };

    // 监听URL参数变化
    useEffect(() => {
        if (!loaded) return;

        const devMock = query.devMock && location.hostname === 'localhost';
        if (devMock) {
            setIsMockMode(true);

            // 可以根据devMock参数值加载不同的mock数据
            // 这里先使用默认数据
            renderMockData(defaultMockData);
        } else {
            setIsMockMode(false);
        }
    }, [query.devMock, loaded]);

    return {
        isMockMode,
        renderMockData,
    };
};

export default useMockMode;
