import { useAsyncEffect } from 'ahooks';
import { useRef, useState } from 'react';
import useAiStore from '../data/core';
import { MessageFrom, MessageStatus } from '../type/message';
import _ from 'lodash';
import adaptOption from '../adaptor/optionAdaptor';
import useSourceParams from '@src/pages/knowledge/chat/common/service/sourceParams';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { App, Spin, Typography } from 'antd';
import useInstruction from '@src/pages/knowledge/chat/common/service/instruction';
import { adaptMessageArray } from '@src/pages/knowledge/chat/common/service/getChatHistory';
import { AbilityType, useStartPolling } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useGrayInfo from '@src/pages/knowledge/chat/common/service/grayInfo';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';
import useReleaseGray from './releaseGray';

export const VERSION_PARAMS_2 = {
    version: 'V2',
};
export const VERSION_PARAMS_3 = {
    version: 'V3',
};

const useOpenSession = props => {
    const [loading, setLoading] = useState(false);
    const appendMessage = useAiStore(v => v.appendMessage);
    const sourceParams = useSourceParams();
    const appendSessionId = useAiStore(v => v.appendSessionId);
    const { data: bizInfo } = useBizInfo();
    const { modal, message } = App.useApp();
    const instruction = useInstruction();
    const startPolling = useStartPolling();
    const grayInfo = useGrayInfo();
    const releaseGray = useReleaseGray();
    const setShowHome = useAiStore(v => v.setShowHome);

    const createChat = async () => {
        const res = await apiCaller.post('/bee/v1/bdaiassistant/openSession', {
            ...VERSION_PARAMS_3,
            ...sourceParams,
        });
        if (res.code !== 0) {
            return;
        }

        appendSessionId(res.data.sessionId);

        // 添加历史消息
        if (res.data.existChatRecord) {
            // 新版本首页，等待用户发出第一条消息后才展示“查看历史对话”
            !releaseGray &&
                appendMessage({
                    localStatus: MessageStatus.done,
                    from: MessageFrom.middle,
                    id: _.uniqueId('message_'),
                    data: [
                        {
                            insert: '点击查看历史会话',
                            type: 'text',
                            localId: _.uniqueId('openSession_'),
                        },
                    ],
                });
        }

        // 添加首页问消息
        try {
            const data = JSON.parse((res.data as any)?.currentContent);
            const optionsMessage = data.find(v => v.type === 'options');
            let finalData = data;
            if (optionsMessage) {
                const finalOptionMessage = adaptOption(optionsMessage, res.data.hasNext);
                finalData = data.map(v => {
                    if (v.type === 'options') {
                        return finalOptionMessage;
                    }
                    return v;
                });
            }
            // 新版本首页不再展示高频问 options消息
            !releaseGray &&
                appendMessage({
                    id: _.uniqueId('message_'),
                    data: finalData,
                    typingData: finalData,
                    from: MessageFrom.left,
                    status: MessageStatus.done,
                    localStatus: MessageStatus.done,
                    noFooter: true,
                });
        } catch (e) {
            console.log(e);
        }

        if (sourceParams.aichat_scene_id) {
            const params = Object.keys(sourceParams)
                .filter(v => v.startsWith('aichat_scene'))
                .map(v => ({ [v.replace('aichat_scene_', '')]: sourceParams[v] }))
                .reduce((pre, cur) => ({ ...pre, ...cur }));
            const messagesRes = await apiCaller.post('/bee/v1/bdaiassistant/triggerScene', {
                ...params,
                // @ts-ignore
                bizId: bizInfo?.bizId,
                sessionId: res.data.sessionId,
            });
            if (messagesRes.code !== 0) {
                return;
            }
            setShowHome(false);
            const messages = messagesRes.data as any[];
            appendMessage(adaptMessageArray(messages));

            // 如果最后一个消息为问题，则自动拉取回答
            if (messages.length) {
                const lastMsg = _.last(messages);
                if (lastMsg.msgType !== MessageFrom.right) {
                    return;
                }
                lastMsg.questionMsgId = lastMsg.id;
                startPolling({ questionMsgId: lastMsg.id, abilityType: AbilityType.GENERAL });
            }
        }
    };

    const { getWidth } = useClientWidth();
    const existSignRecord = async () => {
        const res = await apiCaller.get('/bee/v1/bdaiassistant/existSignRecord', {});
        if (res.code === 0 && res.data.sign) {
            if (res.data.sign) {
                return createChat();
            }
        }
        modal.confirm({
            title: '签署协议',
            okText: '同意并继续',
            width: getWidth(0.92),
            icon: null,
            okButtonProps: {
                className: 'custom-assistant-rule-confirm-btn',
            },
            onOk: () => {
                createChat();
                apiCaller.send('/bee/v1/bdaiassistant/signInstructions', {}, { silent: true });
            },
            onCancel: () => {
                window.parent.postMessage({ type: 'ASSISTANT_CLOSE' }, '*');
            },
            content: (
                <Spin spinning={!instruction}>
                    <Typography.Text>
                        使用前请阅读
                        <Typography.Link
                            onClick={() => {
                                modal.confirm({
                                    title: '使用说明',
                                    okText: '我知道了',
                                    width: getWidth(0.92),
                                    icon: null,
                                    closable: true,
                                    cancelButtonProps: { style: { display: 'none' } },
                                    content: (
                                        <div
                                            style={{
                                                overflowY: 'scroll',
                                                height: document.body.clientHeight * 0.6,
                                                whiteSpace: 'pre-wrap',
                                                scrollbarWidth: 'thin',
                                            }}
                                        >
                                            {instruction}
                                        </div>
                                    ),
                                });
                            }}
                        >
                            《使用说明》
                        </Typography.Link>
                        ，让小蜜成为您专属助手吧~
                    </Typography.Text>
                </Spin>
            ),
        });
    };
    const inited = useRef(false);
    useAsyncEffect(async () => {
        if (!instruction || !grayInfo || inited.current) {
            return;
        }

        inited.current = true; // 保证以下逻辑只执行一次，防止因instruction或者grayInfo刷新而重建会话

        if (!grayInfo?.gray) {
            modal.confirm({
                width: getWidth(0.92),
                title: '暂无权限',
                content: '该功能正在内测中，很快会开放给全部同学使用。',
                okText: '我知道了',
                cancelButtonProps: { style: { display: 'none' } },
            });
            return;
        }
        setLoading(true);
        await existSignRecord();
        setLoading(false);
    }, [instruction, grayInfo]);

    return {
        loading,
    };
};
export default useOpenSession;
