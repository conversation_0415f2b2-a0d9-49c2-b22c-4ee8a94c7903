import { useState } from 'react';
import useCallerRequest from './request';
import { useRequest } from 'ahooks';

const useAnnouncement = () => {
    const [announcementVisible, setAnnouncementVisible] = useState(false);
    const [isExpanded, setIsExpanded] = useState(false);
    const callerRequest = useCallerRequest();

    const { data } = useRequest(
        async () => {
            const res = await callerRequest.get('/bee/v2/bdaiassistant/common/getAnnouncement', {});
            if (res.code === 0) {
                res?.data?.content?.length && setAnnouncementVisible(true);
                return res.data as any;
            }
            return {} as any;
        },
        { cacheKey: 'announcement' },
    );

    const toggleExpand = () => {
        setIsExpanded(prev => {
            return !prev;
        });
    };

    return {
        announcementVisible,
        announcement: data?.content,
        announcementId: data?.announcementId,
        isExpanded,
        toggleExpand,
        closeAnnouncement: () => {
            callerRequest
                .post('/bee/v2/bdaiassistant/common/closeAnnouncement', {
                    announcementId: data.announcementId,
                })
                .then(res => {
                    if (res.code === 0) {
                        setAnnouncementVisible(false);
                    }
                });
        },
    };
};

export default useAnnouncement;
