import { App } from 'antd';
import useAiStore from '../data/core';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { MessageFrom, MessageStatus } from '../type/message';
import _ from 'lodash';
import useSourceParams from '@src/pages/knowledge/chat/common/service/sourceParams';

export const useRefreshSession = () => {
    const { message } = App.useApp();
    const appendMessage = useAiStore(state => state.appendMessage);
    const sourceParams = useSourceParams();
    const appendSessionId = useAiStore(v => v.appendSessionId);
    const setDragged = useAiStore(v => v.setDragged);
    const getLatestSessionId = useAiStore(v => v.getLatestSessionId);

    // noRefreshMsg: 强制不显示刷新提示消息，用于会话过期后刷新会话时不展示提示消息
    return async (noRefreshMsg = false, callback = () => {}) => {
        // trackEvent('chat_refresh');

        setDragged(false);

        // @ts-ignore
        const res = await apiCaller.send('/bee/v1/bdaiassistant/refreshSession', {
            ...sourceParams,
            // @ts-ignore
            sessionId: getLatestSessionId(),
        });

        if (res.code !== 0) {
            return;
        }

        // @ts-ignore
        appendSessionId(res.data.sessionId);
        callback();

        if (!res.data.showRefreshMsg || noRefreshMsg) {
            return;
        }

        message.warning('已为您生成新的对话 ~ ');
        appendMessage({
            localStatus: MessageStatus.done,
            data: [{ insert: '问我新的问题吧', type: 'text' }],
            typingData: [{ insert: '问我新的问题吧', type: 'text' }],
            id: _.uniqueId('message_'),
            from: MessageFrom.middle,
        });
    };
};
export default useRefreshSession;
