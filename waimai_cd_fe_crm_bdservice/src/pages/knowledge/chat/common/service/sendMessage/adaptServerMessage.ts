// TODO 支持配置
// 抹平服务器数据和渲染所需数据的差异
import { Config, Message, SuffixOptionsMessage } from '@src/pages/knowledge/chat/common/type/message';
import _, { flow } from 'lodash';
import adaptOption from '@src/pages/knowledge/chat/common/adaptor/optionAdaptor';

export const castMediaMessage = (msgComponents: Message[]) => {
    let res: Message[] = [];
    msgComponents.forEach(message => {
        // 如果没有有效数据，则丢弃
        if (!message.insert) {
            return;
        }

        // 处理图片和视频数据
        if (message.type === 'image') {
            const lastEle = res[res.length - 1];
            if (lastEle?.type === 'media') {
                lastEle.insert.media.push({ [message.type]: message.insert[message.type] });
                return;
            }
            res = [...res, { type: 'media', insert: { media: [{ [message.type]: message.insert[message.type] }] } }];
        } else if (message.type === 'video') {
            const lastEle = res[res.length - 1];
            if (lastEle?.type === 'media') {
                lastEle.insert.media.push({ [message.type]: message.insert[message.type] });
                return;
            }
            res = [...res, { type: 'media', insert: { media: [{ [message.type]: message.insert[message.type] }] } }];
        } else if (message.type === 'options') {
            res = [...res, adaptOption(message)];
        } else {
            res = [...res, message];
        }
    });
    return res;
};

export const adaptServerMessageComponents = (msgComponents: Message[]) => {
    const splitTextMessage = (msgComponents: Message[]) => {
        let res: Message[] = [];
        msgComponents.forEach(message => {
            if (message.type === 'text' || message.type === 'styledText' || message.type === 'link') {
                res = [...res, ...message.insert.split('').map(v => ({ ...message, insert: v }))];
                return;
            }
            res = [...res, message];
        });
        return res;
    };

    return flow(splitTextMessage, castMediaMessage)(msgComponents);
};
export const parseDataFromMsgComponents = (msgData: Message[]) => {
    // suffixOptions & config处理
    const suffixOptions: SuffixOptionsMessage | undefined = msgData.find(
        v => v.type === 'suffixOptions',
    ) as SuffixOptionsMessage;
    const config: Config | undefined = msgData.find(v => v.type === 'config') as Config;
    msgData = msgData.filter(v => v.type !== 'suffixOptions' && v.type !== 'config');

    msgData = adaptServerMessageComponents(msgData).map(v => ({ ...v, localId: _.uniqueId('messageContent_') }));
    return { config: config?.insert?.config, suffixOptions: suffixOptions?.insert?.suffixOptions, data: msgData };
};
