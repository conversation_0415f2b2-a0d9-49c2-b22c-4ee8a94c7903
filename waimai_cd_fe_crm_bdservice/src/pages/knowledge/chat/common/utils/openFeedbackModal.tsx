import { apiCaller } from '@mfe/cc-api-caller-pc';
import { FeedbackData, RobotType, SubmitStatus } from './types';
import React, { CSSProperties, useEffect, useState } from 'react';
import RateContainer from './Rate/RateContainer';
import FeedbackResult from './feedbackResult';
import titleImg from '@src/assets/images/title.png';
import anonymousImg from '@src/assets/images/anonymous.png';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import { Button, Spin, Image, App, message } from 'antd';
import { getStyle } from '@src/pages/knowledge/chat/common/ui/message/indexNew';
import { useThrottleFn } from 'ahooks';
import { mapPropsToString } from '@src/pages/questions/App';
import { CloseOutlined } from '@ant-design/icons';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';

interface FeedbackModal {
    visible: boolean;
    configs: any;
    mosesId?: string;
    robotType: RobotType;
    onCancel: () => void;
}
const enum SubmitType {
    SUBMIT = 'submit',
    CLOSE = 'close',
}

const styles: Record<string, CSSProperties> = {
    container: {
        paddingBottom: 40,
        justifyContent: 'space-evenly',
        flex: 1,
        position: 'relative',
        borderTopLeftRadius: 10.5,
        borderTopRightRadius: 10.5,
        backgroundClip: 'padding-box',
        backgroundRepeat: 'no-repeat',
        padding: 20,
    },
    infospan: {
        fontSize: 16,
        color: '#222222',
        fontWeight: '500',
        lineHeight: '20px',
        textAlign: 'center',
        marginRight: 4,
    },
    paddingHorizontal: {
        paddingLeft: 12,
        paddingRight: 12,
    },
};

const FeedbackModal = (props: Partial<FeedbackModal>) => {
    const bizInfo = useBizInfo();
    const [submitStatus, setSubmitStatus] = useState(SubmitStatus.FERESH);
    const [cancelling, setCancelling] = useState(false);

    const [data, setData] = useState<Partial<FeedbackData>>({
        star: undefined,
        tips: [],
        comment: undefined,
        desc: undefined,
    });

    const onSubmit = async () => {
        if (submitStatus === SubmitStatus.DOING || cancelling) {
            return;
        }
        setSubmitStatus(SubmitStatus.DOING);
        const params = {
            ...bizInfo,
            ...data,
            value: data.star,
            robotId: props.mosesId,
            type: SubmitType.SUBMIT,
            robotType: props.robotType,
        };
        delete params.star;
        // @ts-ignore
        const res = await apiCaller.send('/bee/v1/bdaiassistant/w/moses/comment', params);

        if (res.code !== 0) {
            setSubmitStatus(SubmitStatus.FERESH);
            return;
        }

        setSubmitStatus(SubmitStatus.DONE);
    };

    const rawCancel = () => {
        setData({
            star: undefined,
            tips: [],
            comment: undefined,
            desc: undefined,
        });

        props?.onCancel?.();
        setSubmitStatus(SubmitStatus.FERESH);
    };

    const cancelTrack = () => {
        if (cancelling) {
            return;
        }
        setCancelling(true);
        apiCaller
            // @ts-ignore
            .send('/bee/v1/bdaiassistant/w/moses/comment', {
                ...bizInfo,
                robotId: props.mosesId,
                type: SubmitType.CLOSE,
                robotType: props.robotType,
            })
            .then(() => setCancelling(false));
    };

    const onCancel = () => {
        // 如果是没有提交直接关闭弹窗的，需要发个请求
        if (submitStatus === SubmitStatus.FERESH) {
            cancelTrack();
        }
        rawCancel();
    };

    const { run: throttledCancel } = useThrottleFn(onCancel, {
        wait: 5000,
        trailing: false,
    });

    const submitDisabled = !data.star;

    useEffect(() => {
        if (props.visible) {
            return;
        }

        setSubmitStatus(SubmitStatus.FERESH);
    }, [props.visible]);

    const content =
        submitStatus !== SubmitStatus.DONE ? (
            <div style={styles.container}>
                <div
                    style={{
                        width: '100%',
                        backgroundRepeat: 'no-repeat',
                        marginBottom: 8,
                        justifyContent: 'center',
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            ...styles.paddingHorizontal,
                        }}
                    >
                        <Image src={titleImg} style={{ width: 162, height: 25 }} />
                        <CloseOutlined onClick={onCancel} />
                    </div>
                    <div
                        style={getStyle([
                            { display: 'flex', flexDirection: 'row', alignItems: 'center', marginTop: 16 },
                            styles.paddingHorizontal,
                        ])}
                    >
                        <span style={styles.infospan}>您的问题是否被解决？</span>
                        <Image src={anonymousImg} style={{ width: 24, height: 15 }} preview={false} />
                    </div>
                </div>

                <RateContainer configs={props.configs} data={data as any} onChange={setData} />
                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                        type="primary"
                        style={{ height: 40, marginLeft: 12, marginRight: 12 }}
                        disabled={submitDisabled || submitStatus !== SubmitStatus.FERESH}
                        onClick={onSubmit}
                        loading={submitStatus === SubmitStatus.DOING}
                    >
                        提交
                    </Button>
                </div>
            </div>
        ) : (
            <FeedbackResult star={data.star as any} onCancel={rawCancel} />
        );
    return content;
};

const useOpenFeedbackModal = ({ onCancel }) => {
    const { modal } = App.useApp();

    const [data, setData] = useState<any>();
    const { data: bizInfo } = useBizInfo();
    const fetchFeedbackData = async () => {
        try {
            const res = await apiCaller.get(
                // @ts-ignore
                '/bee/v1/bdaiassistant/fetchRobot',
                mapPropsToString({
                    bizId: bizInfo?.bizId,
                    tenantId: bizInfo?.tenantId,
                }),
            );

            if (res.code !== 0) {
                return;
            }

            setData(res.data);
            return res.data;
        } catch (e) {
            message.error('网络异常，请稍后重试');
        }
    };

    const [feedbackConfig, setFeedbackConfig] = useState<any>([]);
    const fetchFeedbackConfig = async () => {
        const res = await apiCaller.get(
            // @ts-ignore
            '/bee/v1/bdaiassistant/r/moses/comment/config',
            { bizId: bizInfo?.bizId, tenantId: bizInfo?.tenantId, newConfig: true },
            {
                silent: true,
            },
        );
        // mock data
        // {
        //     "code": 0,
        //     "msg": "成功",
        //     "data": [
        //     {
        //         "value": 1,
        //         "desc": "未解决",
        //         "tips": [
        //             "答案无解决力",
        //             "交互复杂",
        //             "答非所问",
        //             "没找到我的问题"
        //         ],
        //         "showComment": true
        //     },
        //     {
        //         "value": 2,
        //         "desc": "解决",
        //         "tips": [
        //             "答案有解决力",
        //             "交互便捷",
        //             "快速找到答案"
        //         ],
        //         "showComment": true
        //     }
        // ]
        // }

        if (res.code !== 0) {
            return;
        }
        setFeedbackConfig(res.data);
        return res.data;
    };
    useEffect(() => {
        if (!bizInfo) {
            return;
        }
        fetchFeedbackConfig();
        fetchFeedbackData();
    }, [bizInfo]);
    const { getWidth } = useClientWidth();
    return () => {
        if (!feedbackConfig?.length) {
            return onCancel?.();
        }
        let modalIns: any = {};
        modalIns = modal.confirm({
            icon: null,
            footer: null,
            closable: false,
            style: { padding: 0 },
            className: 'no-padding',
            onClose: () => {},
            width: getWidth(0.92),
            content: (
                <Spin spinning={!feedbackConfig}>
                    <FeedbackModal
                        configs={feedbackConfig}
                        mosesId={data?.id || data?.aiUrl}
                        robotType={data?.robotType}
                        onCancel={() => {
                            modalIns?.destroy?.();
                            onCancel?.();
                        }}
                    />
                </Spin>
            ),
        });
        return modalIns;
    };
};
export default useOpenFeedbackModal;
