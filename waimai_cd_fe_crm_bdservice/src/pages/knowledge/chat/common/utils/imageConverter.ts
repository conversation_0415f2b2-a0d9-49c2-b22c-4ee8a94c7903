import { apiCaller } from '@mfe/cc-api-caller-pc';

/**
 * 检查图片URL是否需要转换
 * @param imageUrl 图片链接
 * @returns 是否需要转换
 */
export const shouldConvertImage = (imageUrl: string): boolean => {
    return (
        imageUrl.startsWith('https://km.sankuai.com') ||
        imageUrl.startsWith('https://km.it.st.sankuai.com') ||
        imageUrl.startsWith('https://km.it.test.sankuai.com')
    );
};

/**
 * 调用接口转换Wiki图片链接为S3链接
 * @param wikiPictureUrl 原始Wiki图片链接
 * @returns 转换后的S3链接，转换失败时返回null
 */
export const convertWikiPicToS3Url = async (wikiPictureUrl: string): Promise<string | null> => {
    try {
        const res = await apiCaller.get('/bee/v2/bdaiassistant/common/convertWikiPicToS3Url', {
            wikiPictureUrl,
        });

        if (res.code !== 0) {
            console.warn('图片链接转换失败:', res.msg || '未知错误');
            return null;
        }

        return res.data?.s3Url || null;
    } catch (error) {
        console.error('图片链接转换接口调用失败:', error);
        return null;
    }
};

/**
 * 图片转换缓存，避免重复转换同一张图片
 */
const conversionCache = new Map<string, string | null>();

/**
 * 带缓存的图片链接转换函数
 * @param wikiPictureUrl 原始Wiki图片链接
 * @returns 转换后的S3链接，转换失败时返回null
 */
export const convertWikiPicToS3UrlWithCache = async (wikiPictureUrl: string): Promise<string | null> => {
    // 检查缓存
    if (conversionCache.has(wikiPictureUrl)) {
        return conversionCache.get(wikiPictureUrl) || null;
    }

    // 执行转换
    const s3Url = await convertWikiPicToS3Url(wikiPictureUrl);

    // 缓存结果
    s3Url && conversionCache.set(wikiPictureUrl, s3Url);

    return s3Url;
};

/**
 * 清理转换缓存（可选，用于内存管理）
 */
export const clearConversionCache = (): void => {
    conversionCache.clear();
};
