import { Message } from '../type/message';

const getCopyData = (data: Message[] | string): string => {
    if (!data) {
        return '';
    }
    if (typeof data === 'string') {
        try {
            data = JSON.parse(data) as Message[];
        } catch (error) {
            return data as string;
        }
    }

    const res: string[] = [];
    for (const item of data) {
        switch (item.type) {
            case 'text':
                res.push(item.insert);
                break;
            case 'image':
                res.push(`![](${item.insert.image})`);
                break;
            case 'link':
                res.push(`[${item.insert}](${item?.attributes?.link})`);
                break;
            case 'markdown':
                res.push(item.insert.markdown.text);
                break;
        }
    }
    return res.join('');
};

export default getCopyData;
