import React from 'react';
import n2 from '@src/assets/images/negative_unactive.png';
import n4 from '@src/assets/images/positive_unactive.png';
import p2 from '@src/assets/images/negative_active.png';
import p4 from '@src/assets/images/positive_active.png';
import { RateLevel } from '../types';
import { Button, Image } from 'antd';
import { getStyle } from '@src/pages/knowledge/chat/common/ui/message/indexNew';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';

type Item = any;
const enum Status {
    POSITIVE = 'p',
    NEGATIVE = 'n',
}

const keygen = (l: RateLevel, s: Status) => `${l}_${s}`;

const IconMap = {
    [keygen(RateLevel.ONE, Status.POSITIVE)]: p2,
    [keygen(RateLevel.ONE, Status.NEGATIVE)]: n2,
    [keygen(RateLevel.TWO, Status.POSITIVE)]: p4,
    [keygen(RateLevel.TWO, Status.NEGATIVE)]: n4,
};

interface RateItem {
    current: RateLevel;
    onPress: (v: { star: RateLevel; desc: string }) => void;
}

const styles = {
    container: {
        marginHorizontal: 2,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        height: 34,
        backgroundColor: 'rgba(255,221,26,0.10)',
        borderColor: '#FFE233',
        borderWidth: 0.5,
        borderRadius: 6.5,
        justifyContent: 'center',
    },
    icon: {
        width: 33,
        height: 33,
    },
    span: {
        fontSize: 12,
        lineHeight: 15,
        color: '#666',
        spanAlign: 'center',
        maxWidth: 80,
        fontWeight: '500',
    },
    pspan: {
        color: '#000',
        fontWeight: '500',
    },
    unActiveBg: {
        borderColor: '#D1D3D8',
        backgroundColor: '#fff',
    },
};

const RateItem = (props: RateItem & Item) => {
    const onItemPress = () => {
        props.onPress({ star: props.value, desc: props.desc });
    };

    const status = props.current === props.value ? Status.POSITIVE : Status.NEGATIVE;

    const image = IconMap[keygen(props.value, status)];
    const { getWidth } = useClientWidth();

    return (
        <Button
            onClick={onItemPress}
            style={getStyle([
                styles.container,
                status !== Status.POSITIVE ? styles.unActiveBg : undefined,
                { width: getWidth(0.375) },
            ])}
        >
            <Image src={image} style={styles.icon} preview={false} />
            <span style={getStyle([styles.span, status === Status.POSITIVE ? styles.pspan : undefined])}>
                {props.desc}
            </span>
        </Button>
    );
};

export default RateItem;
