const openAssistant = (source: 'tt' | 'daxiang', callback = (isMobile: boolean) => {}) => {
    const userAgent = navigator.userAgent;
    // 移动端跳转蜜蜂
    if (/Mobi|Android|iPhone|iPad|iPod|BlackBerry|Windows Phone/i.test(userAgent)) {
        callback(true);
        const data = encodeURIComponent(JSON.stringify({ source: `${source}_app` }));
        window.location.replace(
            `meituanwaimaibee://beewaimai.meituan.com/mrn?mrn_biz=waimaicrm&mrn_entry=bee-assistant-main&mrn_component=bee-assistant&new_bundle=1&data=${data}`,
        );
    }
    // PC端跳转智能助手pc端
    else {
        callback(false);
        let path = import.meta.env.DEV
            ? `/knowledge/chat?source=${source}_web`
            : `/page/bdservice/knowledge/chat?source=${source}_web`;
        const search = new URLSearchParams(window.location.search);
        const parentUrl = search?.get('parentUrl');
        const fullScreen = search?.get('fullScreen');
        const botVersion = search?.get('botVersion');

        if (botVersion) {
            path = path + `&botVersion=${botVersion}`;
        }
        if (fullScreen) {
            path = path + `&fullScreen=${fullScreen}`;
        }
        if (parentUrl) {
            path = path + `&extra=${encodeURIComponent(JSON.stringify({ href: parentUrl }))}`;
        }
        if (window.parent !== window) {
            try {
                path = path + `&extra=${encodeURIComponent(JSON.stringify({ href: window.parent.location.href }))}`;
            } catch (e) {
                console.log(e);
            }
        }
        window.open(path, '_self');
    }
};
export default openAssistant;
