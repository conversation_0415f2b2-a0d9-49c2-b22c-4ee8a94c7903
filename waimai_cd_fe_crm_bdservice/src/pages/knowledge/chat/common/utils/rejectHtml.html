<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Reject Card</title>
        <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
        <style>
            html,
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
                    sans-serif;
                background-color: #ffffff;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            .container {
                padding: 0;
            }
            .card-container {
                margin-bottom: 16px;
            }
            .card-title {
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 8px;
                color: #1f2937;
                font-style: italic;
                font-family: 'MeituanRegular';
            }
            .card-item {
                background-color: #f9fafc;
                border-radius: 12px;
                padding: 16px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
            .card-content {
                font-size: 14px;
                color: #374151;
                margin-bottom: 12px;
                line-height: 1.5;
            }
            .card-content img {
                max-width: 100px;
                border-radius: 8px;
                margin-top: 8px;
            }
            .card-content h1,
            .card-content h2,
            .card-content h3,
            .card-content h4,
            .card-content h5,
            .card-content h6 {
                margin: 12px 0 8px 0;
                font-weight: 600;
            }
            .card-content p {
                margin: 8px 0;
            }
            .card-content ul,
            .card-content ol {
                margin: 8px 0;
                padding-left: 20px;
            }
            .card-content li {
                margin: 4px 0;
            }
            .card-content code {
                background-color: #f1f5f9;
                padding: 2px 4px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
            .card-content pre {
                background-color: #f1f5f9;
                padding: 12px;
                border-radius: 8px;
                overflow-x: auto;
                margin: 8px 0;
            }
            .card-content pre code {
                background-color: transparent;
                padding: 0;
            }
            .card-content strong {
                font-weight: 600;
            }
            .card-content em {
                font-style: italic;
            }
            .card-content a {
                color: #3b82f6;
                text-decoration: none;
            }
            .card-content a:hover {
                text-decoration: underline;
            }
            .card-content blockquote {
                border-left: 4px solid #e5e7eb;
                padding-left: 12px;
                margin: 8px 0;
                color: #6b7280;
            }
            .descriptions-container {
                margin-bottom: 16px;
            }
            .description-item {
                font-size: 12px;
                color: #999;
                margin-top: 6px;
                margin-bottom: 6px;
            }
            .btn-primary {
                background: linear-gradient(96deg, #ffe74d 0%, #ffdd1a 100%);
                color: #333333;
                font-weight: bold;
                border: none;
                border-radius: 50px;
                padding: 10px 16px;
                width: 100%;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s;
            }
            .btn-primary:hover {
                background: linear-gradient(96deg, #ffe74d 0%, #ffdd1a 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(255, 221, 26, 0.3);
            }
            .btn-normal {
                background-color: #ffffff;
                border: 1px solid #e5e5e5;
                color: #333333;
                border-radius: 50px;
                padding: 10px 16px;
                width: 100%;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.2s;
            }
            .btn-normal:hover {
                background-color: #f9fafb;
            }
            .extend-button {
                color: #999999;
                background: none;
                border: none;
                cursor: pointer;
                font-size: 12px;
                text-align: center;
                padding: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: auto;
            }
            .extend-button:hover {
                color: #666666;
            }
            .icon {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-left: 4px;
                vertical-align: middle;
            }
            .text-center {
                text-align: center;
            }
            .space-y-3 > *:not(:first-child) {
                margin-top: 12px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div id="rejectCardContainer" class="space-y-3"></div>
            <div id="extendButtonContainer" class="text-center"></div>
        </div>

        <script>
            // 业务数据
            document.addEventListener('DOMContentLoaded', function () {
                const data = window.data;
                const container = document.getElementById('rejectCardContainer');
                const extendContainer = document.getElementById('extendButtonContainer');

                if (!data || !container) {
                    console.error('Card data or container not found.');
                    return;
                }

                const allCards = data.content;
                const showNum = data.showNum;
                let isExtended = false;

                const renderCards = () => {
                    container.innerHTML = '';
                    const cardsToShow = isExtended || !showNum || showNum >= allCards.length
                        ? allCards
                        : allCards.slice(0, showNum);

                    cardsToShow.forEach(item => {
                        // 创建卡片容器
                        const cardContainer = document.createElement('div');
                        cardContainer.className = 'card-container';

                        // 创建标题元素（放在卡片外面）
                        const titleElement = document.createElement('h2');
                        titleElement.className = 'card-title';
                        titleElement.textContent = item.title;

                        // 创建卡片元素
                        const card = document.createElement('div');
                        card.className = 'card-item';

                        const descriptionsHtml = item.descriptions.map(desc => `
                            <p class="description-item">${desc.label}: ${desc.value}</p>
                        `).join('');

                        // 使用marked.js解析markdown内容
                        const parsedContent = typeof marked !== 'undefined' ? marked.parse(item.content || '') : (item.content || '');

                        // 构建按钮HTML（如果存在button属性）
                        const buttonHtml = item.button ? `
                            <button class="${item.button.type === 'primary' ? 'btn-primary' : 'btn-normal'}">
                                ${item.button.text}
                            </button>
                        ` : '';

                        // 卡片内容不包含标题（内容已预处理）
                        card.innerHTML = `
                            <div class="card-content">${parsedContent}</div>
                            <div class="descriptions-container">${descriptionsHtml}</div>
                            ${buttonHtml}
                        `;

                        // 只有当按钮存在时才添加事件监听器
                        if (item.button) {
                            const buttonEl = card.querySelector('button');
                            if (buttonEl) {
                                buttonEl.addEventListener('click', () => {
                                    if (item.button.action === 'submitQuestion') {
                                        window.Communicator.submitQuestion(item.button.question);
                                    } else if (item.button.url) {
                                        window.Communicator.navigate(item.button.url);
                                    } else {
                                        console.log('Button clicked:', item.button.text);
                                    }
                                });
                            }
                        }

                        // 将标题和卡片添加到容器
                        cardContainer.appendChild(titleElement);
                        cardContainer.appendChild(card);
                        container.appendChild(cardContainer);
                    });

                    // 渲染完成后通知高度变化（使用通信器）
                    if (window.Communicator) {
                        window.Communicator.triggerHeightUpdate();
                    }
                };

                const renderExtendButton = () => {
                    extendContainer.innerHTML = '';
                    if (data.extendButtonName && allCards.length > showNum) {
                        const buttonText = isExtended ? '收起' : `${data.extendButtonName}`;
                        const icon = isExtended
                            ? `<svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" /></svg>`
                            : `<svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>`;

                        const extendButton = document.createElement('button');
                        extendButton.className = 'extend-button';
                        extendButton.innerHTML = `${buttonText} ${icon}`;

                        extendButton.addEventListener('click', () => {
                            isExtended = !isExtended;
                            renderCards();
                            renderExtendButton();
                        });
                        extendContainer.appendChild(extendButton);
                    }

                    // 渲染完成后通知高度变化（使用通信器）
                    if (window.Communicator) {
                        window.Communicator.triggerHeightUpdate();
                    }
                };

                renderCards();
                renderExtendButton();
            });
        </script>
    </body>
</html>
