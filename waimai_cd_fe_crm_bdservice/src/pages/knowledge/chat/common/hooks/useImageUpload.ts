import { message as antdMessage } from 'antd';
import uploadImage from '@src/pages/knowledge/utils/uploadImage';
import useAiStore from '../data/core';

/**
 * 图片上传 hook，封装上传及状态管理逻辑
 */
export const useImageUpload = () => {
    const setFile = useAiStore(v => v.setFile);
    const updateFile = useAiStore(v => v.updateFile);

    /**
     * 处理图片上传
     * @param file 需要上传的图片文件
     * @returns Promise<false> 阻止 antd Upload 自动上传
     */
    const handleUpload = async (file: File) => {
        // 先添加到 fileList，状态为 uploading
        const [key] = setFile([
            {
                type: 'image',
                localSrc: URL.createObjectURL(file),
                status: 'uploading',
            },
        ]);
        try {
            const url = await uploadImage(file, 'img');
            if (url) {
                updateFile(key, {
                    src: url,
                    status: 'success',
                });
            } else {
                updateFile(key, {
                    type: 'image',
                    localSrc: URL.createObjectURL(file),
                    status: 'error',
                });
                antdMessage.error('图片上传失败');
            }
        } catch (e) {
            updateFile(key, {
                type: 'image',
                localSrc: URL.createObjectURL(file),
                status: 'error',
            });
            antdMessage.error('图片上传失败');
        }
        // 阻止 antd Upload 自动上传
        return false;
    };

    return {
        handleUpload,
    };
};
