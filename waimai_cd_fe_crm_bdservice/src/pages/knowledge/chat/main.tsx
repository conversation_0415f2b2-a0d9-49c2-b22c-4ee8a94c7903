import { render } from '@src/module/root';
import Config from '@src/pages/knowledge/components/config';
import useAiStore from './common/data/core';
import MessageUInew from './common/ui/message/indexNew';
import AiInputNew from './common/ui/inputter/indexNew';
import useOpenSession from './common/service/openSession';
import { List, Spin, Image, Typography, Space, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { MessageFrom } from './common/type/message';
import Condition from '@src/components/Condition/Condition';
import BgImgNew from '@src/assets/images/chat/bgnew.png';
import RefreshImg from '@src/assets/images/chat/refresh.png';
import HistoryImg from 'src/assets/images/chat/history.png';
import ExpandFullScreenIcon from '@src/assets/images/chat/expand.png';
import CollapseFullScreenIcon from '@src/assets/images/chat/collapse.png';
import './common/styles/index.scss';
import { CloseOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import useOpenFeedback from './common/utils/openFeedbackModal';
import useInstruction from '@src/pages/knowledge/chat/common/service/instruction';
import useClientWidth from './common/utils/screenWidth';
import useGrayInfo from '@src/pages/knowledge/chat/common/service/grayInfo';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import useMockMode from './common/service/mockMode';
import {
    AbilityType,
    EntryPoint,
    EntryPointType,
    QuestionParams,
} from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import { useLatest } from 'ahooks';
import './main.scss';
import WelcomeMessage from './common/ui/message/welcome';
import useRefreshSession from './common/service/refreshSession';
import { closeFullscreenIframe } from './common/utils/fullscreenIframe';
import useGetChatHistory from './common/service/getChatHistory';
import { useDragAndDrop } from './common/hooks/useDragAndDrop';
import _ from 'lodash';
import { isSupportClose, isSupportWideScreen } from './common/utils/ability';
import useSendMessage from './common/service/sendMessage/sendMessage';
import TaskList from '@src/components/TaskList';

const Chat = () => {
    const { isDragging, dragProps } = useDragAndDrop();
    const isFullScreen = useAiStore(v => v.config.ui.fullScreen);
    const isCopilotIframeShow = useAiStore(v => v.config.ui.isCopilotIframeShow);
    const showBackButton = useAiStore(v => v.config.ui.showBackButton);
    const setUiConfig = useAiStore(v => v.setUiConfig);
    const { data: bizInfo } = useBizInfo();
    const getChatHistory = useGetChatHistory();
    const { isMockMode } = useMockMode();
    const sendMessage = useSendMessage();
    const openFeedbackModal = useOpenFeedback({
        onCancel: () => {
            closeFullscreenIframe();
            setUiConfig({ isCopilotIframeShow: false });
            window.parent.postMessage({ type: 'ASSISTANT_DRAWER', data: { openType: 'collapse' } }, '*');
            window.parent.postMessage({ type: 'ASSISTANT_CLOSE' }, '*');
        },
    });

    const refreshSession = useRefreshSession();
    const needPlayTyping = useAiStore(v => v.typing.needPlay);
    const setScrollToEnd = useAiStore(v => v.setScrollToEnd);
    const showHome = useAiStore(v => v.config.ui.showHome);
    const setShowHome = useAiStore(v => v.setShowHome);
    const containerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        setScrollToEnd(() => {
            const ctn = containerRef.current;
            if (!ctn) return;
            ctn.scrollTop = ctn.scrollHeight - ctn.clientHeight;
        });
    }, []);
    const setDragged = useAiStore(v => v.setDragged);

    // 获取初始状态
    const [messageArray, setMessageArray] = useState(useAiStore.getState().messageArray);
    // 在挂载时连接到Store，在卸载时断开连接，在引用时捕获状态变化
    useEffect(
        () =>
            useAiStore.subscribe(state => {
                setMessageArray(state.messageArray);
            }),
        [],
    );

    const finishTypingAnimation = useAiStore(v => v.finishTypingAnimation);
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                finishTypingAnimation();
            }
        };
        window.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            window.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [finishTypingAnimation]);

    // 屏蔽是否需要打字动画的差异fe
    const finalMessageArray = (() => {
        if (!needPlayTyping) {
            return messageArray;
        }

        const finalMessageArray = messageArray.map(v => {
            if (v.from === MessageFrom.left) {
                return { ...v, data: v.typingData };
            }
            return v;
        });
        return finalMessageArray;
    })();

    const setTypingConfig = useAiStore(v => v.setTypingConfig);
    const setApiConfig = useAiStore(v => v.setApiConfig);
    const setStoreEle = useAiStore(v => v.setStoreEle);
    const callerRequest = useCallerRequest();
    const bizInfoLatest = useLatest(bizInfo);
    useEffect(() => {
        setTypingConfig({ needPlay: true });
        setStoreEle({ toolbar: { show: true }, association: { show: true } });
        setApiConfig({
            sendMessage: (params: QuestionParams) => {
                if (showHome) {
                    setShowHome(false);
                }
                return callerRequest.post('/bee/v1/bdaiassistant/submitQuery', {
                    abilityType: AbilityType.GENERAL,
                    bizId: bizInfoLatest.current?.bizId,
                    entryPointType: params.entryPoint ? undefined : EntryPointType.USER, // 如果存在entryPoint，则不设置entryPointType
                    entryPoint: EntryPoint.input,
                    version: 'V3',
                    ...params,
                });
            },
            fetchAnswer: (params: APISpec['/bee/v1/bdaiassistant/fetchAnswer']['request'], silent = true) => {
                return callerRequest.post('/bee/v1/bdaiassistant/fetchAnswer', params, { silent });
            },
        });
    }, []);
    const grayInfo = useGrayInfo();
    const { getWidth } = useClientWidth();
    const getLatestSessionId = useAiStore(v => v.getLatestSessionId);

    const renderContent = () => {
        return (
            <div
                style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    paddingBottom: 20,
                    backgroundImage: `url(${BgImgNew})`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'cover',
                    boxShadow: '0px 10px 22px 1px rgba(100, 100, 100, 0.23)',
                }}
            >
                <Space
                    style={{
                        display: 'flex',
                        padding: '20px 24px',
                        position: 'relative',
                        alignItems: 'center',
                    }}
                >
                    <Condition condition={[isSupportWideScreen()]}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8, lineHeight: '20px' }}>
                            <Tooltip title={isFullScreen ? '收起' : '展开'} zIndex={999999}>
                                <Image
                                    onClick={() => {
                                        window.parent.postMessage(
                                            {
                                                type: 'ASSISTANT_DRAWER',
                                                data: { openType: isFullScreen ? 'collapse' : 'expand' },
                                            },
                                            '*',
                                        );
                                        if (isFullScreen) {
                                            setUiConfig({ isCopilotIframeShow: false });
                                            closeFullscreenIframe();
                                        }
                                        setUiConfig({ fullScreen: !isFullScreen });
                                    }}
                                    src={isFullScreen ? CollapseFullScreenIcon : ExpandFullScreenIcon}
                                    width={24}
                                    preview={false}
                                    style={{
                                        cursor: 'pointer',
                                        verticalAlign: 'top',
                                    }}
                                />
                            </Tooltip>
                            <Condition condition={[isFullScreen && showBackButton]}>
                                {/*  返回按钮，仅关闭 iframe，不关闭全屏 */}
                                <Tooltip title="返回" zIndex={999999}>
                                    <ArrowLeftOutlined
                                        onClick={() => {
                                            setUiConfig({ showBackButton: false, isCopilotIframeShow: false });
                                            closeFullscreenIframe();
                                        }}
                                        style={{ fontSize: 20 }}
                                    />
                                </Tooltip>
                            </Condition>
                        </div>
                    </Condition>

                    {/* 仅首页 & "智能分析页"未打开 时才展示查看历史对话 */}
                    <Condition condition={[showHome && !isCopilotIframeShow]}>
                        <Tooltip title={'查看历史对话'}>
                            <Image
                                src={HistoryImg}
                                width={24}
                                style={{
                                    cursor: 'pointer',
                                }}
                                preview={false}
                                onClick={() => {
                                    setShowHome(false);
                                    getChatHistory();
                                }}
                            />
                        </Tooltip>
                    </Condition>

                    <Condition condition={[!showHome && !isCopilotIframeShow]}>
                        <Tooltip title={'新对话'}>
                            <Image
                                style={{ cursor: 'pointer' }}
                                onClick={() => refreshSession()}
                                src={RefreshImg}
                                width={24}
                                preview={false}
                            />
                        </Tooltip>
                    </Condition>

                    {/* 任务列表组件 */}
                    <TaskList
                        onSendMessage={content => {
                            sendMessage(content, {
                                entryPoint: EntryPoint.input,
                                entryPointType: EntryPointType.USER,
                            });
                        }}
                    />

                    {/*只在iframe嵌入时展示*/}
                    <Condition condition={[isSupportClose()]}>
                        <CloseOutlined
                            className={'pointer'}
                            style={{ position: 'absolute', right: 20, top: 20, width: 24, height: 24 }}
                            onClick={() => {
                                apiCaller.send('/bee/v1/bdaiassistant/closeSession', {
                                    // @ts-ignore
                                    sessionId: getLatestSessionId(),
                                });
                                openFeedbackModal?.();
                            }}
                        />
                    </Condition>
                </Space>
                <div
                    style={{ flex: 1, overflow: 'scroll', scrollbarWidth: 'none', paddingBottom: 20 }}
                    ref={containerRef}
                    onWheel={() => {
                        setDragged(true);
                    }}
                >
                    <Condition condition={[showHome, !showHome]}>
                        <WelcomeMessage />
                        <Condition condition={[finalMessageArray?.length > 0]}>
                            <div
                                style={{
                                    maxWidth: isFullScreen ? getWidth() - 40 : getWidth(),
                                    margin: '0 auto',
                                    padding: isFullScreen ? 0 : '0 20px',
                                }}
                            >
                                <List
                                    dataSource={finalMessageArray}
                                    renderItem={v => {
                                        return <MessageUInew data={v} key={v.id} />;
                                    }}
                                />
                            </div>
                        </Condition>
                    </Condition>
                </div>

                <AiInputNew style={{ maxWidth: 800 }} />
            </div>
        );
    };

    return (
        <Spin spinning={!bizInfo || !grayInfo?.gray}>
            <div
                style={{
                    background: '#F5F6FA',
                    height: '100vh',
                    width: '100vw',
                    display: 'flex',
                    justifyContent: 'center',
                    position: 'relative',
                }}
                {..._.omit(dragProps, 'onDragLeave')}
                onDrop={dragProps.onDrop}
            >
                {/* Mock模式提示 */}
                {isMockMode && (
                    <div
                        style={{
                            position: 'fixed',
                            top: 10,
                            right: 10,
                            background: '#ff6000',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: 4,
                            fontSize: 12,
                            zIndex: 1000,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                        }}
                    >
                        Mock模式
                    </div>
                )}
                {isDragging && (
                    <div
                        onDragLeave={dragProps.onDragLeave}
                        style={{
                            position: 'absolute',
                            border: '1px dashed blue',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: '#fff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 1000,
                        }}
                    >
                        <Typography.Title level={4} style={{ marginBottom: '10px' }}>
                            松开立即上传图片
                        </Typography.Title>
                    </div>
                )}
                {renderContent()}
            </div>
        </Spin>
    );
};

const ChatWrapper = () => {
    const instruction = useInstruction();
    useOpenSession({ instruction });
    const sessionId = useAiStore(v => v.sessionId);
    if (!sessionId.length) {
        return null;
    }
    return <Chat />;
};

render(
    <Config>
        <ChatWrapper />
    </Config>,
    '智能助手',
);
