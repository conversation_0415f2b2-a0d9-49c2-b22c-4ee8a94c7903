.ant-btn {
    outline: none !important;
}

.chat-button {
    border-radius: 16px !important;
}

@font-face {
    font-family: 'MeituanBold';
    src: url('../../../assets/fonts/Meituan Type-Bold.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}

@font-face {
    font-family: 'MeituanRegular';
    src: url('../../../assets/fonts/Meituan Type-Regular.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}

@font-face {
    font-family: 'MeituanLight';
    src: url('../../../assets/fonts/Meituan Type-Light.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}
