import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Row, Col, Typography, Button, message, Collapse } from 'antd';
import { CopyOutlined, ReloadOutlined, RightOutlined } from '@ant-design/icons';
import {
    Message,
    MessageData,
    MessageFrom,
    MessageStatus,
    MarkdownMessage,
    ThinkContentMessage,
} from '../chat/common/type/message';
import { generateMessageExamples } from './MessagePreviewData';
import MessageUI from '../chat/common/ui/message/indexNew';
import MarkdownRender from '../chat/common/ui/messageContent/MarkdownRender';
import ThinkContent from '../chat/common/ui/messageContent/ThinkContent';
import MonacoEditor from 'react-monaco-editor';
import './MessagePreview.scss';

const { Title, Text } = Typography;
const { Panel } = Collapse;

// 懒加载的Monaco编辑器组件
const LazyMonacoEditor = React.memo(
    ({ value, onChange, height = 360 }: { value: string; onChange: (value: string) => void; height?: number }) => {
        return (
            <div className="editor-container" style={{ height }}>
                <MonacoEditor
                    height="100%"
                    language="json"
                    value={value}
                    onChange={onChange}
                    options={{
                        selectOnLineNumbers: true,
                        automaticLayout: true,
                        minimap: { enabled: false },
                        scrollBeyondLastLine: false,
                        fontSize: 13,
                        tabSize: 2,
                        wordWrap: 'on',
                        lineNumbers: 'on',
                        folding: true,
                        formatOnPaste: true,
                        formatOnType: true,
                        fontFamily: 'SF Mono, Monaco, Menlo, Consolas, monospace',
                        renderLineHighlight: 'none',
                        scrollbar: {
                            vertical: 'visible',
                            horizontal: 'visible',
                            verticalScrollbarSize: 8,
                            horizontalScrollbarSize: 8,
                        },
                    }}
                    theme="vs-light"
                />
            </div>
        );
    },
);

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface MessagePreviewProps {}

export const MessagePreview: React.FC<MessagePreviewProps> = () => {
    const [messageExamples] = useState(() => generateMessageExamples());
    const [editedMessages, setEditedMessages] = useState<Record<string, Message>>({});
    const [jsonConfigs, setJsonConfigs] = useState<Record<string, string>>({});
    const [expandedPanels, setExpandedPanels] = useState<string[]>([]);

    // 获取消息类型列表，使用useMemo缓存
    const messageTypes = useMemo(() => Object.keys(messageExamples), [messageExamples]);

    // 初始化JSON配置
    useEffect(() => {
        const initialConfigs: Record<string, string> = {};
        messageTypes.forEach(type => {
            initialConfigs[type] = JSON.stringify(messageExamples[type], null, 2);
        });
        setJsonConfigs(initialConfigs);
        setEditedMessages({ ...messageExamples });
    }, [messageExamples, messageTypes]);

    // 创建消息数据用于渲染
    const createMessageData = (msg: Message, index: number): MessageData => ({
        data: [msg],
        id: 'preview-' + index,
        localStatus: MessageStatus.done,
        from: MessageFrom.left,
    });

    // 处理JSON编辑，使用useCallback缓存
    const handleJsonChange = useCallback((type: string, value: string) => {
        setJsonConfigs(prev => ({ ...prev, [type]: value }));

        try {
            const parsed = JSON.parse(value);
            setEditedMessages(prev => ({ ...prev, [type]: parsed }));
        } catch (error) {
            // JSON解析失败时不更新消息
            console.warn('JSON解析失败:', error);
        }
    }, []);

    // 复制JSON配置，使用useCallback缓存
    const handleCopyJson = useCallback(
        (type: string) => {
            navigator.clipboard.writeText(jsonConfigs[type] || '');
            message.success(`${type} JSON配置已复制到剪贴板`);
        },
        [jsonConfigs],
    );

    // 重置为默认配置，使用useCallback缓存
    const handleReset = useCallback(
        (type: string) => {
            const defaultMessage = messageExamples[type];
            if (defaultMessage) {
                const defaultJson = JSON.stringify(defaultMessage, null, 2);
                setJsonConfigs(prev => ({ ...prev, [type]: defaultJson }));
                setEditedMessages(prev => ({ ...prev, [type]: defaultMessage }));
            }
        },
        [messageExamples],
    );

    // 获取当前消息（编辑后的或默认的），使用useMemo缓存
    const getCurrentMessage = useCallback(
        (type: string): Message => {
            return editedMessages[type] || messageExamples[type];
        },
        [editedMessages, messageExamples],
    );

    // 处理折叠面板变化，添加动画延迟
    const handlePanelChange = useCallback((keys: string | string[]) => {
        const newKeys = Array.isArray(keys) ? keys : [keys];
        setExpandedPanels(newKeys);
    }, []);

    // 渲染消息内容的组件，使用React.memo优化
    const MessageContent = React.memo(({ type, message }: { type: string; message: Message }) => {
        const messageData = createMessageData(message, 0);

        // 特殊处理 markdown 和 thinkContent
        if (type === 'markdown') {
            const markdownMsg = message as MarkdownMessage;
            return (
                <div className="message-bubble">
                    <MarkdownRender data={markdownMsg.insert.markdown.text} />
                </div>
            );
        }

        if (type === 'thinkContent') {
            const thinkMsg = message as ThinkContentMessage;
            return (
                <div className="message-bubble">
                    <ThinkContent
                        status={thinkMsg.insert.thinkContent.status === 'thinking' ? 'thinking' : 'done'}
                        children={[thinkMsg]}
                    />
                </div>
            );
        }

        // 普通消息使用 MessageUI 组件
        return <MessageUI data={messageData} noFooter={true} style={{ alignItems: 'flex-start' }} />;
    });

    // 生成折叠面板数据，使用useMemo缓存
    const collapseItems = useMemo(() => {
        return messageTypes.map(type => {
            const message = getCurrentMessage(type);
            const jsonConfig = jsonConfigs[type] || '';

            return {
                key: type,
                label: (
                    <div className="panel-header">
                        <span className="message-type-label">{type}</span>
                        <div className="panel-actions" onClick={e => e.stopPropagation()}>
                            <Button
                                className="action-button"
                                icon={<CopyOutlined />}
                                onClick={() => handleCopyJson(type)}
                                size="small"
                                type="text"
                            >
                                复制
                            </Button>
                            <Button
                                className="action-button"
                                icon={<ReloadOutlined />}
                                onClick={() => handleReset(type)}
                                size="small"
                                type="text"
                            >
                                重置
                            </Button>
                        </div>
                    </div>
                ),
                children: (
                    <Row gutter={24} className="panel-content">
                        {/* 左侧：消息预览 */}
                        <Col span={11}>
                            <div className="section-header">
                                <span className="section-title">消息预览</span>
                            </div>
                            <div className="preview-container">
                                <MessageContent type={type} message={message} />
                            </div>
                        </Col>

                        {/* 右侧：JSON编辑器 - 只在展开时渲染 */}
                        <Col span={11}>
                            <div className="section-header">
                                <span className="section-title">JSON 配置</span>
                            </div>
                            {expandedPanels.includes(type) ? (
                                <LazyMonacoEditor
                                    value={jsonConfig}
                                    onChange={value => handleJsonChange(type, value)}
                                    height={360}
                                />
                            ) : (
                                <div className="editor-placeholder">
                                    <span className="placeholder-text">展开面板以查看编辑器</span>
                                </div>
                            )}
                        </Col>
                    </Row>
                ),
            };
        });
    }, [messageTypes, getCurrentMessage, jsonConfigs, expandedPanels, handleCopyJson, handleReset, handleJsonChange]);

    return (
        <div className="message-preview-container">
            <div className="page-header">
                <h1 className="page-title">消息预览</h1>
                <p className="page-description">实时预览和编辑所有消息类型，支持 JSON 配置</p>
            </div>

            <div className="content-wrapper">
                <Collapse
                    className="message-collapse"
                    items={collapseItems}
                    activeKey={expandedPanels}
                    onChange={handlePanelChange}
                    expandIcon={({ isActive }) => (
                        <span className={`expand-icon ${isActive ? 'expanded' : ''}`}>
                            <RightOutlined />
                        </span>
                    )}
                    ghost
                />
            </div>
        </div>
    );
};

export default MessagePreview;
