import {
    Form,
    Table,
    Row,
    Col,
    Input,
    DatePicker,
    Button,
    Space,
    Checkbox,
    Tag,
    Typography,
    App as AntdApp,
    DatePickerProps,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { ColumnsType } from 'antd/es/table';
import { useAntdTable } from 'ahooks';
import { useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import openUrl from '@src/pages/knowledge/open';
import useUrlState from '@src/hooks/useUrlState';
import { RightOutlined } from '@ant-design/icons';
import { HookAPI } from 'antd/es/modal/useModal';
import '../standard/style.scss';
import DomainFormItem from '../components/DomainFormItem';
import StandardQuestionItem from '../components/StandardQuestionItem';
import { Data } from '@src/pages/knowledge/types';

const { Item } = Form;

const getColumns = (modal: HookAPI): ColumnsType<Data> => {
    return [
        {
            dataIndex: 'id',
            title: '会话ID',
        },
        {
            dataIndex: 'mis',
            title: '发问人',
            render: (v, r: any) => (
                <div style={{ maxWidth: 200 }}>
                    <div>{r.name}</div>
                    <div>{v}</div>
                </div>
            ),
        },
        {
            dataIndex: 'orgInfos',
            title: '组织架构',
            width: '20%',
            render: v => {
                return (
                    <div style={{ position: 'relative' }}>
                        <Typography.Text style={{ maxWidth: 200 }} ellipsis={true}>
                            {v?.join(' ') || '-'}
                        </Typography.Text>
                        {v ? (
                            <span
                                className={'standard-detail-btn'}
                                onClick={() => {
                                    modal.confirm({
                                        title: '组织架构',
                                        icon: null,
                                        content: (
                                            <>
                                                {v.map(w => (
                                                    <div key={w}>{w}</div>
                                                ))}
                                            </>
                                        ),
                                    });
                                }}
                            >
                                详情
                                <RightOutlined />
                            </span>
                        ) : null}
                    </div>
                );
            },
        },
        {
            title: '涉及域',
            dataIndex: 'domainList',
            render: v => v.join(','),
        },
        {
            dataIndex: 'phraseNameList',
            title: '涉及的标准问',
            width: '20%',
            render: v => {
                const baseComp = v?.map((w, i) => (
                    <Typography.Text style={{ maxWidth: 250 }} ellipsis={{ tooltip: `${i + 1}. ${w}` }} key={w?.id}>
                        {`${i + 1}. ${w}`}
                    </Typography.Text>
                ));
                return <Space direction={'vertical'}>{baseComp}</Space>;
            },
        },
        {
            dataIndex: 'createTime',
            title: '会话时间',
        },
        {
            dataIndex: 'submittedTt',
            title: '是否转了TT',
            render: v => (!v ? '-' : <Tag color={'red'}>已转TT工单</Tag>),
        },
        {
            dataIndex: 'marked',
            title: '备注情况',
            render: v => (!v ? '-' : <Tag color={'blue'}>已备注</Tag>),
        },
        {
            dataIndex: 'id',
            title: '操作',
            align: 'center',
            render: (v, r) => (
                <Button type={'link'} onClick={() => openUrl(`/knowledge/sessionDetail?sessionId=${r.id}`)}>
                    查看
                </Button>
            ),
        },
    ];
};
const App = () => {
    const [updateFlag, setUpdateFlag] = useState(0);
    const forceUpdate = () => setUpdateFlag(updateFlag + 1);
    const { modal } = AntdApp.useApp();

    const [form] = Form.useForm();
    const { tableProps } = useAntdTable(
        async ({ current, pageSize }) => {
            return new Promise<any>((resolve, reject) => {
                form.validateFields({})
                    .then(async values => {
                        let draftParams = { ...values };
                        // 如果有id，则忽略其他参数
                        if (draftParams.sessionId) {
                            draftParams = { sessionId: draftParams.sessionId };
                        }
                        if (draftParams.date as Dayjs[]) {
                            draftParams.startDate = draftParams.date[0].format('YYYY-MM-DD');
                            draftParams.endDate = draftParams.date[1].format('YYYY-MM-DD');
                            delete draftParams.date;
                        }
                        const res = await apiCaller.post('/manage/session/list', {
                            ...draftParams,
                            pageNum: current,
                            pageSize,
                        });
                        if (res.code === 0) {
                            resolve({ list: res.data.sessionList, total: res.data.total });
                        }
                        resolve({ list: [], total: 0 });
                    })
                    .catch(e => {
                        resolve({ list: [], total: 0 });
                    });
            });
        },
        {
            refreshDeps: [updateFlag],
        },
    );

    const { setUrlState } = useUrlState(urlState => {
        if (urlState.startDate && urlState.endDate) {
            urlState.date = [dayjs(urlState.startDate), dayjs(urlState.endDate)];
            delete urlState.startDate;
            delete urlState.endDate;
        }
        form.setFieldsValue(urlState);
    });

    const disabled3MonthsDate: DatePickerProps['disabledDate'] = (current, { from, type }) => {
        if (from) {
            const minDate = from.add(-3, 'months');
            const maxDate = from.add(3, 'months');

            switch (type) {
                case 'year':
                    return current.year() < minDate.year() || current.year() > maxDate.year();

                default:
                    return current.isBefore(minDate) || current.isAfter(maxDate);
            }
        }

        return false;
    };

    return (
        <>
            <Form
                form={form}
                onFinish={forceUpdate}
                onFieldsChange={() => {
                    const values = form.getFieldsValue(true);
                    const draftState = { ...values };
                    if (draftState.date as Dayjs[]) {
                        draftState.startDate = draftState.date[0].format('YYYY-MM-DD');
                        draftState.endDate = draftState.date[1].format('YYYY-MM-DD');
                        delete draftState.date;
                    }
                    delete draftState.phraseData;
                    setUrlState(draftState);
                }}
                // 输入sessionId后，不需要其他参数
                disabled={form.getFieldValue('sessionId')}
            >
                <Row gutter={20}>
                    <Col span={6}>
                        <Item
                            label={'会话ID'}
                            name={'sessionId'}
                            rules={[
                                {
                                    validator: async (rule, value) => {
                                        if (!!value && !/^\d+$/.test(value)) {
                                            return Promise.reject('请输入数字ID');
                                        }
                                    },
                                    message: '请输入数字ID',
                                },
                            ]}
                        >
                            {/*  输入sessionId后，不需要其他参数 */}
                            <Input placeholder={'请输入会话ID'} allowClear disabled={false} />
                        </Item>
                    </Col>
                    <Col span={6}>
                        <Item label={'BDmisID'} name={'mis'}>
                            <Input placeholder={'请输入misID'} allowClear />
                        </Item>
                    </Col>
                    <Col span={6}>
                        <StandardQuestionItem />
                    </Col>
                    <Col span={6}>
                        <Item
                            initialValue={[dayjs().add(-1, 'day'), dayjs().add(-1, 'day')]}
                            label={'开始时间'}
                            name={'date'}
                        >
                            <DatePicker.RangePicker
                                presets={[
                                    {
                                        label: '昨天',
                                        value: [dayjs().add(-1, 'days'), dayjs().add(-1, 'days')],
                                    },
                                    {
                                        label: '一周内',
                                        value: [dayjs().add(-8, 'days'), dayjs().add(-1, 'days')],
                                    },
                                    {
                                        label: '一月内',
                                        value: [dayjs().add(-1, 'months'), dayjs().add(-1, 'days')],
                                    },
                                    {
                                        label: '三月内',
                                        value: [dayjs().add(-3, 'months'), dayjs().add(-1, 'days')],
                                    },
                                ]}
                                style={{ width: '100%' }}
                                allowClear={false}
                                disabledDate={disabled3MonthsDate}
                            />
                        </Item>
                    </Col>
                    <Col span={6}>
                        <DomainFormItem />
                    </Col>
                    <Col span={6}>
                        状态筛选：
                        <Space>
                            <Item colon={false} name={'submittedTt'} valuePropName={'checked'}>
                                <Checkbox />
                            </Item>
                            <Item>
                                <span style={{ color: '#3D3D3D' }}>已转TT工单</span>
                            </Item>
                            <Item name={'marked'} colon={false} valuePropName={'checked'}>
                                <Checkbox />
                            </Item>
                            <Item>
                                <span style={{ color: '#3D3D3D' }}>已备注</span>
                            </Item>
                        </Space>
                    </Col>
                    <Col span={6}></Col>
                    <Col span={6} style={{ justifyContent: 'end', display: 'flex' }}>
                        <Space>
                            <Button
                                disabled={false}
                                onClick={() => {
                                    form.resetFields();
                                    forceUpdate();
                                }}
                            >
                                重置
                            </Button>
                            <Button type={'primary'} htmlType={'submit'} disabled={false}>
                                确定
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
            <Table bordered rowKey={'id'} columns={getColumns(modal)} {...tableProps} />
        </>
    );
};
export default App;
