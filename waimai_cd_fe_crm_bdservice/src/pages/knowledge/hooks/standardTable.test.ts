import { describe, expect, test, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { Form } from 'antd';

const data = {
    pageNum: 1,
    pageSize: 20,
    total: 66,
    list: [
        {
            id: 12245,
            domainId: null,
            question: 'Spring批量导入测试Q-3',
            type: 'faq',
            utime: '2024-09-29 11:01:18',
            modifierName: 'SpringTest',
            modifierMis: 'SpringTest',
            answer: '[{"type":"text","insert":"Spring批量导入测试A-3"}]',
            triggerId: 5757,
            phraseSize: null,
        },
        {
            id: 12244,
            domainId: 1001,
            question: 'Spring批量导入测试-被编辑的问题Q1',
            type: 'faq',
            utime: '2024-09-29 11:01:18',
            modifierName: 'SpringTest',
            modifierMis: 'SpringTest',
            answer: 'Spring批量导入测试-被编辑的问题A1',
            triggerId: 5756,
            phraseSize: null,
        },
        {
            id: 11940,
            domainId: null,
            question: '周五测试v213',
            type: 'faq',
            utime: '2024-09-20 09:38:44',
            modifierName: '冯鑫',
            modifierMis: 'fengxin21',
            answer: '[{\n\t"type": "text",\n\t"insert": "1、首次上线后30天内累计在线订单数（不含到店取餐订单）需满足：低单量品类的商家≥5单；其他品类的商家≥10单；2、本月月累计营业时长≥42小时；3、本月月累计单均价（实付）≥15元；4、本月最后一天在线；5、匹配：潜在门店须在WDC匹配成功；6、首次上线后30天内数据：上线当天统计在内；\\n["\n}, {\n\t"type": "link",\n\t"attributes": {\n\t\t"link": "https://km.sankuai.com/collabpage/2200263181|https://km.sankuai.com/collabpage/2200263181"\n\t},\n\t"insert": "https://km.sankuai.com/collabpage/2200263181|https://km.sankuai.com/collabpage/2200263181"\n}, {\n\t"type": "text",\n\t"insert": "]\\n<分析结论模板>1.商家ID：<数字>（商家名称：<内容>）\\n\\n["\n}, {\n\t"type": "link",\n\t"attributes": {\n\t\t"link": "https://km.sankuai.com/collabpage/2200263181|https://km.sankuai.com/collabpage/2200263181"\n\t},\n\t"insert": "https://km.sankuai.com/collabpage/2200263181|https://km.sankuai.com/collabpage/2200263181"\n}, {\n\t"type": "text",\n\t"insert": "]<分析结论模板>1.商家ID：<数字>（商家名称：<内容>）。"\n}]',
            triggerId: 5521,
            phraseSize: null,
        },
    ],
};

describe('useStandardTable', () => {
    test('should fetch standard table', async () => {
        const mockResponse = {
            code: 0,
            data,
        };

        vi.doMock('@mfe/cc-api-caller-pc', async () => {
            return {
                apiCaller: {
                    get: () => Promise.resolve(mockResponse),
                },
            };
        });

        // 动态import是为了使apiCaller的mock生效
        const { useStandardTable } = await import('./standardTable');

        // Render the hook
        const { result, rerender } = renderHook(() => useStandardTable(Form.useForm()[0]));

        expect(result.current.data).toEqual(undefined);

        // 等待某个条件成立，比如状态更新
        await waitFor(
            () => {
                expect(result.current.data).not.toBeUndefined();
            },
            { timeout: 2000 },
        );

        expect(result.current.data?.list).toHaveLength(mockResponse.data.list.length);
    });
});
