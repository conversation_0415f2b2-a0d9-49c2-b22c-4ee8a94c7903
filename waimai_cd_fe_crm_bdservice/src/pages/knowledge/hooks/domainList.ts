import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';

export const useDomainList = () =>
    useRequest(
        async () => {
            const res = await apiCaller.get('/manage/domain/list', {});
            if (res.code === 0) {
                return res.data.domainList.map(({ id, ...rest }) => ({ id: String(id), ...rest }));
            }
            return [];
        },
        { cacheKey: 'domainList' },
    );

export const getDomainDesc = (id, domainList) => domainList.find(v => String(v.id) === String(id))?.domain;
