import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import { useAntdTable } from 'ahooks';

export const getStandardTableData = async (
    { current, pageSize },
    formData: APISpec['/manage/phrase/standard/list']['request'],
) => {
    const res = await apiCaller.get('/manage/phrase/standard/list', {
        pageNum: current,
        pageSize,
        ...formData,
    } as any);
    if (res.code !== 0) {
        return { list: [], total: 0 };
    }

    return { list: res.data.list, total: res.data.total };
};
export const useStandardTable = form => {
    return useAntdTable(getStandardTableData, {
        form,
    });
};
