import { But<PERSON>, Row, Space, Table, Typography, App as AntdApp } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useEffect, useState } from 'react';
import { useAntdTable, useRequest } from 'ahooks';
import useEditFaq from '@src/pages/knowledge/components/editor/editModal/editFaq';
import { getColumns } from '@src/pages/knowledge/batchImport/getColumns';
import './style.scss';
import useUrlState from '@src/hooks/useUrlState';
import openUrl from '@src/pages/knowledge/open';
import { useDomainList } from '../hooks/domainList';
import Uploader from './Uploader';

const App = () => {
    const { message, modal } = AntdApp.useApp();
    const { tableProps, run, pagination, data, mutate } = useAntdTable(
        async ({ current, pageSize }, id?: number) => {
            if (!id) {
                return {
                    total: 0,
                    list: [],
                };
            }
            // res.data.status: 状态；init：初始化；imported：导入完成；recorded：执行完成; init表示未完成数据处理，需要定时拉取数据
            const res = await apiCaller.post('/manage/phrase/standard/batch/list', {
                id: Number(id),
                pageNum: current,
                pageSize: pageSize,
            });
            if (res.code === 0) {
                if (res.data.status === 'init') {
                    setTimeout(refresh, 1000);
                }
                return {
                    status: res.data.status,
                    total: res.data.total || 0,
                    list: res.data.list || [],
                };
            }
            return {
                total: 0,
                list: [],
            };
        },
        { manual: false },
    );
    const [processId, setProcessId] = useState(undefined);
    useEffect(() => {
        if (!processId) return;
        // 更新processId到url抗刷新
        setUrlState({ processId });
        // 重置数据
        run({ current: 1, pageSize: 10 }, processId);
    }, [processId]);
    const { setUrlState } = useUrlState(urlState => {
        setProcessId(urlState.processId);
    });

    const refresh = () => run(pagination, processId); // 重新请求当前页数据，用于操作后刷新列表
    const editFaq = useEditFaq(refresh);

    const { data: fileUrl } = useRequest(async () => {
        const res = await apiCaller.get('/manage/phrase/standard/batch/downloadTemplate', {});
        if (res.code === 0) {
            return res.data.fileUrl;
        }
        return undefined;
    });
    const { data: domainList } = useDomainList();
    const mutateRow = (row: any) => {
        mutate({
            ...(data as any),
            list: (data?.list.map(d => {
                if (d.id === row.id) {
                    return row;
                }
                return d;
            }) || []) as any[],
        });
    };
    return (
        <Space direction={'vertical'} size={'large'} style={{ width: '100%', height: '100%' }}>
            <Space direction={'vertical'} style={{ width: '100%' }}>
                <Typography.Title level={4}>批量新增</Typography.Title>
                <Row justify={'space-between'}>
                    <Typography.Text>上传并检核你即将上传的知识，确保答案可面向BD和它的准确性</Typography.Text>
                    <Space>
                        <Button
                            icon={<DownloadOutlined />}
                            onClick={() => {
                                if (!fileUrl) {
                                    message.error('模板不存在');
                                    return;
                                }
                                window.open(fileUrl, '_blank');
                            }}
                        >
                            下载模板
                        </Button>
                        <Button
                            type={'primary'}
                            onClick={async () => {
                                if (!processId) {
                                    message.error('没有待录入的数据');
                                    return;
                                }
                                const res = await apiCaller.post('/manage/phrase/standard/batch/executeImport', {
                                    processId,
                                });
                                if (res.code === 0) {
                                    modal.confirm({
                                        title: '录入成功',
                                        content: res.data.message,
                                        onOk: () => {
                                            openUrl('/knowledge/standard', '_self');
                                        },
                                        okText: '我知道了',
                                        okCancel: false,
                                        icon: null,
                                    });
                                }
                            }}
                        >
                            确定提交
                        </Button>
                    </Space>
                </Row>
                <Uploader processId={processId} setProcessId={setProcessId} />
            </Space>
            <Space direction={'vertical'} style={{ width: '100%' }}>
                <Row>
                    <Typography.Text>共{tableProps.pagination.total}条记录</Typography.Text>
                </Row>

                <Table
                    rowKey={'id'}
                    columns={getColumns(
                        tableProps.pagination.current,
                        tableProps.pagination.pageSize,
                        refresh,
                        modal,
                        editFaq,
                        mutateRow,
                        domainList || [],
                    )}
                    {...tableProps}
                    loading={data?.status === 'init' || tableProps.loading}
                />
            </Space>
        </Space>
    );
};
export default App;
