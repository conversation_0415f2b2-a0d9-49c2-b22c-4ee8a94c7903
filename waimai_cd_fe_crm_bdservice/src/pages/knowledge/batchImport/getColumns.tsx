import { HookAPI } from 'antd/es/modal/useModal';
import useEditFaq from '@src/pages/knowledge/components/editor/editModal/editFaq';
import { ColumnsType } from 'antd/es/table';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import { Button, Checkbox, Space } from 'antd';
import AnswerCol from '@src/pages/knowledge/components/AnswerCol';
import { DOMAIN } from '../text';
import { getDomainDesc } from '../hooks/domainList';

type Data = NonNullable<APISpec['/manage/phrase/standard/batch/list']['response']['list']>[number];
export const getColumns = (
    pageNum = 1,
    pageSize = 10,
    refresh,
    modal: HookAPI,
    editFaq: ReturnType<typeof useEditFaq>,
    mutateRow: (row: Data) => void,
    domainList: { id: string; domain: string }[],
): ColumnsType<Data> => [
    {
        title: '序号',
        dataIndex: 'id',
        render: (v, r, i) => i + 1 + (pageNum - 1) * pageSize,
    },
    {
        title: '相似问题',
        dataIndex: 'similarQuestion',
        render: v => (v ? v : ''),
    },
    {
        title: '相似问题的答案',
        dataIndex: 'similarAnswer',
        width: 200,
        render: (answer, row) => <AnswerCol question={row.similarQuestion} answer={answer} />,
    },
    {
        title: DOMAIN,
        dataIndex: 'domainId',
        render: v => getDomainDesc(v, domainList),
    },
    {
        title: '待入库问题',
        dataIndex: 'question',
    },
    {
        title: '待入库答案',
        dataIndex: 'answer',
        width: '30%',
        render: (answer, row) => <AnswerCol question={row.question} answer={answer} maxWidth={400} />,
    },
    {
        title: '是否替换',
        dataIndex: 'replace',
        render: (v, r) =>
            r.similarQuestion ? (
                <Checkbox
                    checked={v}
                    onChange={async v => {
                        // 本地更新
                        mutateRow({ ...r, replace: Number(v.target.checked) });
                        const res: any = await apiCaller.post('/manage/phrase/standard/batch/similarReplace', {
                            id: r.id,
                            replace: Number(v.target.checked),
                        });
                        // 失败则刷新列表，把数据刷回
                        if (res.code !== 0) {
                            refresh();
                        }
                    }}
                />
            ) : null,
    },
    {
        title: '操作',
        dataIndex: 'id',
        render: (_, r) => {
            return (
                <Space>
                    <Button
                        type={'link'}
                        onClick={() => {
                            editFaq('batchImport', r as any);
                        }}
                    >
                        编辑
                    </Button>
                    <Button
                        type={'link'}
                        onClick={() => {
                            modal.confirm({
                                title: '删除该知识',
                                content: '删除后不可恢复，请谨慎删除',
                                onOk: async () => {
                                    const res = await apiCaller.post('/manage/phrase/standard/batch/delete', {
                                        id: r.id,
                                    });
                                    if (res.code === 0) {
                                        refresh();
                                    }
                                },
                            });
                        }}
                    >
                        删除
                    </Button>
                </Space>
            );
        },
    },
];
