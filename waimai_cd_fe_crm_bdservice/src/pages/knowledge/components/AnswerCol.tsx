import { App, Typography } from 'antd';
import { delta2text } from '@src/pages/knowledge/sessionDetail/msg2delta';
import { RightOutlined } from '@ant-design/icons';
import Quill2Editor from '@src/pages/knowledge/components/editor/quill2';

const AnswerCol = ({ answer, question, maxWidth = 200 }) => {
    const { modal } = App.useApp();
    return (
        <div className="standard-detail-wrapper" style={{ position: 'relative' }}>
            <Typography.Text style={{ maxWidth: maxWidth }} ellipsis={true}>
                {delta2text(answer) || '-'}
            </Typography.Text>
            {answer ? (
                <span
                    className={'standard-detail-btn'}
                    onClick={() => {
                        modal.confirm({
                            title: <div style={{ maxWidth: 350 }}>{question}</div>,
                            closable: true,
                            icon: null,
                            content: <Quill2Editor value={answer} readMode className={'answer-col'} />,
                            footer: null,
                        });
                    }}
                >
                    详情
                    <RightOutlined />
                </span>
            ) : null}
        </div>
    );
};
export default AnswerCol;
