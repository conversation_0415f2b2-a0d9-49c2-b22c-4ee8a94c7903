import { Form, Select, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { getStandardTableData } from '../hooks/standardTable';
import { Data } from '../types';
import { useDebounceFn } from 'ahooks';

const AutoSearchSelect = ({
    value,
    options,
    debounceSearch,
    fetching,
    ...rest
}: {
    value?: any;
    options: any[];
    debounceSearch: (key: any) => void;
    fetching?: boolean;
}) => {
    useEffect(() => {
        if (value && !options.length) {
            debounceSearch(value);
        }
    }, [value, options]);
    return (
        <Select
            filterOption={false}
            placeholder={'请输入标准问'}
            allowClear
            showSearch
            fieldNames={{ label: 'question', value: 'id' }}
            onSearch={debounceSearch}
            options={options}
            value={value}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            {...rest}
        />
    );
};
const StandardQuestionItem = () => {
    const [options, setOptions] = useState<Data[]>([]);
    const [fetching, setFetching] = useState(false);
    const debounceSearch = useDebounceFn(async key => {
        setFetching(true);
        if (!key) {
            return setOptions([]);
        }
        const { list } = await getStandardTableData({ current: 1, pageSize: 20 }, { name: key });
        setOptions(list);
        setFetching(false);
    }).run;
    return (
        <Form.Item label={'标准问'} name={'phraseData'}>
            <AutoSearchSelect debounceSearch={debounceSearch} options={options} fetching={fetching} />
        </Form.Item>
    );
};
export default StandardQuestionItem;
