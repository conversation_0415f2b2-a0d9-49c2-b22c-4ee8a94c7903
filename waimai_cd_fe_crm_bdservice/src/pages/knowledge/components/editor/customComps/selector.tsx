import { SelectorItemMessage, SelectorMessage } from '@src/pages/knowledge/message';
import { Typography, Space, Card, Button, Row, App, Radio } from 'antd';
import Quill from 'quill';
import { createRoot } from 'react-dom/client';
import { CardWithAvatarComp } from '@src/pages/knowledge/components/editor/customComps/cardWithAvatar';
import { useState } from 'react';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';

const EmbedBlot = Quill.import('blots/embed') as any;
export const SelectorItemComp = ({
    value,
    needCard = true,
}: {
    value?: SelectorItemMessage['insert']['selectorItem'];
    needCard?: boolean;
}) => {
    const { content, title } = value || {};
    const ele = (
        <>
            <Typography.Title level={5}>{title}</Typography.Title>
            <Row>
                {content?.map(({ label, value }) => (
                    <Space key={value} style={{ marginRight: 8 }}>
                        <Typography.Text className={'c_666'}>{label}:</Typography.Text>
                        <Typography.Text className={'c_666'}>{value}</Typography.Text>
                    </Space>
                ))}
            </Row>
        </>
    );

    return needCard ? <Card>{ele}</Card> : ele;
};
export const customSelectorItem = () => {
    class CustomBlot extends EmbedBlot {
        static create(value: any) {
            const node: any = super.create();
            const root = createRoot(node);
            root.render(<SelectorItemComp value={value} needCard={false} />);
            return node;
        }
    }
    CustomBlot.blotName = 'selectorItem';
    CustomBlot.tagName = 'div';
    Quill.register(CustomBlot, true);
};

const ModalContent = ({ value, onItemClick, withRadio, withLimit, onChange }) => {
    const data = withLimit ? value.content.slice(0, value.showNum) : value.content;
    const [checkedValue, setCheckedValue] = useState<string>(value.content[0].title);
    return (
        <>
            {data.map(({ content, title, type }, index) => (
                <Card
                    key={title}
                    onClick={() => {
                        setCheckedValue(title);
                        onChange?.(value.content[index]);
                        type !== 'poi_private' && onItemClick(value.content[index]);
                    }}
                    className={'pointer'}
                >
                    <div style={{ display: 'flex' }}>
                        {withRadio ? <Radio checked={checkedValue === title} /> : null}
                        {type === 'poi_private' ? (
                            <CardWithAvatarComp
                                value={
                                    {
                                        title,
                                        online: content.find(v => v.key === 'online')?.value as string,
                                        avatar: content.find(v => v.key === 'avatar')?.value as string,
                                        content: content.filter(v => [undefined, 'label', 'ID'].includes(v.key)) as any,
                                        tags: content.filter(v => v.key === 'tag').map(v => v.value) as any,
                                    } as any
                                }
                            />
                        ) : (
                            <div>
                                <Typography.Title level={5}>{title}</Typography.Title>
                                {content.map(({ label, value }) => (
                                    <Row key={value}>
                                        <Typography.Text className={'c_666'}>{label}:</Typography.Text>
                                        <Typography.Text className={'c_666'}>{value}</Typography.Text>
                                    </Row>
                                ))}
                            </div>
                        )}
                    </div>
                </Card>
            ))}
        </>
    );
};
export const SelectorComp = ({
    value = {} as any,
    onItemClick = () => {},
}: {
    value?: SelectorMessage['insert']['selector'];
    onItemClick?: (item: SelectorMessage['insert']['selector']['content'][number]) => void;
}) => {
    const { modal } = App.useApp();
    const { getWidth } = useClientWidth();
    return (
        <Space direction={'vertical'}>
            <Typography.Text>{value.titleInIm}</Typography.Text>
            <ModalContent
                value={value}
                onItemClick={onItemClick}
                withRadio={false}
                withLimit={true}
                onChange={() => {}}
            />
            {value.showNum < value.content.length ? (
                <Row justify={'center'}>
                    <Button
                        type={'link'}
                        onClick={() => {
                            let modalIns: any = {};
                            let checkedPoi = value[0];
                            modalIns = modal.confirm({
                                okText: '查询',
                                onOk: () => {
                                    onItemClick(checkedPoi);
                                },
                                title: '选择驳回原因',
                                width: getWidth(0.92),
                                closable: true,
                                icon: null,
                                content: (
                                    <div style={{ overflowY: 'scroll', height: 700 }}>
                                        <ModalContent
                                            value={value}
                                            onItemClick={() => {}}
                                            withRadio={true}
                                            withLimit={false}
                                            onChange={item => {
                                                checkedPoi = item;
                                            }}
                                        />
                                    </div>
                                ),
                            });
                        }}
                    >
                        {value.extendButtonName}
                    </Button>
                </Row>
            ) : null}
        </Space>
    );
};

export const customSelector = () => {
    class CustomBlot extends EmbedBlot {
        static create(value: any) {
            const node: any = super.create();
            const root = createRoot(node);
            root.render(<SelectorComp value={value} />);
            return node;
        }
    }
    CustomBlot.blotName = 'selector';
    CustomBlot.tagName = 'div';
    Quill.register(CustomBlot, true);
};
