import Quill from 'quill';
import CodeBlock from 'quill/formats/code';
/* eslint-disable-next-line */
import Parchment from 'parchment';
import { BlockEmbed } from 'quill/blots/block';
import { customCardWithAvatar } from './customComps/cardWithAvatar';
import { customSelector, customSelectorItem } from './customComps/selector';
import { createRoot } from 'react-dom/client';
const Scope = Parchment.Scope;
const Delta = Quill.import('delta');

const customQuill = () => {
    // 自定义图标，支持文本，最终会通过innerHTML的方式注入
    const icons = Quill.import('ui/icons') as any;
    icons['disabledMode'] = '预览';
    Quill.register(icons);

    // 不做校验，支持任何协议头的链接
    const Link = Quill.import('formats/link');
    // 自定义链接验证逻辑
    class CustomLink extends (Link as any) {
        static sanitize(url) {
            return url;
        }
    }
    // 注册自定义链接格式
    Quill.register(CustomLink, true);

    // 支持MP4链接文本渲染为视频
    const Clipboard = Quill.import('modules/clipboard');
    function deltaEndsWith(delta, text) {
        let endText = '';
        for (
            let i = delta.ops.length - 1;
            i >= 0 && endText.length < text.length;
            --i // eslint-disable-line no-plusplus
        ) {
            const op = delta.ops[i];
            if (typeof op.insert !== 'string') break;
            endText = op.insert + endText;
        }
        return endText.slice(-1 * text.length) === text;
    }
    // 自定义链接验证逻辑
    class CustomClipboard extends (Clipboard as any) {
        convert(_ref2, ...rest) {
            const { html, text } = _ref2;
            const formats = arguments.length > 1 && rest[0] !== undefined ? rest[0] : {};
            if (formats[CodeBlock.blotName]) {
                return new Delta().insert(text || '', {
                    [CodeBlock.blotName]: formats[CodeBlock.blotName],
                });
            }
            if (!html) {
                if (text.startsWith('http') && text.endsWith('.mp4')) {
                    return new Delta([{ insert: { video: text } }]);
                }
                return new Delta().insert(text || '', formats);
            }
            const delta = this.convertHTML(html);
            // Remove trailing newline
            if (deltaEndsWith(delta, '\n') && (delta.ops[delta.ops.length - 1].attributes == null || formats.table)) {
                return delta.compose(new Delta().retain(delta.length() - 1).delete(1));
            }
            return delta;
        }
    }
    // 注册自定义链接格式
    Quill.register('modules/clipboard', CustomClipboard as any, true);

    const Video = Quill.import('formats/video');
    // 自定义链接验证逻辑
    class CustomVideo extends (Video as any) {
        static tagName = 'VIDEO';
        static scope = Scope.INLINE_BLOT;
        static create(value) {
            const node = super.create(value);
            node.setAttribute('contenteditable', 'false');
            node.setAttribute('frameborder', '0');
            node.setAttribute('allowfullscreen', 'true');
            node.setAttribute('src', this.sanitize(value));
            node.setAttribute('controls', String(true));
            node.setAttribute('style', 'display: inline-block;');
            return node;
        }
    }
    // 注册自定义链接格式
    Quill.register('formats/video', CustomVideo as any, true);

    class Options extends BlockEmbed {
        static create(value: any) {
            const node: any = super.create();
            node.className = 'options';

            // value是数组意味着是旧数据结构，没有tabs字段
            const options = Array.isArray(value) ? value : value.options;
            const tabs = Array.isArray(value) ? [] : value.tabs;

            const root = createRoot(node);
            root.render(
                <>
                    {tabs.length ? (
                        <div style={{ display: 'flex' }}>
                            {tabs.map(({ label, value }) => (
                                <div key={value} style={{ marginRight: 4 }}>
                                    {label}
                                </div>
                            ))}
                        </div>
                    ) : null}
                    {options.map(({ content, url }) => (
                        <div className={'options-item'} key={url}>
                            {url ? <a href={url}>{content}</a> : <span>{content}</span>}
                            {'>'}
                        </div>
                    ))}
                </>,
            );
            return node;
        }
    }
    Options.blotName = 'options';
    Options.tagName = 'div';
    Quill.register(Options, true);

    class Buttons extends BlockEmbed {
        static create(value: any[]) {
            const node: any = super.create();
            node.className = 'buttons';
            value.forEach(v => {
                const item = document.createElement('button');
                item.className = 'buttons-item';
                let content = document.createTextNode(v?.text);
                if (v.url) {
                    content = document.createElement('a').appendChild(content);
                }
                if (v.type === 'primary') {
                    item.className += ' primary';
                }
                if (v.color) {
                    item.style.backgroundColor = v.color;
                }
                item.appendChild(content);
                node.appendChild(item);
            });
            return node;
        }
    }
    Buttons.blotName = 'buttons';
    Buttons.tagName = 'div';
    Quill.register(Buttons, true);

    customCardWithAvatar();
    customSelector();
    customSelectorItem();
};
export default customQuill;
