import React from 'react';
import { Card } from 'antd';
import dayjs from 'dayjs';
import './KnowledgeCard.scss';

interface KnowledgeCardProps {
    title: string;
    description: string;
    updateTime: string;
    sliceCount: number;
    onClick?: () => void;
}

const KnowledgeCard: React.FC<KnowledgeCardProps> = ({ title, description, updateTime, sliceCount, onClick }) => {
    return (
        <Card hoverable onClick={onClick} className="knowledge-card">
            <h3 className="knowledge-card__title">{title}</h3>
            <p className="knowledge-card__desc">{description}</p>
            <div className="knowledge-card__footer">
                <span className="knowledge-card__time">
                    更新时间：{dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss')}
                </span>
                <span className="knowledge-card__count">切片数量：{sliceCount}</span>
            </div>
        </Card>
    );
};

export default KnowledgeCard;
