.knowledge-home {
  padding: 24px;

  &__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      border: 1px solid #FF6A00;
      position: relative;
      overflow: hidden;
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(255, 106, 0, 0.05) 0%, rgba(255, 106, 0, 0) 70%);
        border-radius: 0 0 0 100%;
      }

      &__value {
        font-size: 28px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
      }

      &__title {
        display: flex;
        align-items: center;
        gap: 8px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 12px;
        font-weight: 500;

        .anticon {
          color: #FF6A00;
          opacity: 0.7;
          font-size: 16px;
          transition: all 0.3s;
          
          &:hover {
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }

    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
} 