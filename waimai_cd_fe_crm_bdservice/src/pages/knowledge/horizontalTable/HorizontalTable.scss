.horizontal-table-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.horizontal-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #fafafa;
}

.horizontal-table-title {
    margin: 0 !important;
    color: #222;
}

.rotate-back-btn,
.close-btn {
    color: #666;

    &:hover {
        color: #ff6a00;
    }

    .anticon {
        margin-right: 4px;
    }
}

.horizontal-table-content {
    flex: 1;
    padding: 16px 24px;
    overflow: hidden;
}

.horizontal-table {
    .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 500;
        color: #222;
        border-color: #e8e8e8;
    }

    .ant-table-tbody > tr > td {
        border-color: #e8e8e8;
    }

    .ant-table-tbody > tr:hover > td {
        background-color: #f5f5f5;
    }

    .markdown-content {
        white-space: pre-wrap;
        word-break: break-word;
    }
}

.horizontal-table-loading {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
}

.horizontal-table-error {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;

    .ant-typography {
        color: #666;
    }
}
