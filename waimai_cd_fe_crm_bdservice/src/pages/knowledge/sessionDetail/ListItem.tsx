import { APISpec } from '@mfe/cc-api-caller-pc';
import { Avatar, Row } from 'antd';
import Robot from '@src/assets/images/chat/icon.gif';
import './style.scss';
import { Config, Message, SuffixOptionsMessage } from '../message';
import MessageUI from '@src/pages/knowledge/chat/common/ui/message/indexNew';
import { MessageFrom } from '@src/pages/knowledge/chat/common/type/message';
import { adaptServerMessageComponents } from '@src/pages/knowledge/chat/common/service/sendMessage/adaptServerMessage';

interface Props {
    data: APISpec['/manage/session/conversations']['response']['conversations'][number] & {
        conversationType: 'question' | 'answer';
    };
}
const ListItem = ({ data }: Props) => {
    const left = data.conversationType === 'answer';
    const right = data.conversationType === 'question';
    let delta: any = [];
    try {
        const jsonData = JSON.parse(data.currentContent) as any;
        if (typeof jsonData === 'number') {
            throw new Error('currentContent是字符串型数字');
        }
        delta = adaptServerMessageComponents(jsonData);
    } catch (e) {
        delta = [{ insert: data.currentContent, type: 'text' }];
    }
    const config: Config['insert']['config'] = delta
        .filter((v: Message) => v.type === 'config')
        .map(({ insert: { config } }) => config)
        .reduce((pre, cur) => ({ ...pre, ...cur }), {}) as Config['insert']['config'];
    const suffixOptions: SuffixOptionsMessage['insert']['suffixOptions'] | undefined = delta.find(
        (v: Message) => v.type === 'suffixOptions',
    )?.insert.suffixOptions;

    return (
        <>
            <Row justify={left ? 'start' : 'end'} className={'message'}>
                {left && <Avatar src={Robot} size={'large'} style={{ transform: 'scale(1.4)', marginTop: 12 }} />}
                {right && (
                    <MessageUI
                        data={{ data: delta, from: MessageFrom.right, config, suffixOptions }}
                        style={{ maxWidth: '80%' }}
                    />
                )}
                {left && (
                    <MessageUI
                        data={{ data: delta, from: MessageFrom.left, suffixOptions }}
                        noFooter
                        style={{ maxWidth: '80%' }}
                    />
                )}
                {right && <Avatar style={{ background: '#ededed' }} />}
            </Row>
        </>
    );
};
export default ListItem;
