import React, { useState, useRef, useEffect } from 'react';
import AntSelectPro from '@src/components/AntSelectPro';
import type { DefaultOptionType } from 'antd/es/select';
import { MisSearchResponse } from '../../types';
import { SelectProps } from 'antd';
import { APISpec } from '@mfe/cc-api-caller-pc';
import './index.scss';
interface OptionType extends DefaultOptionType {
    value: string | number;
    label: string;
    [key: string]: any;
}

interface IProps<T = any> extends Omit<SelectProps<T>, 'options'> {
    /** 搜索接口地址 */
    searchUrl?: keyof APISpec;
    /** 搜索接口额外参数 */
    searchParams?: Record<string, any>;
    /** 自定义选项转换函数 */
    transformOptions?: (data: any) => OptionType[];
}

const AutoSearch: React.FC<IProps> = props => {
    const { transformOptions, searchUrl, searchParams = { pageNum: 1, pageSize: 30 }, ...restProps } = props;
    const [showSearch, setShowSearch] = useState(!props.value?.length);
    const selectRef = useRef<any>(null);
    console.log('====', props.value);

    useEffect(() => {
        if (!(props.value || []).length) {
            setShowSearch(true);
        }
    }, [props.value]);

    return (
        <div className="knowledge-auto-search">
            <AntSelectPro
                {...restProps}
                ref={selectRef}
                variant="borderless"
                suffixIcon=""
                searchUrl={searchUrl}
                allowClear
                clearOptionsOnBlur
                showSearch={showSearch}
                transformOptions={(res: MisSearchResponse) => {
                    return transformOptions ? transformOptions(res) : [];
                }}
                searchParams={searchParams}
                withOriginal
                mode="multiple"
                onBlur={() => {
                    if ((props.value || [])?.length) {
                        setShowSearch(false);
                        selectRef?.current?.blur();
                    }
                }}
                // onClear={() => {
                //     setShowSearch(true);
                //     selectRef?.current?.blur();
                // }}
            />

            {props.value?.length ? (
                <div
                    className="knowledge-auto-search-area"
                    onClick={() => {
                        selectRef?.current?.focus();
                        setShowSearch(true);
                    }}
                />
            ) : null}
        </div>
    );
};

export default AutoSearch;
