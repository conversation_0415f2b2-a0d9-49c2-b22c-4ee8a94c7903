import React, { useState } from 'react';
import { Button, Form, Input, Table, Space, message, Modal, Select, Tooltip, Row, Col } from 'antd';
import { useAntdTable, useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { KnowledgeDetailItem } from './types';
import ArchiveTreeSelect from './components/ArchiveTreeSelect';
import ModifyKnowledgeInfoModal from './components/ModifyKnowledgeInfoModal';
import FragmentDrawer from './components/FragmentDrawer';
import './components/FragmentDrawer.scss';
import { getDataSetId } from './utils/getDataSetId';
import dayjs from 'dayjs';
import { ColumnType } from 'antd/es/table';
import { QuestionCircleOutlined } from '@ant-design/icons';

const getAuthTypeName = (authType: string) => {
    switch (authType) {
        case 'dataset':
            return '知识库权限';
        case 'wiki':
            return '知识库权限，学城权限';
        default:
            return '-';
    }
};

const App: React.FC = () => {
    const [form] = Form.useForm();
    const [modifyModalVisible, setModifyModalVisible] = useState(false);
    const [fragmentDrawerVisible, setFragmentDrawerVisible] = useState(false);
    const [selectedWikiId, setSelectedWikiId] = useState<number>();
    const datasetId = getDataSetId();

    // 获取知识库详情
    const { data: detailData, refresh: refreshDetail } = useRequest(
        async () => {
            const res = await apiCaller.send(
                '/manage/dataset/detail',
                { id: datasetId },
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                },
            );
            if (res.code !== 0) return { name: '', desc: '', bizLine: '', authOrgList: [], authUserList: [] };
            return res.data;
        },
        {
            ready: !!datasetId,
        },
    );

    // 获取知识库列表
    const getTableData = async ({ current, pageSize }: { current: number; pageSize: number }, formData: any) => {
        const categories = (formData?.categories || []).map(item => item.namePath.join('-'));
        const params = {
            categories: categories?.length ? categories : undefined,
            hasFormatProblem: formData.hasFormatProblem ?? undefined,
            query: formData.query || undefined,
            modifyMis: formData.modifyMis || undefined,
            authType: formData.authType || undefined,
            pageNum: current,
            pageSize,
            datasetId,
        };

        const res = await apiCaller.send('/manage/dataset/data/list', params);
        if (res.code !== 0) return { total: 0, list: [] };

        const { datasetWikiInfoList } = res.data;
        return {
            total: res.data.total,
            list: datasetWikiInfoList,
        };
    };

    const { tableProps, search } = useAntdTable(getTableData, {
        form,
        defaultPageSize: 20,
    });

    tableProps.pagination = {
        ...tableProps.pagination,
        pageSizeOptions: [10, 20, 30],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`,
    };

    const { submit, reset } = search;

    const handleViewFragment = (wikiId: number) => {
        setSelectedWikiId(wikiId);
        setFragmentDrawerVisible(true);
    };

    const handleDelete = (wikiId: number) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该文档吗？此操作无法撤销。',
            okButtonProps: {
                danger: true,
            },
            onOk: () => {
                apiCaller
                    .send('/manage/wiki/delete', {
                        wikiId,
                        datasetId,
                    })
                    .then(res => {
                        if (res.code !== 0) return;
                        message.success('删除成功');
                        reset();
                    });
            },
        });
    };

    const columns: ColumnType<KnowledgeDetailItem>[] = [
        {
            title: '名称',
            dataIndex: 'title',
            key: 'title',
            align: 'center',
        },
        {
            title: '来源',
            dataIndex: 'url',
            key: 'url',
            align: 'center',
            render: (url: string) => (
                <a href={url} style={{ wordBreak: 'break-all' }} target="_blank" rel="noopener noreferrer">
                    {url}
                </a>
            ),
        },
        {
            title: '最近更新人',
            dataIndex: 'modifyMis',
            key: 'modifyMis',
            align: 'center',
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            align: 'center',
            width: '10%',
            minWidth: 120,
            render: (updateTime: string) => dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '是否引用文档',
            dataIndex: 'isReferWiki',
            key: 'isReferWiki',
            align: 'center',
            width: '10%',
            minWidth: 120,
            render: (isReferWiki: boolean) => (isReferWiki ? '是' : '否'),
        },
        {
            title: (
                <div>
                    格式问题
                    <Tooltip title="含P0-P2所有格式问题">
                        <QuestionCircleOutlined />
                    </Tooltip>
                </div>
            ),
            dataIndex: 'hasFormatProblem',
            key: 'hasFormatProblem',
            align: 'center',
            width: '10%',
            render: (hasFormatProblem: boolean) =>
                hasFormatProblem ? (
                    <span style={{ color: '#FF0000' }}>有</span>
                ) : (
                    <span style={{ color: '#52c41a' }}>无</span>
                ),
        },
        {
            title: '标签',
            dataIndex: 'tags',
            key: 'tags',
            align: 'center',
            width: '10%',
            render: (tags: string[]) => <span>{(tags || []).join('/') || '-'}</span>,
        },
        {
            title: '权限',
            dataIndex: 'authType',
            key: 'authType',
            align: 'center',
            width: '10%',
            render: (authType: string) => <span>{getAuthTypeName(authType)}</span>,
        },
        {
            title: '操作',
            key: 'action',
            align: 'center',
            width: '10%',
            minWidth: 180,
            render: (_: any, record: KnowledgeDetailItem) => (
                <>
                    <Button style={{ padding: 0 }} type="link" onClick={() => handleViewFragment(record.wikiId)}>
                        查看切片
                    </Button>
                    <Button type="text" danger onClick={() => handleDelete(record.wikiId)}>
                        删除文档
                    </Button>
                </>
            ),
        },
    ];

    return (
        <div className="knowledge-detail">
            <div className="knowledge-detail__header">
                <div className="knowledge-detail__header-title">{detailData?.name}</div>
                <div className="knowledge-detail__header-actions">
                    <Button onClick={() => setModifyModalVisible(true)}>修改信息</Button>
                    <Button type="primary" onClick={() => (location.href = `../upload?datasetId=${datasetId}`)}>
                        上传知识
                    </Button>
                </div>
            </div>

            <div className="knowledge-detail__search">
                <Form
                    form={form}
                    onKeyDown={e => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            submit();
                        }
                    }}
                >
                    <Row gutter={16} style={{ width: '100%' }}>
                        <Col span={8}>
                            <Form.Item name="query">
                                <Input placeholder="按原文档名称/名称关键词/URL搜索" />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="categories">
                                <ArchiveTreeSelect
                                    datasetId={datasetId}
                                    multiple={true}
                                    treeCheckable={true}
                                    levelCanCheck={[3]}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="modifyMis">
                                <Input placeholder="请选择最新更新人(输入mis号)" />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16} style={{ width: '100%' }}>
                        <Col span={8}>
                            <Form.Item name="hasFormatProblem">
                                <Select placeholder="是否存在格式问题" allowClear>
                                    <Select.Option value={true}>有格式问题</Select.Option>
                                    <Select.Option value={false}>无格式问题</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="authType">
                                <Select placeholder="请选择权限范围" allowClear>
                                    <Select.Option value={'dataset'}>知识库权限</Select.Option>
                                    <Select.Option value={'wiki'}>学城权限</Select.Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <Form.Item name="authType">
                                <Space>
                                    <Button type="primary" onClick={submit}>
                                        搜索
                                    </Button>
                                    <Button onClick={reset}>清空</Button>
                                </Space>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </div>

            <div className="knowledge-detail__table">
                <Table {...tableProps} columns={columns} rowKey={'id'} />
            </div>

            <ModifyKnowledgeInfoModal
                visible={modifyModalVisible}
                onClose={() => setModifyModalVisible(false)}
                onSuccess={refreshDetail}
                initialValues={{
                    desc: detailData?.desc || '',
                    bizLine: detailData?.bizLine || '',
                    org: (detailData?.authOrgList || []).map(item => ({ value: item.id, label: item.orgPathName })),
                    user: (detailData?.authUserList || []).map(item => ({
                        value: item.id,
                        label: `${item.name}(${item.mis || '-'})`,
                    })),
                }}
                datasetId={datasetId}
            />

            <FragmentDrawer
                open={fragmentDrawerVisible}
                onClose={() => setFragmentDrawerVisible(false)}
                wikiId={selectedWikiId}
                datasetId={datasetId}
            />
        </div>
    );
};

export default App;
