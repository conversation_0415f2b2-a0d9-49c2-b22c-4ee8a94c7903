import createUpload from '@ai/mss-upload-js';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import getS3Host, { Region } from '@src/utils/getS3Host';

const uploadImage = (file, fileType: 'img' | 'video' = 'img') => {
    return new Promise<string | undefined>((res, rej) => {
        const uploadInstance = createUpload(
            {
                onFileInfo: () => {},
                file,
                signatureFunc: (() =>
                    apiCaller.get(`${import.meta.env.VITE_API_PREFIX}/assistant/common/s3` as any, {})) as any,
                bucket: 'bdaiassistant-public',
                s3_host: getS3Host(Region.BEI_JING, true),
                prefix_type: 's3_style',
                accept: fileType === 'img' ? ['.png', '.jpg'] : ['.mp4'],
                hashMode: true,
                onSuccess(fileUrl) {
                    res(fileUrl);
                },
                onError(errorMsg) {
                    rej(errorMsg);
                },
                onProgress() {},
                onStart() {},
                onFinish() {},
                validateFile: () => true,
            },
            1,
        );
        uploadInstance.upload();
    });
};
export default uploadImage;
