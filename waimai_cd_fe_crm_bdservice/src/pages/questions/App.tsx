import { useEffect, useState } from 'react';
import './style.scss';
import { postLXLog, trackPage } from '@src/utils/questions/track';
import { Environment, getEnvironment } from '@mfe/cc-ocrm-utils';
import { message } from 'antd';
import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import openAssistant from '@src/pages/knowledge/chat/common/utils/openAssistant';

type TTInfo = APISpec['/helpbd/r/ttInfo']['response'];
type RobotInfo = APISpec['/helpbd/r/moses']['response'];

const isTitans = /titans/i.test(window.navigator.userAgent);
const isProd = getEnvironment() === Environment.PRORD;

const DEFAULT_TENANT_INFO = {
    tenantName: '外卖',
    bizName: '外卖白领',
    bizId: 5001,
    tenantId: 1000008,
    source: 'daxiang_workbench',
};

export const mapPropsToString = <T,>(obj: Record<keyof T, T[keyof T]>) =>
    Object.keys(obj).reduce((memo, key) => {
        memo[key] = obj[key] == null ? undefined : `${obj[key]}`;
        return memo;
    }, {} as Record<keyof T, string>);

const App = () => {
    const [ttInfo, setTTInfo] = useState<TTInfo>();
    const [robotInfo, setRobotInfo] = useState<RobotInfo>();

    const getRobotInfo = async () => {
        // @ts-ignore
        const res: any = await apiCaller.get('/bee/v1/bdaiassistant/fetchRobot', {
            ...mapPropsToString(DEFAULT_TENANT_INFO),
        });

        if (res.code !== 0) {
            return;
        }

        setRobotInfo(res.data);
    };

    const getTTInfo = async () => {
        const res = await apiCaller.get('/bee/v2/bdaiassistant/helpbd/r/ttInfo', {});
        if (res.code !== 0) {
            return;
        }

        setTTInfo(res.data);
    };

    const openWebView = link => {
        let openLink = link;
        if (link.indexOf('http') && isTitans) {
            openLink = `mtdaxiang://www.meituan.com/web?url=${encodeURIComponent(link)}`;
        }
        window.location.href = openLink;
    };

    const handleJumpReporter = () => {
        const reporterLink = isProd
            ? 'https://tt.sankuai.com/ticket/handle?filter=todo'
            : 'http://tt.cloud.test.sankuai.com/ticket/handle?filter=todo';
        postLXLog('b_waimai_e_dx_complain_click_reporter_mc');
        openWebView(reporterLink);
    };

    const handleJumpResolved = () => {
        const resolvedLink = isProd
            ? 'https://tt.sankuai.com/ticket/list?filter=createdBy'
            : 'http://tt.cloud.test.sankuai.com/ticket/handle?filter=createdBy';
        postLXLog('b_waimai_e_dx_complain_click_resolved_mc');
        openWebView(resolvedLink);
    };

    const handleJumpTTList = () => {
        const ttListLink = isProd
            ? 'https://tt.sankuai.com/ticket/list?filter=createdBy'
            : 'http://tt.cloud.test.sankuai.com/ticket/list?filter=createdBy';
        postLXLog('b_waimai_e_dx_complain_click_ttlist_mc');
        openWebView(ttListLink);
    };

    const getMosesUrl = (id: string) => {
        const MOSES_HOST = [Environment.ST, Environment.PRORD].includes(getEnvironment() as Environment)
            ? 'https://moses.meituan.com'
            : 'http://moses.nlp.test.sankuai.com';
        return `${MOSES_HOST}/chat?robotKey=${id}&userId=${ttInfo?.misId}`;
    };

    const handleJumpRobot = () => {
        if (!robotInfo) {
            return message.warning('暂未获取到机器人信息，请稍候再试～');
        }

        let url = robotInfo.aiUrl;

        if (robotInfo.id) {
            url = getMosesUrl(robotInfo.id);
        }

        postLXLog('b_waimai_e_dx_complain_click_moses_mc');
        apiCaller.post('/bee/v2/bdaiassistant/helpbd/w/tracking/entry', {
            ...DEFAULT_TENANT_INFO,
            robotId: String(robotInfo.aiUrl || robotInfo.id),
            robotType: robotInfo.robotType,
        });

        // 0为服体，1为摩西，2为智能助手
        if (robotInfo.robotType === 2) {
            return openAssistant('daxiang');
        } else {
            openWebView(url);
        }
    };

    useEffect(() => {
        trackPage();
        getTTInfo();
        // getNotice();
        getRobotInfo();
    }, []);

    return (
        <div className="dx-complain">
            <div className="complain-tt-statics">
                <div onClick={handleJumpReporter}>
                    <span className="tt-title">待我处理</span>
                    <span className="tt-number">{ttInfo?.unresolved || 0}</span>
                </div>
                <div onClick={handleJumpResolved}>
                    <span className="tt-title">我发起的</span>
                    <span className="tt-number">{ttInfo?.reporter || 0}</span>
                </div>
            </div>
            <hr />
            <div className="complain-container">
                <div className="complain-button-container">
                    <div onClick={handleJumpTTList}>工单列表</div>
                    <div className="dark-button" onClick={handleJumpRobot}>
                        我要提问
                    </div>
                </div>
            </div>
        </div>
    );
};

export default App;
