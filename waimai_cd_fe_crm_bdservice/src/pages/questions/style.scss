.dx-complain {
    hr {
        margin: 20px 0;
    }
    a {
        color: #007fff;
    }
    .complain-tt-statics {
        display: flex;
        justify-content: space-around;
        padding: 0 2rem;
        margin: 1rem 0;
        div {
            display: flex;
            flex-direction: column;
            span {
                text-align: center;
                margin-top: 1rem;
            }
            .tt-title {
                font-size: 1.2rem;
                font-weight: 200;
                color: #333;
            }
            .tt-number {
                font-size: 1.8rem;
                font-weight: 400;
                margin-top: 1rem;
                cursor: pointer;
            }
        }
    }
    .complain-container {
        padding: 0 0.6rem;
    }
    .complain-button-container {
        display: flex;
        justify-content: space-around;
        margin-bottom: 1rem;
        div {
            border: 1px solid #000;
            padding: 0.6rem 2.5rem;
            font-size: 1.1rem;
            border-radius: 0.3rem;
            cursor: pointer;
        }
        .dark-button {
            background-color: #202532;
            color: #fff;
        }
    }
    .complain-notice {
        background-color: #fff;
        border: 1px solid #fff;
        border-radius: 0.4rem;
        display: flex;
        flex-direction: column;
        font-size: 1rem;
        margin: 0 1rem;
        padding: 0.5rem 1rem;
        span {
            text-align: center;
            margin: 0.2rem 0;
        }
    }
    .complain-message {
        color: #999;
        font-size: 1rem;
    }
    .complain-notice-content {
        font-size: 0.9rem;
        max-height: 15rem;
        overflow-y: scroll;
    }
    .complain-notice-button {
        color: #007fff;
    }
    .roo-modal-mask {
        background-color: rgba(0, 0, 0, 0.6) !important;
    }
    .notice-message {
        display: flex;
        justify-content: space-around;
    }
}
