.product-detail {
    &__productNameTag {
        background: #ff6a0010;
        color: #ff6a00;
    }
    .product-detail__overview {
        // background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(194px);
        background-image: url('../../../assets//images/product-detail.png');
        background-size: cover;
        border-radius: 8px;
        padding: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
        min-width: 950px;

        &__use-percent {
            font-weight: bold;
            margin-bottom: 12px;
            line-height: 30px;
        }
        &__usage {
            padding: 0 28px;
        }

        &__subtitle {
            color: #333;
            margin-right: 8px;
        }

        &__left {
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        &__block {
            border: 1px solid #ffffff;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
            border-radius: 4px;
            flex: 1;
            padding: 24px;

            &__text {
                background: #e9eaf260;
                border-radius: 4px;
                padding: 10px 8px;
                display: flex;
                justify-content: space-between;
                min-width: 141px;
            }

            &__chart {
                width: 135px;
                height: 90px;
                margin-right: 24px;
            }
        }
    }
}
