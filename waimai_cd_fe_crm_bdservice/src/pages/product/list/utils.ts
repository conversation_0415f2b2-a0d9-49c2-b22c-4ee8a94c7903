import { APISpec } from '@mfe/cc-api-caller-pc';
import {
    ProductListSearchFormData,
    ProductQualifiedStatus,
    ProductSource,
    ProductStatus,
    ProductTag,
    ProductType,
} from '@src/types/product';
import dayjs from 'dayjs';

type ProductItem = APISpec['/product/task/searchTaskList']['response']['row'][number];

export const formatProductItem = (p: ProductItem) => {
    const { grantStat, allStatus, status } = p;
    const usePercentageNum = grantStat.total ? +((grantStat.useTotal * 100) / grantStat.total).toFixed(2) : null;
    // 除了已下发/已过期的状态，其他都展示为待下发
    const dispatchStatus = [ProductStatus.已下发, ProductStatus.已过期].includes(status)
        ? status
        : ProductStatus.待下发;
    return {
        ...p,
        product: {
            ...p.product,
            productTag: (p.product.tagList || []).find(t => t !== ProductTag.有奖资源),
            productNameTag: (p.product.tagList || []).find(t => t === ProductTag.有奖资源),
        },
        selectionStatus:
            p.product.productType === ProductType.BD
                ? undefined
                : allStatus.includes(ProductStatus.已圈选)
                ? ProductStatus.已圈选
                : ProductStatus.待圈选,

        dispatchStatus,
        usePercentageNum,
        qualifiedStatus:
            (usePercentageNum || 0) >= p.customTargetPercent
                ? ProductQualifiedStatus.MEET
                : ProductQualifiedStatus.NOT_MEET,
        ablity: {
            select:
                p.product.productType === ProductType.CM &&
                dispatchStatus === ProductStatus.待下发 &&
                dayjs().isBefore(p.product.validRange.startTime),
        },
    };
};

export const formatProductListFormData = (formData: ProductListSearchFormData) => {
    const { dateRange, selectionStatusWithProductType, grantStatus, qualified, ...rest } = formData;
    const createTime = dateRange?.value
        ? {
              startTime: dayjs(dateRange?.value).startOf(dateRange.type).format('YYYY-MM-DD HH:mm:ss'),
              endTime: dayjs(dateRange?.value).endOf(dateRange.type).format('YYYY-MM-DD HH:mm:ss'),
          }
        : undefined;

    return {
        ...rest,
        ...selectionStatusWithProductType,
        createTime,
        productSource: ProductSource.商增,
        grantStatus: grantStatus || undefined,
        qualified: qualified != null ? !!qualified : undefined,
    };
};
