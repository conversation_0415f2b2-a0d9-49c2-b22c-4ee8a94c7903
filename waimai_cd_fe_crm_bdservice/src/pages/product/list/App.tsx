import { apiCaller } from '@mfe/cc-api-caller-pc';
import ProductListSearchForm from '@src/components/product/ProductListSearchForm';
import { FormatedProductItem, ProductListSearchFormData } from '@src/types/product';
import { useAntdTable } from 'ahooks';
import { Form } from 'antd';
import { formatProductItem, formatProductListFormData } from './utils';
import ProductListTable from '@src/components/product/ProductListTable';
import './style.scss';
import Title from 'antd/es/typography/Title';
import { useUserContext } from '@src/components/User/UserProvider';

interface Result {
    total: number;
    list: FormatedProductItem[];
}

const App = () => {
    const [form] = Form.useForm();
    const { user } = useUserContext();

    const getTableData = async ({ current, pageSize }, formData: ProductListSearchFormData): Promise<Result> => {
        if (!formData.orgIdList || !formData.orgIdList.length || !user) {
            return {
                total: 0,
                list: [],
            };
        }

        const res = await apiCaller.send('/product/task/searchTaskList', {
            pageNum: current,
            pageSize,
            operatorUid: user.id,
            ...formatProductListFormData(formData),
        });

        if (res.code !== 0) {
            return {
                total: 0,
                list: [],
            };
        }

        return {
            total: res.data.total,
            list: res.data.row.map(formatProductItem),
        };
    };
    const { tableProps, search, loading } = useAntdTable(getTableData, {
        defaultPageSize: 10,
        form,
    });
    return (
        <div className="product-list">
            <Title level={4} style={{ marginBottom: 20 }}>
                资源中心
            </Title>
            <ProductListSearchForm form={form} submitting={loading} submit={search.submit} />

            <ProductListTable form={form} tableProps={tableProps} reload={search.submit} />
        </div>
    );
};

export default App;
