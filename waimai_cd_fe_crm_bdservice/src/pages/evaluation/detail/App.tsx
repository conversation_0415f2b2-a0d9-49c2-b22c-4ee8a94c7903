import { apiCaller } from '@mfe/cc-api-caller-pc';
import Title from '@src/components/Title';
import { useQuery } from '@src/hooks/useQuery';
import { useRequest } from 'ahooks';
import { Button, Descriptions, Empty, Spin, Typography, message, Tooltip } from 'antd';
import style from './style.module.scss';
import EvaluationDetailField from '@src/components/evaluation/EvaluationDetailField';
import { useEffect } from 'react';
import { trackPage } from '@src/utils/questions/track';
import CommentDetail from '@src/pages/evaluation/detail/CommentDetail';
import { QuestionCircleOutlined } from '@ant-design/icons';

const pageCase = trackPage('c_waimai_m_7omezafq');

const App = () => {
    const { query, loaded } = useQuery();

    const fetchData = async () => {
        const { uid, orgId, serviceType, beginTime, endTime } = query;
        const res = await apiCaller.send('/impc/service/getServiceDetail', {
            uid,
            serviceType,
            beginTime,
            endTime,
            orgId,
            version: new URL(window.location.href).searchParams.get('version'),
        });

        if (res.code !== 0) {
            return;
        }

        return res.data;
    };

    const fetchCommentData = async () => {
        const { uid, orgId, beginTime, endTime } = query;
        const res = await apiCaller.send('/impc/service/getCommentDetail', {
            uid,
            beginTime,
            endTime,
            orgId,
        });

        if (res.code !== 0) {
            return;
        }

        return res.data;
    };

    const { run: getServiceData, data, loading: serviceLoading } = useRequest(fetchData, { manual: true });
    const {
        run: getCommentData,
        data: commentData,
        loading: commentLoading,
    } = useRequest(fetchCommentData, { manual: true });

    const loading = commentLoading || serviceLoading;

    const run = () => {
        const { serviceType } = query;
        if (serviceType === 'satisfySocreRate') {
            getCommentData();
        } else {
            getServiceData();
        }
    };

    const exportData = async () => {
        pageCase('moduleClick', 'b_waimai_m_bv9fumgc_mc', null);
        const res = await apiCaller.send('/impc/service/exportDetail', query as any);

        if (res.code !== 0) {
            return;
        }

        message.success(res.data);
    };

    const { run: doExport, loading: exporting } = useRequest(exportData, {
        manual: true,
    });

    const onItemClick = (key: string) => {
        pageCase('moduleClick', 'b_waimai_m_163ra55n_mc', {
            custom: {
                metric_name: key,
            },
        });
    };

    useEffect(() => {
        if (!loaded) {
            return;
        }
        run();
    }, [loaded]);

    if (!(data || commentData) || loading) {
        return <Spin spinning />;
    }

    const sourceData = data?.list[0];

    if (!(sourceData || commentData)) {
        return <Empty />;
    }

    const { serviceType } = query;
    return (
        <div className={style['evaluation-detail']}>
            <Title level={4}>{query.name}-底层数据指标</Title>

            <div
                style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginTop: '20px',
                    marginBottom: serviceType === 'satisfySocreRate' ? 0 : 20,
                }}
            >
                <Typography.Text type="danger" style={{ marginLeft: 5 }}>
                    点击下方橙色字体↓直接看明细！无需导出！！！
                </Typography.Text>
                <Button onClick={doExport} loading={exporting} type="primary">
                    导出
                </Button>
            </div>
            {serviceType === 'satisfySocreRate' ? (
                <a
                    target={'_blank'}
                    style={{ fontSize: 16, marginBottom: 20, display: 'block' }}
                    href={
                        'https://readata-wm.sankuai.com/pages/101/1501543930917818373/1526179007802953785?RPType=MoShu&RPId=111547'
                    }
                >
                    工单&拜访评价明细请点击
                    <Tooltip title={'商家选择“不认可平台规则”标签，会做免责剔除，不扣服务分'}>
                        <QuestionCircleOutlined />
                    </Tooltip>
                </a>
            ) : null}

            {data?.head.map(h => (
                <Descriptions
                    key={h.typeId}
                    title={h.typeName}
                    items={Object.keys(h.prop).map(k => ({
                        key: k,
                        label: h.prop[k],
                        children: (
                            <EvaluationDetailField
                                {...query}
                                field={k}
                                value={sourceData?.[k]}
                                onClick={() => onItemClick(`${k}:${h.prop[k]}`)}
                            />
                        ),
                    }))}
                />
            ))}
            <CommentDetail data={commentData} />
        </div>
    );
};

export default App;
