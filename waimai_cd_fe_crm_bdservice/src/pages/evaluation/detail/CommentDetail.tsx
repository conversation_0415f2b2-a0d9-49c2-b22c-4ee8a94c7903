import { Descriptions, Table, Tag } from 'antd';
import { APISpec } from '@mfe/cc-api-caller-pc';
import style from './style.module.scss';
import { useQuery } from '@src/hooks/useQuery';
interface Props {
    data?: APISpec['/impc/service/getCommentDetail']['response'];
}

const CommentDetail = ({ data }: Props) => {
    const { query } = useQuery();

    if (!data?.list?.length) {
        return null;
    }

    const columns: any[] = [1, 2, 3, 4, 5, 6].map(v => ({
        width: v % 2 !== 0 ? 200 : undefined,
        dataIndex: `col${v}`,
        key: `col${v}`,
        render: data => {
            if (!data) {
                return { props: { rowSpan: 0 } };
            }
            if (Array.isArray(data)) {
                return (
                    <span style={{ display: 'flex', flexDirection: 'column' }}>
                        {data.map(d => (
                            <Tag color={'pink'} style={{ marginTop: 5, width: 140 }} key={d}>
                                {d}
                            </Tag>
                        ))}
                    </span>
                );
            }
            if (typeof data === 'object') {
                return data;
            }
            if (data.includes('工单')) {
                const [label, value] = data.split(':');
                // const { misId, orgId, beginTime, endTime: queryEndTime } = query;
                // const startTime = beginTime && dayjs.unix(+beginTime).format('YYYY-MM-DD');
                // const endTime = queryEndTime && dayjs.unix(+queryEndTime).format('YYYY-MM-DD');

                return (
                    <span>
                        {label}: {value}
                        {/*<Button*/}
                        {/*    type="link"*/}
                        {/*    size="small"*/}
                        {/*    target="_blank"*/}
                        {/*    href={bellwetherLinkParse(*/}
                        {/*        `/page/dove/biz/poiBizList${objectToQuery({*/}
                        {/*            misId,*/}
                        {/*            orgId,*/}
                        {/*            startTime,*/}
                        {/*            endTime,*/}
                        {/*        })}`,*/}
                        {/*    )}*/}
                        {/*>*/}
                        {/*    {value}*/}
                        {/*</Button>*/}
                    </span>
                );
            }
            return data;
        },
    }));
    columns.unshift({
        width: 160,
        dataIndex: 'total',
        key: 'total',
        onCell: () => ({
            className: style['gray-cell'],
            style: { backgroundColor: '#d8d8d8' },
        }),
        render: d => {
            return (
                d || {
                    props: {
                        rowSpan: 0,
                    },
                }
            );
        },
    });

    return (
        <>
            <h2 style={{ fontSize: 16, marginRight: 10 }}>组织</h2>
            <Descriptions items={[{ label: '组织架构', children: data.org }]} />
            {data.list.map(d => {
                const { detail } = d;
                if (!detail) {
                    return null;
                }
                const { commetDetail } = detail;
                const dataSource: any[] = [
                    [
                        ...(commetDetail?.head || []).map((v, i) => ({
                            [`col${i * 2 + 1}`]: {
                                children: v,
                                props: {
                                    colSpan: 2,
                                },
                            },
                        })),
                        {
                            total: {
                                children: detail.totalDesc || '',
                                props: {
                                    rowSpan: 2,
                                },
                            },
                        },
                    ].reduce((pre, cur) => ({ ...pre, ...cur })),
                    commetDetail?.commentDesc,
                ];
                if (commetDetail?.labelDesc) {
                    dataSource.push({
                        ...commetDetail?.labelDesc,
                        total: {
                            children: detail.tagTotalDesc || '',
                        },
                    });
                }
                return (
                    <div key={d.typeName} style={{ marginBottom: 16 }}>
                        <h2 style={{ fontSize: 16, marginBottom: 20 }}>{d.typeName}</h2>
                        <Table
                            rowKey={r => r.col1}
                            className={style['no-hover']}
                            bordered
                            showHeader={false}
                            pagination={false}
                            columns={columns}
                            dataSource={dataSource}
                            rowClassName={(_, i) => (i === 0 ? style['gray-cell'] : '')}
                        />
                    </div>
                );
            })}
        </>
    );
};
export default CommentDetail;
