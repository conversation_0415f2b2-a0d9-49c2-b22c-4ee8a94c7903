import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import EvaluationSearchForm from '@src/components/evaluation/EvaluationSearchForm';
import EvaluationStatistics from '@src/components/evaluation/EvaluationStatistics';
import { useRequest } from 'ahooks';
import { FloatButton, Image, Popover, Result, Spin } from 'antd';
import EvaluationServiceItemChart from '@src/components/evaluation/EvaluationServiceItemChart';
import EvaluationOrgTable from '@src/components/evaluation/EvaluationOrgTable';
import EvaluationServiceChart from '@src/components/evaluation/EvaluationServiceChart';
import style from './style.module.scss';
import { QrcodeOutlined } from '@ant-design/icons';
import ServiceQrcodeImg from '@src/assets/images/service-qrcode.jpeg';
import { trackPage } from '@src/utils/questions/track';
import VersionContext, { defaultVersion } from '@src/pages/evaluation/context/version';

type SearchParams = APISpec['/impc/service/getServicePoints']['request'];

const pageCase = trackPage('c_waimai_m_xaj9iooe');

const App = () => {
    const fetchEvaluationInfo = async (params: SearchParams) => {
        // @ts-ignore
        params.version = defaultVersion;
        const res = await apiCaller.send('/impc/service/getServicePoints', params);

        if (res.code !== 0) {
            return;
        }

        return res.data;
    };

    const { run, loading, data } = useRequest(fetchEvaluationInfo, {
        manual: true,
    });

    return (
        <VersionContext.Provider
            value={{
                version: defaultVersion,
                setVersion: () => {},
            }}
        >
            <div className={style['evaluation-overview']}>
                <Spin spinning={loading}>
                    <EvaluationSearchForm onSearch={run} />

                    {data ? (
                        <>
                            <EvaluationStatistics data={data.dataOverview} />

                            <EvaluationServiceItemChart data={data.serviceItem} />

                            <EvaluationOrgTable data={data.orgDistributed} tracker={pageCase} />

                            <EvaluationServiceChart data={data.marketData} />
                        </>
                    ) : !loading ? (
                        <Result status="404" title="暂无数据" subTitle="暂时没有可供展示的数据，请切换搜索条件" />
                    ) : null}
                </Spin>
                <FloatButton
                    icon={
                        <Popover
                            placement="rightBottom"
                            content={<Image src={ServiceQrcodeImg} preview={false} width={300} />}
                        >
                            <QrcodeOutlined />
                        </Popover>
                    }
                />
            </div>
        </VersionContext.Provider>
    );
};

export default App;
