import { Button, Input, InputNumber, Modal, Popconfirm, Table, TableColumnType, Tour, Typography, message } from 'antd';
import { useLiveRequestIdContext } from './RequestIdProvider';
import S3Upload from '@src/components/S3Upload';
import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import { useAntdTable, useBoolean, useDebounceFn, useRequest } from 'ahooks';
import { produce } from 'immer';
import ScriptList from '@src/components/live/ScriptList';
import './style.scss';
import { DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import { UploadChangeParam, UploadFile } from 'antd/es/upload';
import { useEffect, useRef } from 'react';

type TableData = APISpec['/livescript/sku/list']['response']['data'][number];

const TOUR_KEY = 'live-script__my-script';

const App = () => {
    const { requestId, reload } = useLiveRequestIdContext();
    const scriptListRef = useRef(null);
    const [open, { setFalse, setTrue }] = useBoolean();

    const fetchFileInfo = async (params: UploadChangeParam<UploadFile<string>>) => {
        const { file } = params;
        if (!file || file.status !== 'done') {
            return;
        }

        const res = await apiCaller.send('/livescript/sku/import', {
            url: file.response,
            requestId,
        });

        if (res.code !== 0) {
            return;
        }

        search.reset();
        Modal.info({
            content: res.data ? `已为您成功导入${res.data}条数据` : '暂未识别到数据',
        });
    };

    const fetchTableData = async ({ current, pageSize }) => {
        // @ts-ignore
        const res = await apiCaller.send('/livescript/sku/list', {
            requestId,
            pageNum: current,
            pageSize,
        });

        if (res.code !== 0) {
            return {
                total: 0,
                list: [],
            };
        }

        return {
            total: res.data.total || 0,
            list: (res.data.data || []).map(d => ({
                ...d,
                price: +(Number(d.price) / 100).toFixed(2),
                discount: +(Number(d.discount) / 100).toFixed(2),
            })),
        };
    };

    const { search, loading, tableProps, mutate } = useAntdTable(fetchTableData, {
        defaultPageSize: 20,
    });

    const onFieldEdit = (payload: Record<string, any>, skuId?: string) => {
        mutate(
            produce(draft => {
                if (!draft) {
                    return;
                }
                const item = draft.list.find(it => it.id === skuId);
                if (!item) {
                    return;
                }
                for (const key in payload) {
                    item[key] = payload[key];
                }
                debouncedEdit({ skuId, ...payload });
            }),
        );
    };

    const submitEdit = async p => {
        const discount = p.discount != null ? p.discount * 100 : undefined;
        await apiCaller.send('/livescript/sku/edit', {
            requestId,
            ...p,
            discount,
        });

        search.submit();
    };
    const { run: debouncedEdit } = useDebounceFn(submitEdit, { wait: 2000 });

    const handleDelete = async (skuId?: string) => {
        const res = await apiCaller.send('/livescript/sku/delete', {
            requestId,
            skuId,
        });
        if (res.code !== 0) {
            return;
        }

        search.reset();
    };

    const handleGenerate = async () => {
        const res = await apiCaller.send('/livescript/task/generate', {
            requestId,
        });

        if (res.code !== 0) {
            return;
        }

        Modal.success({
            content: '正在为您生成中，生成成功后请留心大象公众号的通知',
        });
    };

    const { run: submit, loading: submitting } = useRequest(handleGenerate, {
        manual: true,
    });

    const beforeFileUpload = (file: File) => {
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            message.error('请上传小于10M的文件!');
        }
        return isLt10M;
    };

    const onCloseTour = () => {
        localStorage.setItem(TOUR_KEY, '1');
        setFalse();
    };

    useEffect(() => {
        if (localStorage.getItem(TOUR_KEY)) {
            return;
        }

        setTimeout(() => {
            setTrue();
        }, 2000);
    }, []);

    const columns: TableColumnType<TableData>[] = [
        {
            dataIndex: 'id',
            title: '商品id',
        },
        {
            dataIndex: 'skuName',
            title: '商品名',
        },
        {
            dataIndex: 'poiName',
            title: '商家名',
        },
        {
            dataIndex: 'price',
            title: '原价',
        },
        {
            dataIndex: 'discount',
            title: '折扣',
            render: (text, record) => (
                <InputNumber
                    value={+text}
                    min={0}
                    max={1}
                    step={0.1}
                    precision={2}
                    onChange={v => onFieldEdit({ discount: v }, record.id)}
                />
            ),
        },
        {
            title: '折后价格',
            render: (_, record: TableData) => +(Number(record.price) * Number(record.discount)).toFixed(2),
        },
        {
            dataIndex: 'category',
            title: '类别',
        },
        {
            dataIndex: 'keywords',
            title: '关键词',
            width: 200,
            render: (text, record) => (
                <Input.TextArea
                    value={text}
                    maxLength={30}
                    onChange={e => onFieldEdit({ keywords: e.target.value }, record.id)}
                />
            ),
        },
        {
            title: '操作',
            fixed: 'right',
            render: (_, record) => (
                <Popconfirm title="确认要删除吗？" onConfirm={() => handleDelete(record.id)}>
                    <Button type="link">删除</Button>
                </Popconfirm>
            ),
        },
    ];

    const steps = [
        {
            title: '我的脚本',
            description: '点击这里可以查看我的脚本哦～',
            target: () => scriptListRef.current,
        },
    ];

    return (
        <div className="live-assistant">
            <div className="live-assistant__left">
                <div className="live-assistant__head">
                    <div className="live-assistant__title">直播脚本助手</div>
                    <Typography.Text type="secondary">通过AIGC大模型的力量帮助你上手直播更快</Typography.Text>
                </div>

                <div>
                    <S3Upload
                        accept={'.xls,.xlsx'}
                        onChange={fetchFileInfo}
                        maxCount={1}
                        beforeUpload={beforeFileUpload}
                    >
                        <Button icon={<UploadOutlined />} type="primary" style={{ marginBottom: 10 }}>
                            按模板批量导入
                        </Button>
                        <div>
                            <Typography.Text type="secondary" onClick={e => e.stopPropagation()}>
                                支持上传xls, xlsx格式的文件，最大不超过10MB 点我
                                <Typography.Link
                                    href="https://s3plus.meituan.net/waimai-m-bee-prod/live-script/%E7%9B%B4%E6%92%AD%E6%A8%A1%E6%9D%BF.xlsx"
                                    target="_blank"
                                >
                                    下载模板
                                </Typography.Link>
                            </Typography.Text>
                        </div>
                    </S3Upload>
                </div>

                <div style={{ textAlign: 'right' }}>
                    <Popconfirm title="确定要清空吗？" onConfirm={reload}>
                        <Button type="link" icon={<DeleteOutlined />}>
                            一键清空
                        </Button>
                    </Popconfirm>
                </div>

                <Table
                    {...tableProps}
                    loading={loading}
                    // @ts-ignore
                    columns={columns}
                    rowKey={'id'}
                    style={{ marginTop: 10 }}
                />

                {tableProps.dataSource.length ? (
                    <div style={{ textAlign: 'right', marginTop: 20 }}>
                        <Button type="primary" onClick={submit} loading={submitting}>
                            点击生成
                        </Button>
                    </div>
                ) : null}
                <ScriptList ref={scriptListRef} />
            </div>

            <Tour open={open} onClose={onCloseTour} steps={steps} />
        </div>
    );
};

export default App;
