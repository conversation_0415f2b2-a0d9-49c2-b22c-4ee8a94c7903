import { render } from '@src/module/root';
import App from './App';
import LiveRequestIdProvider from './RequestIdProvider';
import { useEffect } from 'react';
import { trackPage } from '@src/utils/questions/track';

const Wrapper = () => {
    useEffect(() => {
        trackPage('c_waimai_m_o7oyo5h1');
    }, []);

    return (
        <LiveRequestIdProvider>
            <App />
        </LiveRequestIdProvider>
    );
};

render(<Wrapper />, '直播脚本助手');
