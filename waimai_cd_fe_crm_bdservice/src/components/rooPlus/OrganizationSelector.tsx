import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';
import { OrganizationSelectorProps } from '@roo/roo-plus/OrganizationSelector/interface';

import '@roo/roo/theme/default/index.css';
import '@roo/roo-plus/theme/default/index.css';
import { useRef, useState } from 'react';
const prefix = '/xianfu/api/common';

interface CommonItem {
    childrenList: CommonItem[];
    id: number;
    isSelected: boolean;
    isManager: boolean;
}
const defaultAPI = {
    pidUrl: `${prefix}/uicomponent/api/orgs/getByPid`,
    searchUrl: `${prefix}/uicomponent/api/orgs/search`,
};

// TODO: 初始值选择
export default (
    props: Omit<OrganizationSelectorProps, 'resetDisabled'> & {
        value?: number[] | number;
        onChange?: (v: number[] | number) => void;
        onInit?: (n?: number[] | number) => void;
    },
) => {
    const defaultAllOrgs = useRef<Set<number>>(new Set());
    const hasPrivilege = useRef(false);
    const [defaultValue, setDefaultValue] = useState<number | number[]>();

    const dig = (item: CommonItem) => {
        // 总部权限
        if (item.id < 0) {
            hasPrivilege.current = true;
        }
        defaultAllOrgs.current?.add(item.id);
        if (item.childrenList) {
            return item.childrenList.map(dig);
        }

        if (!item.isSelected && !item.isManager) {
            return;
        }

        return item.id;
    };

    const applyDefaultValue = (data: CommonItem[]) => {
        if (!data.length) {
            props.onInit?.(props.multiple ? [] : undefined);
            return;
        }
        const diggedIds = data.map(dig).flat(Infinity).filter(Boolean);

        const hasValue = props.value instanceof Array ? props.value.length : props.value;

        if (hasValue) {
            return;
        }
        if (diggedIds.some(v => v < 0)) {
            props.onInit?.(props.multiple ? [] : undefined);
            return;
        }

        const ids = props.multiple ? diggedIds : diggedIds[0];
        setDefaultValue(ids);
        props.onInit?.(ids);
    };

    const onConfirm = (
        // @ts-ignore
        ...args: Parameters<OrganizationSelectorProps['onConfirm']>
    ) => {
        const ids = args[0];

        if (typeof ids === 'number' ? !ids : !ids?.length) {
            defaultValue && props.onChange?.(defaultValue);
            return;
        }
        props.onChange?.(ids);
        props.onConfirm?.(ids, args[1]);
    };

    return (
        <OrganizationSelector
            placeholder="请选择组织结构"
            {...props}
            {...defaultAPI}
            // resetDisabled
            // @ts-ignore
            onMountedFetchFn={applyDefaultValue}
            filterDisableFn={it => {
                if (hasPrivilege.current) {
                    return false;
                }
                const item = it as CommonItem;
                if (item.isSelected || item.isManager) {
                    return false;
                }
                // @ts-ignore
                return !item.editable;
            }}
            defaultValue={defaultValue}
            onConfirm={onConfirm}
        />
    );
};
