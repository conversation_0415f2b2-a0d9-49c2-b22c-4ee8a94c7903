import { theme } from 'antd';
import { PropsWithChildren, useLayoutEffect } from 'react';
import '@src/assets/overrides-roo.scss';

const OverridesRoo = (props: PropsWithChildren<any>) => {
    const useToken = theme.useToken();

    // antd >= 5.12已经通过configprovider theme.cssVar属性支持了css variable
    // 但是antd的cssvar是绑定在app组件对应的dom上，而roo的modal等组件渲染在app dom外, 无法感知
    // 所以我们只能再写一份

    useLayoutEffect(() => {
        if (!useToken.token) {
            return;
        }
        const variableText = Object.keys(useToken.token)
            .reduce((memo, key) => {
                const value = useToken.token[key];
                if (typeof value !== 'string' && typeof value !== 'number') {
                    return memo;
                }
                const valueText = typeof value === 'string' ? value : `${value}px`;
                memo.push(`--${key}: ${valueText}`);
                return memo;
            }, [] as string[])
            .join(';');

        // 创建一个新的style元素
        const style = document.createElement('style');
        // 为style元素设置类型为text/css
        style.type = 'text/css';

        // 这适用于大多数现代浏览器
        style.appendChild(document.createTextNode(`:root{${variableText}}`));
        // 将style元素添加到文档的<head>中
        document.head.appendChild(style);
    }, [useToken.token]);

    return <div>{props.children}</div>;
};

export default OverridesRoo;
