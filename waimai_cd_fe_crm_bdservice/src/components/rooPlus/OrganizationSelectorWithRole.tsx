import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';
import '@roo/roo/theme/default/index.css';
import '@roo/roo-plus/theme/default/index.css';
import { useRef } from 'react';
import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import { OrganizationSelectorProps as RooOrganizationSelectorProps } from '@roo/roo-plus/OrganizationSelector/interface';

export type OrganizationSelectorProps = Omit<RooOrganizationSelectorProps, 'resetDisabled'> & {
    value?: number[] | number;
    onChange?: (v: number[] | number) => void;
    onInit?: (n?: number[] | number) => void;
    authCode: string;
    sourceIds?: number[];
};

const TENANT = 1000008;

type OrgList = APISpec['/wm/org/shepherd/box/org-box-org-search']['response'];

// 接入了数据权限的组织结构选择器
export default (props: OrganizationSelectorProps) => {
    const minLevel = useRef<number>(0);
    const lastTenant = useRef<number>();
    const { authCode, sourceIds = [1, 4, 5, 6, 10, 11, 12, 13, 16, 17, 18, 23], ...rest } = props;
    const firstRender = useRef(true);

    const applyDefaultValue = data => {
        // 第一次渲染的时候，要尊重回显(props.value)的orgId
        if (firstRender.current && (Array.isArray(props.value) ? props.value.length : props.value)) {
            firstRender.current = false;
            props.onInit?.(props.value);
            return;
        }
        firstRender.current = false;

        if (!data.length) {
            props.onInit?.(props.multiple ? [] : undefined);
            return;
        }

        const allIds = (data as OrgList).map(it => it.id);

        const ids = props.multiple ? allIds : allIds[0];

        props.onInit?.(ids);
        return onConfirm(ids, {});
    };

    // @ts-ignore
    const onConfirm = (...args: Parameters<OrganizationSelectorProps['onConfirm']>) => {
        const ids = args[0];
        props.onChange?.(ids);
        props.onConfirm?.(ids, args[1]);
    };

    const pidService = async params => {
        if (!params.tenantId) {
            return { data: { code: 0, data: { list: [] } } };
        }

        if (lastTenant.current && lastTenant.current !== params.tenantId) {
            delete params.orgIds;
            delete params.anchorPointOrgIds;
        }
        if (!params.orgIds) {
            const res = await apiCaller.post('/wm/org/shepherd/box/org-box-org-search', params);
            if (res.code !== 0) {
                return {
                    data: res,
                };
            }

            if (!minLevel.current) {
                minLevel.current = res.data[0]?.level;
            }

            return {
                data: {
                    ...res,
                    data: { isHq: false, list: res.data },
                },
            };
        }

        // 回显
        params.anchorPointOrgIds = params.value;
        const res = await apiCaller.post('/wm/org/shepherd/box/org-box-org-tree-search', params);

        if (res.code !== 0) {
            return {
                data: { ...res, data: { isHq: false, list: [] } },
            };
        }

        const { tree } = res.data;

        try {
            const _tree = JSON.parse(tree);
            if (!minLevel.current) {
                minLevel.current = _tree[0]?.level;
            }

            return {
                data: {
                    ...res,
                    data: { isHq: false, list: _tree },
                },
            };
        } catch (e) {
            return {
                data: { ...res, data: { isHq: false, list: [] } },
            };
        }
    };

    return (
        // roo plus真的是个天才，没有提供wrapper组件的控制
        <div style={{ marginTop: -1 }}>
            <OrganizationSelector
                placeholder={'请选择组织结构'}
                {...rest}
                transformDataList={list =>
                    (list as OrgList).map(item => ({
                        ...item,
                        level: item.level - minLevel.current + 1,
                    }))
                }
                pidService={pidService}
                params={{
                    wmOrgBoxAuth: {
                        authCode: authCode,
                    },
                    tenantId: TENANT,
                    sourceIds: sourceIds,
                    parentId: 0,
                    pageUrl: location.href,
                }}
                onMountedFetchFn={applyDefaultValue}
                onConfirm={onConfirm}
            />
        </div>
    );
};
