// @ts-nocheck
import { DoubleLeftOutlined, ReloadOutlined } from '@ant-design/icons';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useAntdTable, useBoolean, useRequest } from 'ahooks';
import { <PERSON><PERSON>, Drawer, List, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import ScriptTag from './ScriptTag';
import { forwardRef, useState } from 'react';
import { TaskStatus } from './types';

const enum ViewStatus {
    FRESH = 0,
    READ,
}

const ScriptList = forwardRef<HTMLElement>((_, ref) => {
    const [open, { setFalse, setTrue }] = useBoolean();
    const [tooltipOpen, toggle] = useState(false);

    const fetchTableData = async ({ current, pageSize }) => {
        const res = await apiCaller.send('/livescript/task/list', {
            pageNum: current,
            pageSize,
        });

        if (res.code !== 0) {
            return {
                total: 0,
                list: [],
            };
        }

        return {
            total: res.data.total || 0,
            list: res.data.data,
        };
    };

    const fetchNewItem = async () => {
        const res = await fetchTableData({ current: 1, pageSize: 10 });

        const hasNew = res.list.some(it => it.status === TaskStatus.SUCCESS);

        if (!hasNew) {
            return;
        }

        toggle(true);
    };

    useRequest(fetchNewItem, { pollingInterval: 10 * 1000 });

    const { search, loading, tableProps } = useAntdTable(fetchTableData);

    const onOpenDrawer = () => {
        setTrue();
        search.reset();
        toggle(false);
    };

    const checkSuccess = (status?: TaskStatus) => {
        if (!status || ![TaskStatus.SUCCESS, TaskStatus.SUCCESS_VIEWED].includes(status)) {
            return false;
        }
        return true;
    };

    return (
        <div className="live-assistant__right">
            <Tooltip
                title="您有新的脚本生成啦，快来查看吧！"
                open={tooltipOpen}
                placement="topRight"
                trigger={[]}
                onOpenChange={toggle}
            >
                <Button ref={ref} icon={<DoubleLeftOutlined />} onClick={onOpenDrawer} />
            </Tooltip>

            <Drawer
                title="我的脚本"
                open={open}
                onClose={setFalse}
                extra={<ReloadOutlined spin={loading} onClick={search.reset} />}
                className="live-assistant__right__drawer"
            >
                <List
                    {...tableProps}
                    pagination={{
                        ...tableProps.pagination,
                        simple: true,
                        onChange: (current, pageSize) => tableProps.onChange({ current, pageSize }),
                        onShowSizeChange: (current, pageSize) => tableProps.onChange({ current, pageSize }),
                    }}
                    loading={loading}
                    itemLayout="vertical"
                    renderItem={item => (
                        <List.Item key={item.id}>
                            <Typography.Text type="secondary">
                                {dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                                {item.viewStatus === ViewStatus.FRESH ? (
                                    <Typography.Text type="danger" style={{ marginLeft: 5 }}>
                                        new
                                    </Typography.Text>
                                ) : null}
                                <ScriptTag status={item.status} style={{ marginLeft: 5 }} />
                            </Typography.Text>

                            <Typography.Paragraph
                                ellipsis={{
                                    rows: 2,
                                    // @ts-ignore
                                    suffix: checkSuccess(item.status) ? (
                                        <Typography.Link href={`./script-detail?id=${item.id}`} target="_blank">
                                            查看详情
                                        </Typography.Link>
                                    ) : undefined,
                                }}
                            >
                                {item.content}
                            </Typography.Paragraph>
                        </List.Item>
                    )}
                />
            </Drawer>
        </div>
    );
});

export default ScriptList;
