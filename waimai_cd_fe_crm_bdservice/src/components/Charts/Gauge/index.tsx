import { Gauge as AntGauge, GaugeConfig } from '@ant-design/plots';
import './style.scss';

interface Guage {
    percent: number;
    limit?: number;
    title: string;
}

const Gauge = (props: Guage) => {
    const { limit, title } = props;

    // AntGauge有bug, percent为0的时候，需要给一个极小值
    // 否则range会有问题
    const percent = props.percent || 0.000000001;

    const ticks = limit
        ? percent <= limit
            ? [0, percent, limit, limit + 0.005, 1]
            : [0, limit, limit + 0.005, percent, 1]
        : [0, percent, 1];

    const color = limit
        ? percent <= limit
            ? ['#FFDD00', '#E9EAF2', '#FF6A00', '#E9EAF2']
            : ['#FFDD00', '#FF6A00', '#FFDD00', '#E9EAF2']
        : ['#FFDD00', '#E9EAF2'];

    const percentNum = (percent * 100).toFixed(2);

    const [integer, decimal] = percentNum.split('.').map(Number);

    const decimalText = decimal ? `.${decimal}%` : '%';
    const config: GaugeConfig = {
        autoFit: true,
        range: {
            ticks: ticks,
            color: color,
            width: 12,
        },
        statistic: {
            title: {
                offsetY: -42,
                style: {
                    fontSize: '12px',
                    color: '#666',
                },
                formatter: () => title,
            },
            content: {
                customHtml: () =>
                    `<div class="gauge-title"><span class="gauge-title__text">${integer}</span><span class="gauge-title__percent">${decimalText}</span></div>`,
            },
        },
        // @ts-ignore
        legend: false,
        indicator: false,
    };
    return <AntGauge className="chart-gauge" {...config} percent={0.1} />;
};

export default Gauge;
