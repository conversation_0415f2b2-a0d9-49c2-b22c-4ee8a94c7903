import React, { useEffect } from 'react';
import { Drawer, Empty } from 'antd';
import { useRequest } from 'ahooks';
import TaskListItem, { TaskItem } from './TaskListItem';
import dayjs from 'dayjs';
import { ArrowLeftOutlined } from '@ant-design/icons';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';

interface TaskListDrawerProps {
    open: boolean;
    onClose: () => void;
    onSendMessage: (content: string) => void;
}

interface TaskGroup {
    createTime: string;
    title: string;
    itemList: TaskItem[];
}

interface TaskListResponse {
    jobList: TaskGroup[];
    total: number;
    success: number;
    fail: number;
    running: number;
}

const TaskListDrawer: React.FC<TaskListDrawerProps> = ({ open, onClose, onSendMessage }) => {
    const callerRequest = useCallerRequest();
    // 获取任务列表
    const {
        data: taskData,
        run: refreshTasks,
        cancel,
    } = useRequest(
        async () => {
            const res = await callerRequest.get('/bee/v2/bdaiassistant/job/poiDiagnosis/listToday', {});
            if (res.code === 0) {
                return res.data;
            }
            return null;
        },
        {
            pollingInterval: 2000, // 2秒轮询
            manual: true,
            onError: error => {
                console.error('获取任务列表失败:', error);
            },
        },
    );

    // 当抽屉打开时获取任务列表
    useEffect(() => {
        if (open) {
            refreshTasks();
        }
        return () => {
            cancel();
        };
    }, [open, refreshTasks]);

    // 处理任务列表数据
    const getAllTasks = (): TaskItem[] => {
        if (!taskData?.jobList) return [];

        const allTasks: TaskItem[] = [];
        taskData.jobList.forEach(group => {
            group.itemList.forEach(item => {
                allTasks.push({
                    ...item,
                    createTime: group.createTime,
                });
            });
        });

        return allTasks;
    };

    const getFilteredTasks = (status?: string): TaskItem[] => {
        const allTasks = getAllTasks();
        if (!status) return allTasks;
        return allTasks.filter(task => task.status === status);
    };

    const handleViewResult = (item: TaskItem) => {
        // 构建查询消息
        const queryMessage = item.content;

        // 发送消息到聊天界面
        onSendMessage(queryMessage);

        // 关闭抽屉
        onClose();
    };

    // 获取统计数据
    const runningTasks = getFilteredTasks('init');
    const successTasks = getFilteredTasks('success');
    const failTasks = getFilteredTasks('fail');

    return (
        <Drawer
            title={
                <div className="task-list-drawer-header">
                    <div className="task-list-drawer-title-container">
                        <div className="task-list-drawer-title-text">今日任务</div>
                        <div style={{ display: 'flex', cursor: 'pointer' }} onClick={onClose}>
                            <p
                                style={{
                                    color: '#999',
                                    fontSize: 14,
                                    fontWeight: 400,
                                    lineHeight: '16px',
                                    marginBottom: 3,
                                }}
                            >
                                |
                            </p>
                            <ArrowLeftOutlined style={{ fontSize: 14, color: '#999' }} />
                        </div>
                    </div>
                </div>
            }
            open={open}
            onClose={onClose}
            width={328}
            destroyOnClose
            closeIcon={false}
            styles={{
                body: {
                    padding: 0,
                },
            }}
            placement="left"
            className="task-list-drawer"
        >
            <div className="task-list-drawer-content">
                {/* 统计数据区域 */}
                <div className="task-list-drawer-stats-container">
                    <div className="task-list-drawer-stats-item">
                        <div className="task-list-drawer-stats-label">进行中</div>
                        <div className="task-list-drawer-stats-value">{runningTasks.length}</div>
                    </div>
                    <div className="task-list-drawer-stats-item">
                        <div className="task-list-drawer-stats-label">成功</div>
                        <div className="task-list-drawer-stats-value">{successTasks.length}</div>
                    </div>
                    <div className="task-list-drawer-stats-item">
                        <div className="task-list-drawer-stats-label">失败</div>
                        <div className="task-list-drawer-stats-value">{failTasks.length}</div>
                    </div>
                </div>

                {/* 任务列表区域 */}
                <div className="task-list-drawer-list-container">
                    {taskData?.jobList?.map((group, groupIndex) => (
                        <div key={groupIndex} className="task-list-drawer-group">
                            <div className="task-list-drawer-group-content">
                                <div className="task-list-drawer-group-time">
                                    {dayjs(group.createTime).format('HH:mm')}
                                </div>
                                <div className="task-list-drawer-group-main">
                                    <div className="task-list-drawer-group-title">{group.title}</div>
                                    <div className="task-list-drawer-group-items">
                                        {group.itemList.map((item, itemIndex) => (
                                            <TaskListItem
                                                key={`${groupIndex}-${itemIndex}`}
                                                item={item}
                                                onViewResult={handleViewResult}
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}

                    {(!taskData?.jobList || taskData.jobList.length === 0) && (
                        <Empty description="暂无任务" style={{ marginTop: 60 }} />
                    )}
                </div>
            </div>
        </Drawer>
    );
};

export default TaskListDrawer;
