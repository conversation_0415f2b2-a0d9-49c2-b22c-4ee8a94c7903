import React from 'react';
import { Tooltip } from 'antd';
import taskListIcon from '../../assets/images/chat/task_list.png';
import taskListLoadingIcon from '../../assets/images/chat/task_list_loading.png';
import taskListNeedClickIcon from '../../assets/images/chat/task_list_done.png';

interface TaskListButtonProps {
    runningCount: number;
    onClick: () => void;
    needToClick: boolean;
}

const TaskListButton: React.FC<TaskListButtonProps> = ({ runningCount, onClick, needToClick }) => {
    const hasRunningTasks = runningCount > 0;

    const getRunningIcon = () => {
        if (needToClick) {
            return taskListNeedClickIcon;
        }

        if (hasRunningTasks) {
            return taskListLoadingIcon;
        }

        return taskListIcon;
    };
    return (
        <Tooltip title="任务列表">
            <div
                onClick={onClick}
                style={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 8,
                    border: 'none',
                    background: 'rgba(255, 255, 255, 0.1)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                }}
            >
                {/* 图片容器 */}
                <div
                    style={{
                        position: 'relative',
                        width: 24,
                        height: 24,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                >
                    {/* 图片 */}
                    <img
                        src={getRunningIcon()}
                        alt="任务列表"
                        style={{
                            width: 24,
                            height: 24,
                            animation: hasRunningTasks ? 'spin 2s linear infinite' : 'none',
                        }}
                    />

                    {/* 运行中任务数量 */}
                    {hasRunningTasks && (
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                color: '#6047FA',
                                fontSize: 10,
                                fontWeight: 'bold',
                                pointerEvents: 'none',
                                zIndex: 1,
                            }}
                        >
                            {runningCount > 99 ? '99+' : runningCount}
                        </div>
                    )}
                </div>

                {/* 旋转动画样式 */}
                <style>
                    {`
                        @keyframes spin {
                            from {
                                transform: rotate(0deg);
                            }
                            to {
                                transform: rotate(360deg);
                            }
                        }
                    `}
                </style>
            </div>
        </Tooltip>
    );
};

export default TaskListButton;
