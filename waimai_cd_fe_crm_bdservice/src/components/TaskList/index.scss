.task-list-drawer {
    .ant-drawer-header {
        border-bottom: none !important;
        padding: 20px 20px 0 20px;
    }

    .task-list-drawer-header {
        margin-bottom: 12px;
        .task-list-drawer-title-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            .task-list-drawer-title-text {
                font-size: 16px;
                font-weight: 600;
                color: #222222;
                font-family: 'PingFang SC', sans-serif;
            }

            .task-list-drawer-download-icon {
                font-size: 16px;
                color: #999999;
                cursor: pointer;
            }
        }
    }

    .task-list-drawer-content {
        display: flex;
        flex-direction: column;
        gap: 24px;
        padding: 0 20px;
    }

    .task-list-drawer-stats-container {
        display: flex;
        gap: 12px;
        border-radius: 24px;
        background: #ffffff;
        align-items: center;

        .task-list-drawer-stats-item {
            border-radius: 8px;
            background: #f9fafc;
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: center;
            padding: 12px 0;
            flex: 1;

            .task-list-drawer-stats-label {
                color: #666666;
                font-family: 'PingFang SC', sans-serif;
                font-size: 12px;
                line-height: normal;
            }

            .task-list-drawer-stats-value {
                color: #222222;
                font-family: 'Meituan Digital Type', sans-serif;
                font-weight: 700;
                font-size: 18px;
                line-height: normal;
            }
        }
    }

    // 任务列表项样式
    .task-list-item {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        padding: 0;

        &.clickable {
            cursor: pointer;
        }

        &.disabled {
            cursor: default;
        }

        .task-item-image-container {
            position: relative;
            width: 46px;
            height: 46px;
            flex-shrink: 0;

            .task-item-image {
                position: relative;
                width: 44px;
                height: 44px;
                margin-top: 2px;

                .task-item-image-placeholder {
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    background: #d8d8d8;
                }

                .task-item-status-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    background: rgba(34, 34, 34, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &.fail {
                        bottom: 0;
                        top: auto;
                        height: 16px;
                        border-radius: 0 0 6px 6px;
                    }

                    .task-item-status-content {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 4px;

                        .task-item-status-icon {
                            .loading-icon {
                                width: 13px;
                                height: 12px;
                                background: #ffffff;
                                // 这里可以添加旋转动画
                            }
                        }
                    }

                    .task-item-status-text {
                        color: #ffffff;
                        font-family: 'PingFang SC', sans-serif;
                        font-size: 10px;
                        line-height: 16px;
                        text-align: center;

                        &.fail {
                            font-size: 11px;
                            line-height: 16px;
                        }
                    }
                }
            }

            .task-item-badge {
                position: absolute;
                top: 0;
                right: 0;
                width: 8px;
                height: 8px;
                background: #ff192d;
                border-radius: 50%;
            }
        }

        .task-item-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            gap: 4px;

            .task-item-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .poi-name {
                    color: #222222;
                    font-family: 'PingFang SC', sans-serif;
                    font-size: 14px;
                    line-height: 22px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 174px;

                    &.disabled {
                        color: #999999;
                    }
                }
            }

            .task-item-id {
                color: #666666;
                font-family: 'PingFang SC', sans-serif;
                font-size: 12px;
                line-height: 18px;

                &.disabled {
                    color: #999999;
                }
            }
        }

        .task-item-arrow {
            width: 10px;
            height: 10px;
            color: #999999;
            font-weight: 600;
            flex-shrink: 0;
            align-self: center;
            right: 20px;
            margin-bottom: 20px;
        }
    }

    .task-list-drawer-list-container {
        display: flex;
        flex-direction: column;
        gap: 32px;
    }

    .task-list-drawer-group {
        .task-list-drawer-group-content {
            display: flex;
            flex-direction: row;
            gap: 8px;

            .task-list-drawer-group-time {
                color: #999999;
                font-family: 'PingFang SC', sans-serif;
                font-size: 12px;
                line-height: 18px;
                text-align: center;
                width: 32px;
                flex-shrink: 0;
            }

            .task-list-drawer-group-main {
                display: flex;
                flex-direction: column;
                flex: 1;

                .task-list-drawer-group-title {
                    color: #222222;
                    font-family: 'PingFang SC', sans-serif;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                }

                .task-list-drawer-group-items {
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                }
            }
        }
    }
}