import React, { useState, useEffect } from 'react';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import TaskListButton from './TaskListButton';
import TaskListDrawer from './TaskListDrawer';
import './index.scss';

interface TaskListProps {
    onSendMessage: (content: string) => void;
}

const TaskList: React.FC<TaskListProps> = ({ onSendMessage }) => {
    const [drawerOpen, setDrawerOpen] = useState(false);

    // 获取运行中的任务数量
    const { data: runningData, cancel } = useRequest(
        async () => {
            const res = await apiCaller.get('/bee/v2/bdaiassistant/job/poiDiagnosis/runningToday', {});
            if (res.code === 0) {
                return res.data;
            }
            return null;
        },
        {
            pollingInterval: 2000, // 2秒轮询
            onError: error => {
                console.error('获取运行中任务数量失败:', error);
            },
        },
    );

    const runningCount = runningData?.runnings || 0;

    const handleButtonClick = () => {
        setDrawerOpen(true);
    };

    const handleDrawerClose = () => {
        setDrawerOpen(false);
    };

    useEffect(() => {
        return () => {
            cancel();
        };
    }, [cancel]);

    return (
        <>
            <TaskListButton
                needToClick={runningData?.needToClick}
                runningCount={runningCount}
                onClick={handleButtonClick}
            />

            <TaskListDrawer open={drawerOpen} onClose={handleDrawerClose} onSendMessage={onSendMessage} />
        </>
    );
};

export default TaskList;
