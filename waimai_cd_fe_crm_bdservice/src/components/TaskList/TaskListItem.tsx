import React from 'react';
import { RightOutlined } from '@ant-design/icons';

export interface TaskItem {
    /** 任务类型 */
    type: string;
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: number;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
    /** 创建时间 */
    createTime: string;
}

interface TaskListItemProps {
    item: TaskItem;
    onViewResult: (item: TaskItem) => void;
}

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    // 处理点击操作
    const handleClick = () => {
        onViewResult(item);
    };

    // 判断是否可以点击
    const isClickable = item.status === 'success' || item.status === 'fail';

    // 渲染状态覆盖层
    const renderStatusOverlay = () => {
        if (item.status === 'init') {
            return (
                <div className="task-item-status-overlay" style={{ opacity: 0.8 }}>
                    <div className="task-item-status-content" style={{ gap: 0 }}>
                        <div className="task-item-status-icon" style={{ height: 20 }}>
                            <svg width="20" height="20" viewBox="-5 -16 45 45">
                                <path
                                    d="M20 2 L24 12 L34 16 L24 20 L20 30 L16 20 L6 16 L16 12 Z"
                                    fill="#fff"
                                    opacity="0.9"
                                />
                                <path d="M7 0 L9 5 L14 7 L9 9 L7 14 L5 9 L0 7 L5 5 Z" fill="#fff" opacity="0.9" />
                            </svg>
                        </div>
                        <div className="task-item-status-text">进行中</div>
                    </div>
                </div>
            );
        } else if (item.status === 'fail') {
            return (
                <div className="task-item-status-overlay fail">
                    <div className="task-item-status-text">失败</div>
                </div>
            );
        }
        return null;
    };

    return (
        <div
            className={`task-list-item ${isClickable ? 'clickable' : ''} ${item.status === 'init' ? 'disabled' : ''}`}
            onClick={isClickable ? handleClick : undefined}
        >
            {/* 商家图片容器 */}
            <div className="task-item-image-container">
                <div className="task-item-image">
                    {/* 商家图片占位 */}
                    <div className="task-item-image-placeholder">
                        <img src={item.poiAvator} alt="商家图片" style={{ width: 44, height: 44, borderRadius: 4 }} />
                    </div>
                    {/* 状态覆盖层 */}
                    {renderStatusOverlay()}
                </div>
                {/* 红色角标 */}
                {/* {item.status !== 'init' ? <div className="task-item-badge"></div> : null} */}
            </div>

            {/* 商家信息 */}
            <div className="task-item-content">
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <div className="task-item-title">
                        <span className={`poi-name ${item.status === 'init' ? 'disabled' : ''}`}>{item.poiName}</span>
                    </div>
                    <div className={`task-item-id ${item.status === 'init' ? 'disabled' : ''}`}>ID：{item.poiId}</div>
                </div>
                <RightOutlined className="task-item-arrow" />
            </div>
        </div>
    );
};

export default TaskListItem;
