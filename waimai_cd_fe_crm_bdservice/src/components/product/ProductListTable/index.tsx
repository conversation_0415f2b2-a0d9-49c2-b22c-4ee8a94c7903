import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import {
    productDispatchMap,
    productQualifiedStatusMap,
    productSelectionMap,
    productTagMap,
    productTypeMap,
} from '@src/constants/product';
import { FormatedProductItem, ProductQualifiedStatus, ProductStatus } from '@src/types/product';
import { Badge, Button, Flex, FormInstance, Pagination, Popover, Table, Tag, Typography, message } from 'antd';
import { ColumnsType, TableProps } from 'antd/es/table';
import dayjs from 'dayjs';
import ProductListTableOperation from './ProductListTableOperation';
import { useState } from 'react';
import ProductListTableSelectionModal from './ProductListTableSelectionModal';
import { useRequest } from 'ahooks';
import { formatProductListFormData } from '@src/pages/product/list/utils';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { getUrlState } from '@src/hooks/useUrlState';
import { useUserContext } from '@src/components/User/UserProvider';

export interface ProductListTableProps {
    tableProps: TableProps<FormatedProductItem>;
    form: FormInstance;
    reload: () => void;
}

const query = getUrlState();

const ProductListTable = (props: ProductListTableProps) => {
    const { user } = useUserContext();

    const fisrtPopoverTaskId = (props.tableProps.dataSource || []).find(it => it.ablity.select)?.taskId;

    const [item, setItem] = useState<FormatedProductItem>();

    const onExport = async () => {
        if (!user) {
            return;
        }
        const res = await apiCaller.send('/product/task/exportTaskList', {
            operatorUid: user.id,
            ...formatProductListFormData(props.form.getFieldsValue()),
        });

        if (res.code !== 0) {
            return;
        }

        message.success(res.data);
    };

    const { run, loading } = useRequest(onExport, { manual: true });

    const highlightRow = (record: FormatedProductItem) => {
        if (record.taskId === +query.taskId) {
            return 'product-list__table__highlight-row';
        }
        return '';
    };

    const columns: ColumnsType<FormatedProductItem> = [
        {
            title: '任务ID',
            width: 80,
            dataIndex: ['taskId'],
        },
        {
            title: '任务名称',
            dataIndex: ['product', 'productName'],
            width: 206,
            render: (name, record) => {
                if (!record.product.productNameTag) {
                    return name;
                }

                return (
                    <>
                        {name}
                        <Tag style={{ marginLeft: 4 }} bordered={false} className="product-list-table__productNameTag">
                            {productTagMap.get(record.product.productNameTag)}
                            {record.product.excitation ? (
                                <Popover
                                    color="#222"
                                    content={
                                        <div style={{ maxWidth: 300, color: '#fff' }}>
                                            活动说明：
                                            <br />
                                            {record.product.excitation}
                                        </div>
                                    }
                                >
                                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                                </Popover>
                            ) : null}
                        </Tag>
                    </>
                );
            },
        },
        {
            title: '任务类型',
            width: 90,
            dataIndex: ['product', 'productTag'],
            render: productTag => productTagMap.get(productTag) || productTag,
        },
        {
            title: '下发类型',
            width: 90,
            dataIndex: ['product', 'productType'],
            render: productType => productTypeMap.get(productType) || productType,
        },
        {
            title: 'CM圈选',
            width: 90,
            dataIndex: ['selectionStatus'],
            render: selectionStatus => {
                const text = productSelectionMap.get(selectionStatus);
                if (!text) {
                    return <Typography.Text>-</Typography.Text>;
                }
                return (
                    <Badge
                        status={selectionStatus === ProductStatus.待圈选 ? 'warning' : 'default'}
                        color={selectionStatus === ProductStatus.待圈选 ? '#FF6A00' : undefined}
                        text={productSelectionMap.get(selectionStatus)}
                    />
                );
            },
        },
        {
            title: '是否下发',
            width: 90,
            dataIndex: ['dispatchStatus'],
            render: dispatchStatus => {
                return <Typography.Text>{productDispatchMap.get(dispatchStatus)}</Typography.Text>;
            },
        },
        {
            title: '领取量',
            width: 90,
            dataIndex: ['grantStat', 'receiveTotal'],
            align: 'right',
            render: (receiveTotal, r) => {
                if (r.selectionStatus === ProductStatus.待圈选) {
                    return <div style={{ textAlign: 'right' }}>{receiveTotal}</div>;
                }
                return (
                    <div style={{ textAlign: 'right' }}>
                        <Typography.Text>{receiveTotal}</Typography.Text>
                        <Typography.Text type="secondary">/{r.grantStat.total}</Typography.Text>
                    </div>
                );
            },
        },
        {
            title: '使用率/目标',
            dataIndex: 'usePercentageNum',
            width: 130,
            align: 'right',
            render: (usePercentageNum, r) => {
                const usePercentage = usePercentageNum != null ? `${usePercentageNum}%  ` : '-';

                return (
                    <div style={{ textAlign: 'right' }}>
                        <Typography.Text
                            type={r.qualifiedStatus === ProductQualifiedStatus.MEET ? 'success' : 'danger'}
                        >
                            {usePercentage}
                        </Typography.Text>
                        <Typography.Text type="secondary">/{r.customTargetPercent}%</Typography.Text>
                    </div>
                );
            },
        },
        {
            title: '是否达标',
            width: 90,
            dataIndex: 'qualifiedStatus',
            render: qualifiedStatus => (
                <Typography.Text type={qualifiedStatus === ProductQualifiedStatus.MEET ? 'success' : 'danger'}>
                    {productQualifiedStatusMap.get(qualifiedStatus)}
                </Typography.Text>
            ),
        },
        {
            title: '活动时间',
            dataIndex: ['product', 'validRange'],
            width: 200,
            render: range =>
                `${dayjs(range.startTime).format('YYYY-MM-DD')}~${dayjs(range.endTime).format('YYYY-MM-DD')}`,
        },
        {
            title: '创建时间',
            width: 120,
            dataIndex: 'createTime',
            render: creatTime => dayjs(creatTime).format('YYYY-MM-DD'),
        },

        {
            title: '操作',
            key: 'operation',
            fixed: 'right',
            width: 120,
            render: (_, r) => (
                <ProductListTableOperation
                    fisrtPopoverTaskId={fisrtPopoverTaskId}
                    item={r}
                    onClickSelection={() => setItem(r)}
                />
            ),
        },
    ];

    return (
        <div className="product-list-table">
            <Table
                {...props.tableProps}
                bordered
                pagination={false}
                columns={columns}
                scroll={{ x: 1000 }}
                rowKey={r => r.taskId}
                rowClassName={highlightRow}
            />
            <Flex style={{ margin: '16px 0' }} justify="space-between">
                <Button onClick={run} loading={loading} icon={<DownloadOutlined />}>
                    下载表格
                </Button>
                <Pagination
                    {...props.tableProps.pagination}
                    // @ts-ignore
                    onChange={(current, pageSize) => props.tableProps.onChange({ current, pageSize })}
                />
            </Flex>
            <ProductListTableSelectionModal
                key={item?.taskId}
                open={!!item}
                item={item}
                onCancel={() => setItem(undefined)}
                onConfirm={() => {
                    setItem(undefined);
                    props.reload();
                }}
            />
        </div>
    );
};

export default ProductListTable;
