import SvgGauge from '@src/components/SvgGauge';
import { Flex, Typography } from 'antd';

export interface ProductDetailOverViewBlockProps {
    useCount: number;
    total: number;
    useAmount: number;
    label: string;
    limit?: number;
}

const ProductDetailOverViewBlock = (props: ProductDetailOverViewBlockProps) => {
    const proportion = Math.min(props.useCount / props.total, 1);

    return (
        <Flex justify="space-bwteewn" align="strentch" className="product-detail__overview__block">
            <div className="product-detail__overview__block__chart">
                <SvgGauge
                    percent={proportion}
                    target={props.limit}
                    title={`${props.label}率`}
                    size={120}
                    degree={225}
                    strokeWidth={13}
                />
            </div>

            <Flex vertical justify="space-between">
                <div className="product-detail__overview__block__text">
                    <Typography.Text type="secondary">{props.label}量</Typography.Text>
                    <Typography.Text strong>
                        {props.useCount}/{props.total}
                    </Typography.Text>
                </div>
                <div className="product-detail__overview__block__text">
                    <Typography.Text type="secondary">{props.label}额</Typography.Text>
                    <Typography.Text strong>¥{props.useAmount}</Typography.Text>
                </div>
            </Flex>
        </Flex>
    );
};

export default ProductDetailOverViewBlock;
