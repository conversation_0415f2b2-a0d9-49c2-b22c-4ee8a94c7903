import { DownloadOutlined } from '@ant-design/icons';
import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import { productGrantStatusMap } from '@src/constants/product';
import { ProductGrantStatus } from '@src/types/product';
import { useAntdTable, useRequest } from 'ahooks';
import { Form, Typography, Table, Flex, Button, Pagination, message } from 'antd';
import { ColumnsType } from 'antd/es/table';

type ListRes = APISpec['/product/grant/searchGrantProductList']['response'];

export interface ProductDetailTableProps {
    productId: number;
    productType: number;
    taskId: number;
}

interface Result {
    total: number;
    list: ListRes['row'];
}

const getTableData = async ({ current, pageSize }, props): Promise<Result> => {
    const res = await apiCaller.send('/product/grant/searchGrantProductList', {
        pageNum: current,
        pageSize,
        productId: props.productId,
        productType: props.productType,
        containStatusList: [ProductGrantStatus.已领取],
        valid: 1,
    });

    if (res.code !== 0) {
        return {
            total: 0,
            list: [],
        };
    }

    return {
        total: res.data.total,
        list: res.data.row,
    };
};
const ProductDetailTable = (props: ProductDetailTableProps) => {
    const [form] = Form.useForm();

    const { tableProps } = useAntdTable(pagination => getTableData(pagination, props), {
        defaultPageSize: 10,
        form,
        refreshDeps: [props.productId],
    });

    const onExport = async () => {
        const res = await apiCaller.send('/product/task/exportTaskDetail', {
            productId: props.productId,
            productType: props.productType,
            taskId: props.taskId,
            containStatusList: [ProductGrantStatus.已领取],
        });

        if (res.code !== 0) {
            return;
        }

        message.success(res.msg);
    };

    const { run, loading } = useRequest(onExport, { manual: true });

    const columns: ColumnsType<ListRes['row'][number]> = [
        {
            title: '商家ID',
            width: 90,
            dataIndex: ['grantTargetId'],
        },
        {
            title: '商家名称',
            width: 190,
            dataIndex: ['grantTargetName'],
        },
        {
            title: 'BD组织架构',
            width: 120,
            dataIndex: ['orgPath'],
        },
        {
            title: 'BD',
            width: 90,
            dataIndex: ['operator'],
            render: operator => `${operator.name}（${operator.mis}）`,
        },
        {
            title: '店铺上线时间',
            width: 120,
            dataIndex: ['onlineTime'],
        },
        {
            title: '领券时间',
            width: 90,
            dataIndex: ['receiveTime'],
        },
        {
            title: '使用状态',
            width: 90,
            dataIndex: ['status'],
            render: s => productGrantStatusMap.get(s) || '未知',
        },
        {
            title: '折扣比例',
            width: 90,
            dataIndex: ['discountPercent'],
            render: (discountPercent = 0) => `${discountPercent}%`,
        },
        {
            title: '最高抵扣额',
            width: 110,
            dataIndex: ['receiveAmount'],
            render: (receiveAmount = 0) => `¥${receiveAmount}`,
        },
        {
            title: '使用金额',
            width: 90,
            dataIndex: ['useAmount'],
            render: (useAmount = 0) => `¥${useAmount}`,
        },
        {
            title: '剩余抵扣额',
            width: 90,
            dataIndex: ['remainUseAmount'],
            render: (remainUseAmount = 0) => `¥${remainUseAmount}`,
        },
        {
            title: '使用有效期',
            width: 90,
            dataIndex: ['useValidEndTime'],
        },
    ];

    return (
        <div>
            <Typography.Title level={5}>领取资源商家明细</Typography.Title>

            <Table
                {...tableProps}
                bordered
                pagination={false}
                columns={columns}
                scroll={{ x: 1000 }}
                rowKey={r => r.grantTargetId}
            />
            <Flex style={{ margin: '16px 0' }} justify="space-between">
                <Button onClick={run} loading={loading} icon={<DownloadOutlined />}>
                    下载表格
                </Button>
                <Pagination
                    {...tableProps.pagination}
                    onChange={(current, pageSize) => tableProps.onChange({ current, pageSize })}
                />
            </Flex>
        </div>
    );
};

export default ProductDetailTable;
