import { FormatedProductItem, ProductQualifiedStatus } from '@src/types/product';
import './style.scss';
import { Divider, Space, Tag, Typography } from 'antd';
import { productQualifiedStatusMap } from '@src/constants/product';
import ProductDetailOverViewBlock from './ProductDetailOverViewBlock';

const { Title, Text } = Typography;

export interface ProductDetailOverviewProps {
    data: FormatedProductItem;
}

const ProductDetailOverview = (props: ProductDetailOverviewProps) => {
    const { data } = props;
    const { grantStat, product, qualifiedStatus, usePercentageNum, customTargetPercent } = data;
    const { targetPercent } = product;

    const p = usePercentageNum != null ? `${usePercentageNum}%` : '-';
    return (
        <div className="product-detail__overview">
            <div className="product-detail__overview__usage">
                <Title className="product-detail__overview__use-percent" level={1}>
                    {p}
                </Title>
                <div>
                    <div style={{ marginBottom: 20 }}>
                        <span className="product-detail__overview__subtitle">当前使用率情况</span>
                        <Tag
                            bordered={false}
                            color={qualifiedStatus === ProductQualifiedStatus.MEET ? 'success' : 'error'}
                        >
                            {productQualifiedStatusMap.get(qualifiedStatus)}
                        </Tag>
                    </div>

                    <Space split={<Divider type="vertical" />}>
                        <Space>
                            <Text type="secondary">总部目标</Text>
                            <Text strong>{targetPercent}%</Text>
                        </Space>
                        <Space>
                            <Text type="secondary">城市目标</Text>
                            <Text strong>{customTargetPercent}%</Text>
                        </Space>
                    </Space>
                </div>
            </div>

            <Space size="middle">
                <div className="product-detail__overview__left">
                    <Title level={5}>领取情况</Title>
                    <ProductDetailOverViewBlock
                        label="领取"
                        useCount={grantStat.receiveTotal}
                        total={grantStat.total}
                        useAmount={grantStat.receiveAmount}
                    />
                </div>
                <div className="product-detail__overview__left">
                    <Title level={5}>使用情况</Title>
                    <ProductDetailOverViewBlock
                        label="使用"
                        useCount={grantStat.useTotal}
                        total={grantStat.total}
                        useAmount={grantStat.useAmount}
                        limit={customTargetPercent / 100}
                    />
                </div>
            </Space>
        </div>
    );
};

export default ProductDetailOverview;
