import { APISpec } from '@mfe/cc-api-caller-pc';
import Title from '../Title';
import { Bar, BarConfig } from '@ant-design/plots';

type ServiceChart = APISpec['/impc/service/getServicePoints']['response']['marketData'];

interface EvaluationServiceChart {
    data?: ServiceChart;
}

const EvaluationServiceChart = (props: EvaluationServiceChart) => {
    const { data } = props;

    if (!data) {
        return null;
    }

    const chartData = data.map(v => ({ ...v, score: +v.score }));

    // DELETE ME: DEBUG CODE
    const map = chartData.reduce((m, item) => {
        m.set(item.orgName, item.score);
        return m;
    }, new Map());

    const config: BarConfig = {
        // data: chartData,
        data: [...map].map(([orgName, score]) => ({ orgName, score })),
        xField: 'score',
        yField: 'orgName',
        yAxis: {
            line: { style: { stroke: '#222' } },
            top: true,
        },
        xAxis: {
            label: { formatter: v => `${v}%` },
        },

        /** 自定义颜色 */
        color: v => (v.orgName !== '外卖总部' ? '#f7bb2d' : '#fe9975'),
        marginRatio: 0,
        // intervalPadding: 20,
        barWidthRatio: 0.3,
        label: {
            // 可手动配置 label 数据标签位置
            // position: 'middle',
            // 'left', 'middle', 'right'
            // 可配置附加的布局方法
            formatter: v => `${v.score}%`,
            layout: [
                // 柱形图数据标签位置自动调整
                {
                    type: 'interval-adjust-position',
                }, // 数据标签防遮挡
                {
                    type: 'interval-hide-overlap',
                }, // 数据标签文颜色自动调整
                {
                    type: 'adjust-color',
                },
            ],
        },
        tooltip: {
            formatter: datum => {
                return { name: datum.orgName, value: datum.score + '%' };
            },
        },
    };

    return (
        <>
            <Title>大盘服务分得分率数据</Title>
            <div className="evaluation-block__chart">
                <Bar {...config} />
            </div>
        </>
    );
};

export default EvaluationServiceChart;
