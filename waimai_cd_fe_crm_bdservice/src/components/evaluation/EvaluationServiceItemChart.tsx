import { APISpec } from '@mfe/cc-api-caller-pc';
import Title from '../Title';
import { Bar, BarConfig } from '@ant-design/plots';

type ServiceItemChart = APISpec['/impc/service/getServicePoints']['response']['serviceItem'];

interface EvaluationServiceItemChart {
    data?: ServiceItemChart;
}

const enum DataType {
    SELF = '本团队数据',
    AVERAGE = '上一级组织节点平均值',
    SELF_SPEC = '本团队数据（服务得分率）',
}

const LabelDescMap = new Map([
    ['服务分得分率', '数据口径：每个模块的服务量*每个模块BD实际得分）/（每个模块的服务量*每个模块最高满分）'],
    [
        '服务及时度得分率',
        '数据口径：BD在信鸽工单模块产生的服务量*BD在信鸽工单响应模块实际得分）/（BD在信鸽工单模块产生的服务量*信鸽工单响应模块最高满分）',
    ],
    [
        '服务态度得分率',
        '数据口径：BD在AI质检模块产生的服务量*BD在AI质检模块实际得分）/（BD在AI质检模块产生的服务量*AI质检模块最高满分）',
    ],
    ['服务专业度得分率', '数据口径：BD当月天机考试实际答题总分/当月天机考试应答题总分'],
    [
        '服务违规投诉率',
        '数据口径：（BD被商家投诉态度违规量+ 违规处理工单数 + BD被商家投诉虚假拜访成立量）/BD负责的商家数（非下线）',
    ],
    [
        '服务满意度得分率',
        '数据口径：BD在工单评价、拜访评价模块产生的服务量*BD在工单评价、拜访评价模块实际得分）/（BD在工单评价、拜访评价模块产生的服务量*工单评价、拜访评价模块最高满分）',
    ],
]);

const EvaluationServiceItemChart = (props: EvaluationServiceItemChart) => {
    const { data } = props;

    if (!data) {
        return null;
    }

    let chartData = [
        {
            label: '服务分得分率',
            type: DataType.SELF_SPEC,
            value: data.serviceScoreRate,
            contrast: data.serviceScoreRate / data.averageServiceScoreRate,
            desc: '服务分得分率\n\n' + LabelDescMap.get('服务分得分率'),
        },
        {
            label: '服务及时度得分率',
            type: DataType.SELF,
            value: data.timelinessScoreRate,
            contrast: data.timelinessScoreRate / data.averageTimelinessScoreRate,
            desc: '服务及时度得分率\n\n' + LabelDescMap.get('服务及时度得分率'),
        },
        {
            label: '服务分得分率',
            type: DataType.AVERAGE,
            value: data.averageServiceScoreRate,
            desc: '服务分得分率\n\n' + LabelDescMap.get('服务分得分率'),
        },
        {
            label: '服务及时度得分率',
            type: DataType.AVERAGE,
            value: data.averageTimelinessScoreRate,
            desc: '服务及时度得分率\n\n' + LabelDescMap.get('服务及时度得分率'),
        },
        {
            label: '服务态度得分率',
            type: DataType.SELF,
            value: data.attitudeScoreRate,
            contrast: data.attitudeScoreRate / data.averageAttitudeScoreRate,
            desc: '服务态度得分率\n\n' + LabelDescMap.get('服务态度得分率'),
        },
        {
            label: '服务态度得分率',
            type: DataType.AVERAGE,
            value: data.averageAttitudeScoreRate,
            desc: '服务态度得分率\n\n' + LabelDescMap.get('服务态度得分率'),
        },
        {
            label: '服务专业度得分率',
            type: DataType.SELF,
            value: data.majorScoreRate,
            contrast: data.majorScoreRate / data.averageMajorScoreRate,
            desc: '服务专业度得分率\n\n' + LabelDescMap.get('服务专业度得分率'),
        },
        {
            label: '服务专业度得分率',
            type: DataType.AVERAGE,
            value: data.averageMajorScoreRate,
            desc: '服务专业度得分率\n\n' + LabelDescMap.get('服务专业度得分率'),
        },
        {
            label: '服务违规投诉率',
            type: DataType.SELF,
            value: data.complaintRate,
            contrast: data.complaintRate / data.averageComplaintRate,
            desc: '服务违规投诉率\n\n' + LabelDescMap.get('服务违规投诉率'),
        },
        {
            label: '服务违规投诉率',
            type: DataType.AVERAGE,
            value: data.averageComplaintRate,
            desc: '服务违规投诉率\n\n' + LabelDescMap.get('服务违规投诉率'),
        },
    ];

    // 服务分2.0去除了满意度得分
    if (data.satisfySocreRate !== undefined) {
        chartData = [
            ...chartData,
            {
                label: '服务满意度得分率',
                type: DataType.SELF,
                value: data.satisfySocreRate,
                contrast: data.satisfySocreRate / data.averageSatisfySocreRate,
                desc: '服务满意度得分率\n\n' + LabelDescMap.get('服务满意度得分率'),
            },
            {
                label: '服务满意度得分率',
                type: DataType.AVERAGE,
                value: data.averageSatisfySocreRate,
                desc: '服务满意度得分率\n\n' + LabelDescMap.get('服务满意度得分率'),
            },
        ];
    }

    const dataSource = chartData.filter(v => v.value != null);
    const showGroup = dataSource.length === chartData.length;

    const config: BarConfig = {
        data: dataSource,
        isGroup: showGroup,
        seriesField: showGroup ? 'type' : undefined,
        xField: 'value',
        yField: 'label',
        yAxis: {
            line: { style: { stroke: '#222' } },
            label: {
                style: {
                    fontSize: 14,
                    fill: '#666',
                },
            },
            top: true,
        },
        xAxis: {
            label: {
                formatter: v => `${v}%`,
            },
        },

        /** 自定义颜色 */
        color: ({ type }) => {
            if (!showGroup) {
                return '#f7bb2d';
            }

            switch (type) {
                case DataType.SELF:
                    return '#f7bb2d';
                case DataType.SELF_SPEC:
                    return '#fe9975';
                default:
                    return '#e5ebff';
            }
        },
        intervalPadding: 20,
        marginRatio: 0,
        barWidthRatio: 1,
        // appendPadding: 20,

        renderer: 'svg',
        label: {
            // 可手动配置 label 数据标签位置
            position: 'middle',
            formatter: v => {
                console.log({ v });
                return `${v.value}%`;
            },
            // 'left', 'middle', 'right'
            // 可配置附加的布局方法
            layout: [
                // 柱形图数据标签位置自动调整
                {
                    type: 'interval-adjust-position',
                }, // 数据标签防遮挡
                {
                    type: 'interval-hide-overlap',
                }, // 数据标签文颜色自动调整
                {
                    type: 'adjust-color',
                },
            ],
        },
        legend: {
            layout: 'horizontal',
            position: 'bottom-left',
            offsetY: 10,
        },

        interactions: [{ type: 'legend-filter', enable: false }],
        /* eslint-disable @typescript-eslint/indent */
        annotations: showGroup
            ? chartData
                  .filter(one => one.type !== DataType.AVERAGE)
                  .map(item => ({
                      type: 'html',
                      html: () => {
                          const fraction = (item.contrast || 1) - 1;
                          const text = `${(fraction * 100).toFixed(2)}%`;
                          if (fraction === 0 || fraction === Infinity) {
                              return;
                          }
                          const literal = fraction > 0 ? '高' : '低';
                          return `<div class="annotation ${
                              fraction > 0 ? 'annotation__higher' : 'annotation__lower'
                          }">比同区平均水平${literal}${text}</div>`;
                      },
                      position: (xScale, yScale) => [
                          `${
                              // @ts-ignore
                              yScale.value.scale(Math.max(item.value, 0)) * // Math.max(0, item.value)确保不做负位移
                                  100 +
                              1
                          }%`,
                          // @ts-ignore
                          `${xScale.scale(item.label) * 100 - 4.5}%`,
                      ],
                  }))
            : undefined,
        /* eslint-enable @typescript-eslint/indent */
        tooltip: {
            formatter: datum => {
                return {
                    name: datum.type || datum.label,
                    value: datum.value + '%',
                };
            },
            title: 'desc',
        },
    };

    return (
        <div className="evaluation-block" style={{ marginBottom: 80 }}>
            <Title>按服务项分布</Title>
            <div className="evaluation-block__chart">
                <Bar {...config} />
            </div>
        </div>
    );
};

export default EvaluationServiceItemChart;
