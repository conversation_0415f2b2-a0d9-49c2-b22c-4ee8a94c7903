import { APISpec } from '@mfe/cc-api-caller-pc';
import Title from '../Title';
import { Popover, Statistic, Flex } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useContext } from 'react';
import VersionContext from '@src/pages/evaluation/context/version';

type StatisticsData = APISpec['/impc/service/getServicePoints']['response']['dataOverview'];

interface EvaluationStatistics {
    data?: StatisticsData;
}

const EvaluationStatistics = (props: EvaluationStatistics) => {
    const { data } = props;
    const { version } = useContext(VersionContext);

    if (!data) {
        return null;
    }

    return (
        <div className="evaluation-block">
            <Title>数据概览</Title>
            <Flex className="evaluation-block__stats" wrap="wrap" gap="middle">
                <div className="evaluation-block__stats__item">
                    <Statistic
                        title={
                            <Popover content="每个模块的服务量*每个模块BD实际得分）/（每个模块的服务量*每个模块最高满分）">
                                服务得分率
                                <QuestionCircleOutlined className="evaluation-block__icon" />
                            </Popover>
                        }
                        value={data.scoreRate}
                        suffix="%"
                    />
                    <div className="evaluation-block__stats__item__sub">
                        排名第{data.rank}位（{data.rank}/{data.total}）
                        <Popover content="服务得分率同一节点下的排名（如：大区之间相比、区域之间相比）">
                            <QuestionCircleOutlined className="evaluation-block__icon" />
                        </Popover>
                    </div>
                </div>

                <Statistic
                    title={
                        <Popover content="BD在每个模块下的的服务量 * 每个模块最高满分">
                            服务分满分
                            <QuestionCircleOutlined className="evaluation-block__icon" />
                        </Popover>
                    }
                    value={data.fullMarks}
                    suffix="分"
                    className="evaluation-block__stats__item"
                />
                <Statistic
                    title={
                        <Popover content="BD在每个模块下的的服务量 * BD在每个模块下动作实际得分">
                            服务分实际得分
                            <QuestionCircleOutlined className="evaluation-block__icon" />
                        </Popover>
                    }
                    value={data.actualScore}
                    suffix="分"
                    className="evaluation-block__stats__item"
                />
                <Statistic
                    title={
                        <Popover
                            content={`BD在每个模块下的实际服务量（如：工单响应算1次、工单结案算1次、工单${
                                version === '1' ? '评价' : '质检'
                            }算1次）`}
                        >
                            服务量
                            <QuestionCircleOutlined className="evaluation-block__icon" />
                        </Popover>
                    }
                    value={data.serviceNum}
                    className="evaluation-block__stats__item"
                    suffix="个"
                />
            </Flex>
        </div>
    );
};

export default EvaluationStatistics;
