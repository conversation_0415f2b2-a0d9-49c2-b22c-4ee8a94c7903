import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { objectToQuery } from '@src/utils/query';
import { Button, Tag } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';

interface EvaluationDetailField {
    misId?: string;
    beginTime?: number;
    endTime?: number;
    orgId?: number;
    field: string;
    value: any;
    onClick?: () => void;
}

const enum DetailLink {
    DOVE = '/page/dove/callRecord',
    BIZ = '/page/dove/biz/poiBizList',
}

const propCheckableMap = new Map<string, [string, Record<string, any>?]>([
    ['effectiveCallRate', [DetailLink.DOVE]],
    ['qcPassRate', [DetailLink.DOVE, { qualifiedResult: 0 }]],
    ['bizResponseIn1hRate', [DetailLink.BIZ]],
    ['bizResponseIn2hRate', [DetailLink.BIZ]],
    ['bizQcNum', [DetailLink.BIZ]],
    ['bizFinishIn24hRate', [DetailLink.BIZ]],
]);

const VisitDetail = ({ data }) => {
    const ref = useRef<any>();
    const [ml, setMl] = useState<number>();
    useEffect(() => {
        if (!ref.current) {
            return;
        }
        const bro = ref.current.parentNode.parentNode.children[0];

        // 线上初始化时宽度为0，所以添加ResizeObserver设置margin
        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                setMl(-1 * entry.contentRect.width);
            }
        });
        resizeObserver.observe(bro);

        // 设置margin
        const { width } = bro.getBoundingClientRect();
        setMl(-1 * width);

        return () => resizeObserver.disconnect();
    }, [ref.current]);
    return (
        <div style={{ display: 'flex', flexDirection: 'column' }} ref={ref}>
            <span>{data.count}</span>
            <div style={{ marginLeft: ml || -90 }}>
                {data.subOptions.map(v => (
                    <Tag color={'pink'} style={{ minWidth: 135, marginTop: 5 }} key={v}>
                        {v}
                    </Tag>
                ))}
            </div>
        </div>
    );
};
const EvaluationDetailField = (props: EvaluationDetailField) => {
    if (!propCheckableMap.has(props.field)) {
        return <span>{props.value || '-'}</span>;
    }

    const { misId, beginTime, endTime: queryEndTime, orgId } = props;

    const startTime = beginTime && dayjs.unix(+beginTime).format('YYYY-MM-DD');

    const endTime = queryEndTime && dayjs.unix(+queryEndTime).format('YYYY-MM-DD');

    const [path, query] = propCheckableMap.get(props.field) || [];

    return (
        <Button
            type="link"
            size="small"
            target="_blank"
            href={bellwetherLinkParse(
                `${path}${objectToQuery({
                    misId,
                    orgId,
                    startTime,
                    endTime,
                    ...query,
                })}`,
            )}
            onClick={props.onClick}
        >
            {props.value}
        </Button>
    );
};

export default EvaluationDetailField;
