import './style.scss';

export interface SvgGaugeProps {
    size?: number; // 直径
    percent: number; // eg: 0.7
    strokeWidth?: number;
    target?: number; // eg: 0.9 意味目标值
    backgroundColor?: string;
    tintColor?: string;
    targetColor?: string;
    degree?: number; // 0 - 360, 仪表盘的弧线角度，一般是270
    title?: string;
}

// 在半径为r上的圆有一点(x1, y1), 求解逆时针旋转a度后的坐标;
const rotatePoint = (x1, y1, r, a) => {
    const angle = (a * Math.PI) / 180; // 将角度转换为弧度
    const x2 = (x1 - r) * Math.cos(angle) - (y1 - r) * Math.sin(angle) + r;
    const y2 = (x1 - r) * Math.sin(angle) + (y1 - r) * Math.cos(angle) + r;

    return { x: x2, y: y2 };
};

const SvgGauge = (props: SvgGaugeProps) => {
    const {
        size = 100,
        strokeWidth = 10,
        percent = 0,
        target = 0,
        backgroundColor = '#e9eaf2',
        tintColor = '#FFDD00',
        targetColor = '#FF6A00',
        degree = 270,
    } = props;
    const r = size / 2;

    // 圆顶点
    const topPoint = { x: r, y: 2 * r };

    const startDegree = 180 - degree / 2;
    // 从顶点逆时针旋转startDegree度，得到起点
    const startPoint = rotatePoint(topPoint.x, topPoint.y, r, startDegree);
    // 从顶点逆时针旋转startDegree + degree度，得到起点
    const endPoint = rotatePoint(topPoint.x, topPoint.y, r, startDegree + degree);

    const { x, y } = rotatePoint(startPoint.x, startPoint.y, r, degree * percent);

    const targetStart = rotatePoint(startPoint.x, startPoint.y, r, degree * target);
    const targetEnd = rotatePoint(startPoint.x, startPoint.y, r, degree * (target === 0 ? 0 : target + 0.01));

    const offset = strokeWidth / 2;

    // 弧度标志位, 1大弧， 0小弧，如果角度超过180就是大弧，小于180的用小弧
    const bgSweepFlag = degree > 180 ? 1 : 0;
    const valueSweepFlag = degree * percent > 180 ? 1 : 0;

    const percentNum = (percent * 100).toFixed(2);

    const [integer, decimal] = percentNum.split('.').map(Number);

    const decimalText = decimal ? `.${decimal}%` : '%';

    return (
        <div className="svg-gauge">
            <svg
                width={size + strokeWidth}
                height={r + r * Math.cos((startDegree * Math.PI) / 180) + strokeWidth + strokeWidth}
            >
                <path
                    d={`M ${startPoint.x + offset} ${startPoint.y + offset} A ${r} ${r} 0 ${bgSweepFlag} 1 ${
                        endPoint.x + offset
                    } ${endPoint.y + offset}`}
                    fill="none"
                    stroke={backgroundColor}
                    strokeWidth={strokeWidth}
                />
                <path
                    d={`M ${startPoint.x + offset} ${startPoint.y + offset} A ${r} ${r} 0 ${valueSweepFlag} 1 ${
                        x + offset
                    } ${y + offset}`}
                    fill="none"
                    stroke={tintColor}
                    strokeWidth={strokeWidth}
                />
                <path
                    d={`M ${targetStart.x + offset} ${targetStart.y + offset} A ${r} ${r} 0 0 1 ${
                        targetEnd.x + offset
                    } ${targetEnd.y + offset}`}
                    fill="none"
                    stroke={targetColor}
                    strokeWidth={strokeWidth}
                />
            </svg>
            {props.title ? (
                <div className="svg-gauge__text-content">
                    <div className="svg-gauge__title">{props.title}</div>
                    {isNaN(percent) ? (
                        <span className="svg-gauge__numeric__text">-</span>
                    ) : (
                        <div className="svg-gauge__numeric">
                            <span className="svg-gauge__numeric__text">{integer}</span>
                            <span className="svg-gauge__numeric__percent">{decimalText}</span>
                        </div>
                    )}
                </div>
            ) : null}
        </div>
    );
};

export default SvgGauge;
