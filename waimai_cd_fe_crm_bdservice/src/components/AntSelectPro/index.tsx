import { Select, SelectProps } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import React, { useState, forwardRef } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { debounce } from 'lodash';
import type { APISpec } from '@mfe/cc-api-caller-pc';

interface OptionType extends DefaultOptionType {
    value: string | number;
    label: string;
    [key: string]: any;
}

export interface AntSelectProProps<T = any> extends Omit<SelectProps<T>, 'options'> {
    /** Select的选项数据 */
    options?: OptionType[];
    /** 是否返回完整的选项信息 */
    withOriginal?: boolean;
    /** 搜索接口地址 */
    searchUrl?: keyof APISpec;
    /** 搜索接口额外参数 */
    searchParams?: Record<string, any>;
    /** 自定义选项转换函数 */
    transformOptions?: (data: any) => OptionType[];
    /** 值改变时的回调 */
    onChange?: (value: T) => void;
    /** 搜索字段key-传给后端的字段，默认为keyword */
    searchKey?: string;
    /** 请求方法 */
    requestMethod?: 'get' | 'send';
    /** 失去焦点时是否清空选项 */
    clearOptionsOnBlur?: boolean;
    ref?: any;
}

const AntSelectPro: React.FC<AntSelectProProps> = forwardRef(
    (
        {
            options: propOptions = [],
            onChange,
            withOriginal = false,
            searchUrl,
            searchParams = {},
            transformOptions,
            searchKey = 'keyword',
            requestMethod = 'send',
            clearOptionsOnBlur = false,
            ...restProps
        },
        ref,
    ) => {
        const [loading, setLoading] = useState(false);
        const [options, setOptions] = useState<OptionType[]>(propOptions);

        const initOptions = (search: OptionType[] = []) => {
            const { label = 'label', value = 'value' } = restProps.fieldNames || { label: 'label', value: 'value' };
            let propsOption: OptionType[] = [];
            if (restProps.value) {
                if (Array.isArray(restProps.value)) {
                    propsOption = restProps.value.map(item => ({
                        ...item,
                        [label]: item['label'],
                        [value]: item['value'],
                    }));
                } else {
                    propsOption = [restProps.value];
                }
            }

            // 合并并去重,使用value作为唯一标识
            const options = restProps?.showSearch ? search : [...propsOption, ...search];
            const mergedOptions = options.reduce((acc: OptionType[], curr: OptionType) => {
                if (!acc.find(item => item[value] === curr[value])) {
                    const newItem = { ...curr, value: curr[value] || curr.value, label: curr[label] || curr.label };
                    acc.push(newItem);
                }
                return acc;
            }, []);
            return mergedOptions;
        };

        // 处理onChange事件
        const handleChange = (value: any, option: any) => {
            if (!onChange) return;
            if (withOriginal) {
                onChange(option);
            } else {
                onChange(value);
            }
        };

        // 处理搜索
        const handleSearch = debounce(async (searchText: string) => {
            if (!searchUrl) {
                return;
            }

            setLoading(true);
            try {
                const res = await apiCaller[requestMethod](searchUrl, {
                    ...searchParams,
                    [searchKey]: searchText.trim(),
                });

                if (res.code !== 0) {
                    return;
                }

                // 处理返回数据
                let dataList: OptionType[] = [];
                if (transformOptions) {
                    // 使用自定义转换函数
                    dataList = transformOptions(res.data);
                } else if (Array.isArray(res.data)) {
                    // 如果返回数据是数组,则需要满足fieldNames的配置，默认使用返回数据的label和value
                    dataList = res.data;
                } else {
                    dataList = [];
                }

                setOptions(dataList);
            } catch (error) {
                console.error('搜索失败:', error);
            } finally {
                setLoading(false);
            }
        }, 300);

        // 如果有searchUrl，则启用搜索功能
        const selectProps = searchUrl
            ? {
                  showSearch: true,
                  filterOption: false,
                  onSearch: handleSearch,
                  loading,
              }
            : {};
        return (
            <Select<OptionType>
                ref={ref as any}
                {...selectProps}
                {...restProps}
                onBlur={e => {
                    restProps.onBlur && restProps.onBlur(e);
                    clearOptionsOnBlur && setOptions([]);
                }}
                options={initOptions(options)}
                onChange={handleChange}
            />
        );
    },
);

export default AntSelectPro;
