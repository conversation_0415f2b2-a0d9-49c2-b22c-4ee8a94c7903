import { PropsWithChildren, Children } from 'react';
import _ from 'lodash';
import { Col, Row, RowProps } from 'antd';

interface Grid {
    gutter?: RowProps['gutter'];
    divide?: number;
    limit?: number;
    className?: string;
    offset?: number;
}

const AdaptiveGrid = (props: PropsWithChildren<Grid>) => {
    const { gutter = 16, divide = 4, offset = 0, limit, children } = props;

    const els = Children.toArray(children).slice(0, limit);
    const groups = _.chunk(els, divide);
    const span = Math.floor(24 / divide);

    return (
        <>
            {groups.map((g, index) => (
                <Row
                    gutter={gutter}
                    justify="space-between"
                    align="middle"
                    style={{ marginBottom: 12 }}
                    key={index}
                    className={props.className}
                >
                    {g.map((node, i) => (
                        <Col key={i} span={span - offset}>
                            {node}
                        </Col>
                    ))}
                    {Array.from({ length: divide - g.length }).map((_, i) => (
                        <Col key={i} span={span - offset} />
                    ))}
                </Row>
            ))}
        </>
    );
};

export default AdaptiveGrid;
