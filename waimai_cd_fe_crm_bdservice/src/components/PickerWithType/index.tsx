import { DatePicker, Select, Space } from 'antd';
import { Dayjs } from 'dayjs';
import { useEffect } from 'react';

type Unit = 'year' | 'month' | 'date';

export interface PickerWithTypeValue {
    type: Unit;
    value: Dayjs;
}

export interface PickerWithTypeProps {
    value?: PickerWithTypeValue;
    onChange?: (v: Partial<PickerWithTypeValue>) => void;
}

const options = [
    {
        label: '年',
        value: 'year',
    },
    {
        label: '月',
        value: 'month',
    },
    {
        label: '日',
        value: 'date',
    },
];

const PickerWithType = (props: PickerWithTypeProps) => {
    const onFieldChange = (p: Partial<PickerWithTypeValue>) => {
        props.onChange?.({ ...props.value, ...p });
    };

    useEffect(() => {
        if (props.value?.type) {
            return;
        }
        props.onChange?.({ ...props.value, type: 'date' });
    }, []);

    return (
        <Space.Compact style={{ width: '100%' }}>
            <Select
                style={{ flexBasis: 60 }}
                value={props.value?.type || 'date'}
                options={options}
                onChange={type => onFieldChange({ type })}
            />
            <DatePicker
                style={{ flex: 1 }}
                picker={props.value?.type}
                value={props.value?.value}
                onChange={value => onFieldChange({ value })}
            />
        </Space.Compact>
    );
};

export default PickerWithType;
