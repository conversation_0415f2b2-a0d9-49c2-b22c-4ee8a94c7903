import { CSSProperties, PropsWithChildren } from 'react';
import style from './style.module.scss';
import { Typography } from 'antd';
import { TitleProps } from 'antd/es/typography/Title';

const Title = (
    props: PropsWithChildren<{
        level?: TitleProps['level'];
        style?: CSSProperties;
    }>,
) => (
    <div className={style.title} style={props.style}>
        {props.level ? (
            <Typography.Title level={props.level}>
                <span className={style.prefix}></span>
                {props.children}
            </Typography.Title>
        ) : (
            props.children
        )}
    </div>
);

export default Title;
