import createUpload from '@ai/mss-upload-js';
import { Upload, UploadProps } from 'antd';
import getS3Host, { Region } from '@src/utils/getS3Host';

interface S3Upload {
    sigUrl?: string;
    draggable?: boolean;
    bucket?: string;
    region?: Region;
    s3UploadProps?: Partial<Parameters<typeof createUpload>['0']>;
}

const S3Upload = (props: UploadProps & S3Upload) => {
    const { sigUrl, bucket = 'yc-as-bucket', region = Region.BEI_JING, ...rest } = props;

    const customRequest = ({ file, onError, onProgress, onSuccess }) => {
        const uploadInstance = createUpload(
            {
                onFileInfo: () => {},
                signatureUrl: sigUrl || `${import.meta.env.VITE_API_PREFIX}/livescript/sku/upload/signature`, // 加签接口API
                bucket,
                prefix_type: 's3_style',
                // tenant_id: "mss_fad1a48f61909e8451b8172ba5abfd908e5",  // prefix_type为s3_style，tenant_id不需要
                file,
                hashMode: true,
                s3_host: getS3Host(region),
                onProgress(percent) {
                    onProgress({ percent }, file);
                },
                onStart() {},

                onSuccess(fileUrl) {
                    onSuccess(fileUrl);
                },
                onError(e) {
                    onError(e);
                },
                onFinish() {},
                validateFile: () => true,
            },
            1,
        );

        uploadInstance.upload();

        return {
            abort() {
                console.log('upload progress is aborted.');
            },
        };
    };

    const Comp = props.draggable ? Upload.Dragger : Upload;
    return <Comp {...rest} customRequest={customRequest as any} />;
};

export default S3Upload;
