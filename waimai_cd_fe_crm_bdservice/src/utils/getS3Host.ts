import { Environment, getEnvironment } from '@mfe/cc-ocrm-utils';

export const enum Region {
    BEI_JING,
    BEI_JING_02,
}

const S3_HOST = new Map([
    [
        Region.BEI_JING,
        new Map([
            [Environment.DEV, 'msstest.vip.sankuai.com'],
            [Environment.TEST, 'msstest.vip.sankuai.com'],
            [Environment.ST, 'mss.vip.sankuai.com'],
            [Environment.PRORD, 's3plus.meituan.net'],
        ]),
    ],
    [
        Region.BEI_JING_02,
        new Map([
            [Environment.DEV, 'msstest.vip.sankuai.com'],
            [Environment.TEST, 'msstest.vip.sankuai.com'],
            [Environment.ST, 's3plus-bj02.vip.sankuai.com'],
            [Environment.PRORD, 's3plus-bj02.sankuai.com'],
        ]),
    ],
]);
const getS3Host = (region = Region.BEI_JING, forceProd = false) => {
    const regionConfig = S3_HOST.get(region);

    if (!regionConfig) {
        return '';
    }

    // ST图片外网大模型无法访问，所以需要支持强制使用prod环境
    let env: any = getEnvironment();
    if (forceProd && env === Environment.ST) {
        env = Environment.PRORD;
    }
    return regionConfig.get(env) || '';
};
export default getS3Host;
