import CustomConfigProvider from '@src/components/antd/CustomConfigProvider';
import { createRoot } from 'react-dom/client';
import { register } from '../module/request';
import { ReactElement } from 'react';
import 'dayjs/locale/zh-cn';
import { renderWithQiankun } from 'vite-plugin-qiankun/dist/helper';
import initSso from '@src/module/sso';

// 微前端应用名获取
const getMicroAppSuffix = (str: string) => {
    const reg = /\/work\/(.*)/;
    const match = str.match(reg);
    return match ? match[1] : '';
};

// root管理
const rootDom = document.getElementById('root') as HTMLElement;

// 使用此方式时，需要手动调用register
export const root = createRoot(rootDom);

export const render = (el: ReactElement, title?: string) => {
    // 初始化sso-web
    initSso();

    // 网络请求初始化
    register();
    let currentRoot = root;

    const docTitle = document.title;
    if (title) {
        document.title = docTitle ? `${docTitle}-${title}` : title;
    }

    if (window.__XF_Bellwether_MicroProxy__) {
        // 如果是微前端环境， 手动维护子应用映射表
        if (!window.XianFu_Homing_MicroAppMap) {
            window.XianFu_Homing_MicroAppMap = {};
        }
        const curAppName = getMicroAppSuffix(location.pathname);
        if (!window?.XianFu_Homing_MicroAppMap?.[curAppName]) {
            window.XianFu_Homing_MicroAppMap[curAppName] = {
                element: el,
                title: title,
            };
        }
    } else {
        // 非微前端环境，直接render
        currentRoot.render(<CustomConfigProvider>{el}</CustomConfigProvider>);
        return;
    }

    //声明周期钩子注入
    renderWithQiankun({
        mount(props) {
            const { container } = props || {};
            const rootDom = (
                container ? container.querySelector('#root') : document.getElementById('root')
            ) as HTMLElement;
            currentRoot = createRoot(rootDom);
            const renderEl =
                window.__XF_Bellwether_MicroProxy__ && window.XianFu_Homing_MicroAppMap
                    ? window.XianFu_Homing_MicroAppMap[props.name]?.element
                    : el;
            currentRoot.render(<CustomConfigProvider>{renderEl}</CustomConfigProvider>);
        },
        update() {},
        bootstrap() {},
        unmount() {
            currentRoot.unmount();
        },
    });
};
