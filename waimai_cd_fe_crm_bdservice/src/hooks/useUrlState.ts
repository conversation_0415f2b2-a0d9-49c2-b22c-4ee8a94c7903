import { useEffect, useState } from 'react';
import _ from 'lodash';

const useUrlState = (firstLoad = data => {}) => {
    const [urlState, setUrlStateInner] = useState<any>(null);
    useEffect(() => {
        const allParams = getUrlState();
        firstLoad?.(allParams);
        setUrlStateInner(allParams);
    }, []);

    const setUrlState = (newParams: Record<string, string | number>) => {
        const newParamsWithOld = { ...urlState, ...newParams };
        const newUrlParams = new URLSearchParams(_.omitBy(newParamsWithOld, v => !v) as any);
        newUrlParams.toString()
            ? window.history.replaceState({}, '', '?' + newUrlParams.toString())
            : window.history.replaceState(null, '', window.location.href.split('?')[0]);

        setUrlStateInner(newParams);
    };
    return { urlState, setUrlState };
};
export const getUrlState = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const allParams = {};
    for (const [key, value] of urlParams.entries()) {
        allParams[key] = value;
    }
    return allParams as any;
};
export default useUrlState;
