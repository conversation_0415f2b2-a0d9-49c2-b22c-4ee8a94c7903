/**
 * @type import('@mfe/cc-api-caller/bin/yapi/types').Config
 */
module.exports = {
    path: './apiSpec',
    errorCode: {
        FAILED: 1,
    },
    yapi: {
        projects: [
            {
                // tenant
                token: '4e302ae7f13f3fdf5faa6d5bec3eb4268d0079e98f1f547f2d2619862389cfb8',
                categoryList: [
                    {
                        id: 202416, // yapi链接：/interface/api/cat_400480， 取cat_后面的数字
                        name: 'tenant', // yapi的分类的名字
                        alias: 'tenant',
                    },
                ],
            },
            {
                token: 'c0919c26821570b502ba08c3aa32215d079f53315610f10bd76103b4cbb5b575', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 244025, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: '投放模块', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'productGrant',
                    },
                    {
                        id: 244031, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: '任务模块', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'productTask',
                    },
                    {
                        id: 244904, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: 'S3', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'productUpload',
                    },
                ],
            },
            {
                token: '9cf3f35c99df892f375f0aecf692959cf9c5572d32d95912346a779c9e6002dd', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 215968, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: 'pc', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'tt',
                    },
                ],
            },
            {
                // http://yapi.sankuai.com/project/35742/setting
                token: 'a4e1f8a605c814073578b7124c8190b4bdd117e47350518d5be8033da69a2b24', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 223534, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: 'service', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'evaluation',
                    },
                ],
            },
            {
                // http://yapi.sankuai.com/project/35742/setting
                token: '8ab9ad16ac68d880c7736afe159d0dc828d810d41c8368947ba910fe64eb5e74', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 240231, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: '运营工作台', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'assistant',
                    },
                ],
            },
            {
                // http://yapi.sankuai.com/project/35742/setting
                token: '8ab9ad16ac68d880c7736afe159d0dc828d810d41c8368947ba910fe64eb5e74', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 232782, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: '蜜蜂交互', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'chat',
                    },
                ],
            },
            {
                // https://yapi.sankuai.com/project/37154/setting
                token: '996f988ff180760203660de2c113407d4c80082d03e2770bb013ac7781488b69', // yapi api项目必须填写
                categoryList: [
                    {
                        id: 256882, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: '运营工作台-知识库', // yapi的分类的名字, 必须和yapi的分类保持一致
                        alias: 'knowledge',
                    },
                ],
            },
            {
                categoryList: [
                    {
                        id: 498213, // yapi分类id, eg: https://yapi.sankuai.com/project/28051/interface/api/cat_185263 取cat_后面的数字
                        name: 'live_script', // yapi的分类的名字, 必须和yapi的分类保持一致
                    },
                ],
            },
        ],
    },
};
