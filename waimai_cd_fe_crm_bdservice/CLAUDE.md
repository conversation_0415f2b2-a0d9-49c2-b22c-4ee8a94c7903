# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React + TypeScript web application for a CRM BD service system, built with Vite and modern tooling. The project uses Ant Design as the primary UI framework and follows enterprise-grade development practices.

## Development Commands

### Package Management

-   `yarn install` - Install dependencies (uses Yarn with lock file)
-   `yarn install --frozen-lockfile` - Install dependencies for CI/CD

### Build Commands

-   `yarn build` - Build project for production (TypeScript + Vite)
-   `yarn dev` - Start development server with API proxy
-   `yarn preview` - Preview production build locally

### Testing Commands

-   `yarn test` - Run Vitest tests
-   No separate test commands configured

### Code Quality Commands

-   `yarn lint` - Run ESLint for TypeScript/JavaScript files
-   `tsc` - Run TypeScript type checking (no dedicated script)
-   Prettier formatting via lint-staged hooks

### Project-Specific Commands

-   `yarn sync-dts` - Sync TypeScript declarations
-   `yarn parse-yapi` - Parse YAPI specifications
-   `yarn prepare` - Set up Husky git hooks

## Technology Stack

### Core Technologies

-   **TypeScript** - Primary language with strict mode
-   **React 18** - UI library with hooks and functional components
-   **Node.js 18.20.5** - Runtime environment (Volta managed)
-   **Yarn 1.22.22** - Package management

### UI Framework & Styling

-   **Ant Design 5.x** - Primary UI component library
-   **@ant-design/plots** - Chart components
-   **SCSS/Sass** - Styling with CSS modules
-   **CSS-in-JS** - Some styled components

### Build Tools

-   **Vite 5.x** - Fast build tool and development server
-   **SWC** - Fast TypeScript/JavaScript compiler
-   **Qiankun** - Micro-frontend framework

### Testing Framework

-   **Vitest** - Fast unit test framework
-   **@testing-library/react** - React testing utilities
-   **jsdom** - DOM environment for testing

### Code Quality Tools

-   **ESLint** - TypeScript/JavaScript linter with React rules
-   **Prettier** - Code formatter (via lint-staged)
-   **TypeScript 4.9** - Static type checking
-   **Husky** - Git hooks for pre-commit checks
-   **lint-staged** - Staged file linting

### State Management & Data

-   **Zustand** - Lightweight state management
-   **SWR** - Data fetching and caching
-   **Axios** - HTTP client
-   **ahooks** - React utility hooks

### Enterprise/Internal Tools

-   **@roo/roo** - Internal UI framework
-   **@mfe/** packages - Micro-frontend utilities
-   **@mtfe/sso-web** - Single sign-on integration

## Project Structure Guidelines

### File Organization

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components and micro-apps
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
├── module/        # Core modules (SSO, request handling)
├── types/         # TypeScript type definitions
├── constants/     # Application constants
├── assets/        # Static assets (images, fonts, styles)
└── __test__/      # Test setup and utilities
```

### Naming Conventions

-   **Files**: Use kebab-case for directories, PascalCase for React components
-   **Components**: Use PascalCase for component names (`UserProfile`)
-   **Functions**: Use camelCase for function names (`getUserData`)
-   **Constants**: Use UPPER_SNAKE_CASE for constants (`API_BASE_URL`)
-   **Types/Interfaces**: Use PascalCase with descriptive names (`UserData`, `ApiResponse`)

## TypeScript Guidelines

### Type Safety

-   Enable strict mode in `tsconfig.json`
-   Use explicit types for function parameters and return values
-   Prefer interfaces over types for object shapes
-   Use union types for multiple possible values
-   Avoid `any` type - use proper typing with API specifications

### Best Practices

-   Use type guards for runtime type checking
-   Leverage utility types (`Partial`, `Pick`, `Omit`, etc.)
-   Create custom types for domain-specific data
-   Use enums for finite sets of values
-   Document complex types with JSDoc comments

## Code Quality Standards

### ESLint Configuration

-   Use TypeScript ESLint rules
-   Enable React-specific rules
-   Configure import/export rules for consistent module usage
-   Enable unused-imports plugin for cleanup

### Prettier Configuration

-   Automatic formatting via lint-staged
-   Consistent indentation and line length
-   Single quotes for strings
-   Trailing commas for better git diffs

### Testing Standards

-   Use Vitest for unit testing
-   Write tests for utilities and business logic
-   Use Testing Library for component testing
-   Follow AAA pattern (Arrange, Act, Assert)
-   Mock external dependencies appropriately

## Performance Optimization

### Bundle Optimization

-   Use code splitting for large applications
-   Implement lazy loading for routes and components
-   Optimize images and assets
-   Use tree shaking (disabled for @roo packages)
-   Monitor bundle size

### Runtime Performance

-   Implement proper memoization (React.memo, useMemo, useCallback)
-   Use virtualization for large lists
-   Optimize re-renders in React applications
-   Implement proper error boundaries
-   Use web workers for heavy computations

## Security Guidelines

### Dependencies

-   Regularly audit dependencies (npm audit hooks configured)
-   Keep dependencies updated
-   Use lock files (yarn.lock)
-   Avoid dependencies with known vulnerabilities

### Code Security

-   Sanitize user inputs
-   Use HTTPS for API calls
-   Implement proper authentication with SSO
-   Store sensitive data securely (environment variables)
-   Console.log detection prevents logging sensitive data

## Development Workflow

### Before Starting

1. Check Node.js version (18.20.5 via Volta)
2. Install dependencies with `yarn install`
3. Copy environment variables from examples
4. Run type checking with `tsc`

### During Development

1. Use TypeScript for type safety
2. Run linter frequently to catch issues early
3. Write tests for new features
4. Use meaningful commit messages
5. Review code changes before committing

### Before Committing

1. Run tests: `yarn test`
2. Check linting: `yarn lint`
3. Run type checking: `tsc`
4. Test production build: `yarn build`
5. Husky hooks will auto-format and validate staged files

# important-instruction-reminders

Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (\*.md) or README files. Only create documentation files if explicitly requested by the User.
