<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reject Card</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            padding: 16px;
            color: #333333;
            line-height: 1.5;
        }

        .reject-card {
            max-width: 100%;
            border-radius: 8px;
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .content-item {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 12px;
        }

        .content-body {
            font-size: 14px;
            color: #666666;
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .descriptions {
            margin-bottom: 16px;
        }

        .description-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .description-item:last-child {
            border-bottom: none;
        }

        .description-label {
            font-size: 14px;
            color: #666666;
            flex-shrink: 0;
            margin-right: 16px;
        }

        .description-value {
            font-size: 14px;
            color: #333333;
            text-align: right;
            word-break: break-word;
        }

        .button {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .button-primary {
            background-color: #FFD100;
            color: #333333;
        }

        .button-primary:hover {
            background-color: #E6BC00;
        }

        .button-normal {
            background-color: #ffffff;
            color: #333333;
            border: 1px solid #d9d9d9;
        }

        .button-normal:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        .expand-button {
            width: 100%;
            padding: 12px;
            background-color: #f8f9fa;
            border: none;
            border-top: 1px solid #e9ecef;
            color: #666666;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .expand-button:hover {
            background-color: #e9ecef;
        }

        .expand-button::after {
            content: " ▼";
            transition: transform 0.3s ease;
            display: inline-block;
        }

        .expand-button.expanded::after {
            transform: rotate(180deg);
        }

        .hidden-content {
            display: none;
        }

        .markdown-content {
            line-height: 1.6;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3 {
            margin: 16px 0 8px 0;
            color: #333333;
        }

        .markdown-content p {
            margin: 8px 0;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .markdown-content code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
    </style>
</head>
<body>
    <div class="reject-card" id="rejectCard">
        <!-- 内容将通过JavaScript动态生成 -->
    </div>

    <script>
        // 示例数据结构
        const mockData = {
            type: 'rejectCard',
            insert: {
                rejectCard: {
                    content: [
                        {
                            title: "订单审核被拒绝",
                            content: "您的订单因为以下原因被拒绝处理，请查看详细信息并重新提交。\n\n**注意：** 请确保所有信息准确无误。",
                            descriptions: [
                                {
                                    label: "订单号",
                                    value: "MT202401150001"
                                },
                                {
                                    label: "拒绝时间",
                                    value: "2024-01-15 14:30:25"
                                },
                                {
                                    label: "处理人",
                                    value: "系统自动审核"
                                }
                            ],
                            button: {
                                text: "重新提交",
                                url: "https://example.com/resubmit",
                                color: "#FFD100",
                                type: "primary"
                            }
                        },
                        {
                            title: "拒绝原因详情",
                            content: "订单信息不完整，缺少必要的认证文件。",
                            descriptions: [
                                {
                                    label: "错误代码",
                                    value: "E001"
                                },
                                {
                                    label: "建议操作",
                                    value: "补充上传相关证件"
                                }
                            ],
                            button: {
                                text: "联系客服",
                                question: "我想咨询订单被拒绝的问题",
                                action: "submitQuestion",
                                color: "#ffffff",
                                type: "normal"
                            }
                        }
                    ],
                    extendButtonName: "查看更多",
                    showNum: 1
                }
            }
        };

        // Markdown 简单解析函数
        function parseMarkdown(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/\n/g, '<br>');
        }

        // 渲染卡片内容
        function renderRejectCard(data) {
            const container = document.getElementById('rejectCard');
            const rejectCardData = data.insert.rejectCard;
            const { content, extendButtonName, showNum } = rejectCardData;

            let html = '';

            // 渲染内容项
            content.forEach((item, index) => {
                const isHidden = showNum && index >= showNum;
                const hiddenClass = isHidden ? 'hidden-content' : '';

                html += `
                    <div class="content-item ${hiddenClass}" data-index="${index}">
                        <div class="content-title">${item.title}</div>
                        <div class="content-body markdown-content">${parseMarkdown(item.content)}</div>
                        
                        ${item.descriptions && item.descriptions.length > 0 ? `
                            <div class="descriptions">
                                ${item.descriptions.map(desc => `
                                    <div class="description-item">
                                        <span class="description-label">${desc.label}:</span>
                                        <span class="description-value">${desc.value}</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        
                        <div class="button-container">
                            <button class="button button-${item.button.type}" 
                                    style="background-color: ${item.button.color}"
                                    onclick="handleButtonClick('${item.button.url || ''}', '${item.button.question || ''}', '${item.button.action || ''}')">
                                ${item.button.text}
                            </button>
                        </div>
                    </div>
                `;
            });

            // 添加展开按钮
            if (extendButtonName && content.length > showNum) {
                html += `
                    <button class="expand-button" onclick="toggleExpand()">
                        ${extendButtonName}
                    </button>
                `;
            }

            container.innerHTML = html;
        }

        // 按钮点击处理
        function handleButtonClick(url, question, action) {
            if (action === 'submitQuestion' && question) {
                // 处理提问操作
                console.log('提交问题:', question);
                // 这里可以调用父页面的方法或发送消息
                if (window.parent) {
                    window.parent.postMessage({
                        type: 'submitQuestion',
                        question: question
                    }, '*');
                }
            } else if (url) {
                // 处理跳转
                if (window.parent) {
                    window.parent.postMessage({
                        type: 'navigate',
                        url: url
                    }, '*');
                } else {
                    window.open(url, '_blank');
                }
            }
        }

        // 展开/收起功能
        function toggleExpand() {
            const hiddenItems = document.querySelectorAll('.hidden-content');
            const expandButton = document.querySelector('.expand-button');
            const isExpanded = expandButton.classList.contains('expanded');

            hiddenItems.forEach(item => {
                item.style.display = isExpanded ? 'none' : 'block';
            });

            expandButton.classList.toggle('expanded');
            expandButton.textContent = isExpanded ? 
                expandButton.textContent.replace('收起', mockData.insert.rejectCard.extendButtonName) :
                expandButton.textContent.replace(mockData.insert.rejectCard.extendButtonName, '收起');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderRejectCard(mockData);
        });

        // 监听来自父页面的数据
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'rejectCard') {
                renderRejectCard(event.data);
            }
        });
    </script>
</body>
</html>
